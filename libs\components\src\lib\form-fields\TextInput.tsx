import { TextField } from '@mui/material';
import React from 'react';

interface TextInputProps {
  label: string;
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  sx?: object;
}

const TextInput: React.FC<TextInputProps> = ({
  label,
  placeholder,
  value,
  onChange,
  sx,
}) => (
  <TextField
    label={label}
    placeholder={placeholder}
    value={value}
    onChange={(e) => onChange(e.target.value)}
    fullWidth
    InputLabelProps={{ shrink: true }}
    sx={sx}
  />
);

export default TextInput;
