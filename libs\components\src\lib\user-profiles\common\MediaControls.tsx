import React from 'react';
import { Box } from '@mui/material';
import CustomRadioButton from '../../form-fields/ShapeOption';
import ColorPickerBox from '../../form-fields/ColorPickerBox';

const MediaControls = () => (
  <>
    <Box display="flex" alignItems="center" gap="12px">
      <CustomRadioButton label="Square" value="square" />
      <CustomRadioButton label="Circle" value="circle" />
    </Box>

    <Box height="48px" width="1px" bgcolor="#C4C4C4" mx="12px" />

    <ColorPickerBox label="Title Text" color="#000000" />
    <Box height="48px" width="1px" bgcolor="#C4C4C4" mx="12px" />
    <ColorPickerBox label="Body Text" color="#000000" />
  </>
);

export default MediaControls;
