'use client';

import { forwardRef, useState, useEffect, useCallback } from 'react';
import {
  generateSlug,
  enhanceLinkPreview,
} from '@minicardiac-client/utilities';
import { useRouter } from 'next/navigation';
import { Box, Divider, Typography, useTheme } from '@mui/material';
import PostHeader from './PostHeader';
import PostFooterActions from './PostFooterActions';
import { LinkPostProps } from '@minicardiac-client/types';
import { HighlightHtml } from '../common/HighlightHtml';
import { useFeedSearchStore } from '../store/useFeedSearchStore';
import { PostTags } from '../tags/PostTags';
import { LinkPreviewCard } from './link-post/LinkPreviewCard';
import ContentLinkPostDialog from './ContentLinkPostDialog';

export const ContentLinkPost = forwardRef(function ContentLinkPost(
  {
    user = {
      name: 'Anonymous',
      profilePic: '/placeholder-avatar.png',
      postedAgo: 'just now',
    },
    linkUrl = '',
    content = '',
    linkPreview,
    likes = 0,
    comments = 0,
    reposts = 0,
    shares = 0,
    isLiked = false,
    postId,
    tags,
    onLikeChange: _onLikeChange,
    ...eventHandlers
  }: LinkPostProps & React.HTMLAttributes<HTMLDivElement>,
  ref: React.Ref<HTMLDivElement>
) {
  const [openDialog, setOpenDialog] = useState(false);
  const [enhancedLinkPreview, setEnhancedLinkPreview] = useState(linkPreview);
  const theme = useTheme();
  const [showComments, setShowComments] = useState(false);

  const searchKeyword = useFeedSearchStore((state) => state.searchKeyword);
  const router = useRouter();

  // Enhance link preview with real data if needed
  const fetchRealPreview = useCallback(async () => {
    // Only fetch if we have a URL but no meaningful preview data
    if (
      linkUrl &&
      (!linkPreview ||
        !linkPreview.image ||
        linkPreview.description === 'Shared link')
    ) {
      try {
        const realPreview = await enhanceLinkPreview(linkUrl);
        if (realPreview) {
          setEnhancedLinkPreview(realPreview);
        }
      } catch (error) {
        console.error('Failed to fetch link preview:', error);
      }
    }
  }, [linkUrl, linkPreview, setEnhancedLinkPreview]);

  useEffect(() => {
    fetchRealPreview();
  }, [fetchRealPreview]);

  const handleCommentClick = () => {
    setShowComments((prev) => !prev);
  };

  const handlePostClick = () => {
    console.log('Link post clicked', postId);
    console.log(
      'Navigating to',
      `/feed/link-post/${postId}/${generateSlug(content)}`
    );
    // Navigate to link post detail page
    if (postId && content) {
      const slug = generateSlug(content);
      router.push(`/feed/link-post/${postId}/${slug}`);
    }
  };

  const handleExternalLinkClick = () => {
    if (linkUrl) {
      // Ensure the URL has a protocol
      let fullUrl = linkUrl;
      if (!linkUrl.startsWith('http://') && !linkUrl.startsWith('https://')) {
        fullUrl = `https://${linkUrl}`;
      }
      window.open(fullUrl, '_blank', 'noopener,noreferrer');
    }
  };

  return (
    <>
      <Box
        ref={ref}
        {...eventHandlers}
        sx={{
          width: '100%',
          p: { xs: '16px', sm: '20px' },
          borderRadius: '12px',
          backgroundColor: '#fff',
          boxShadow: '0px 1px 6px rgba(0, 0, 0, 0.05)',
          boxSizing: 'border-box',
          cursor: eventHandlers.onMouseDown ? 'grab' : 'default',
        }}
      >
        <Box onClick={handlePostClick} sx={{ cursor: 'pointer' }}>
          <PostHeader user={user} showOptions />
        </Box>

        {/* Caption/Content */}
        {content && (
          <Box sx={{ mt: '16px', cursor: 'pointer' }} onClick={handlePostClick}>
            <Typography
              component="div"
              sx={{
                fontSize: '14px',
                fontWeight: 400,
                color: theme.palette.text.primary,
                lineHeight: '20px',
              }}
            >
              <HighlightHtml html={content} keyword={searchKeyword} />
            </Typography>
          </Box>
        )}

        {/* Link Preview */}
        <Box sx={{ mt: content ? '16px' : '16px' }}>
          <LinkPreviewCard
            linkUrl={linkUrl}
            linkPreview={enhancedLinkPreview}
            onClick={handleExternalLinkClick}
            size="small"
          />
        </Box>

        {/* Tags */}
        <PostTags tags={tags || []} />

        <Divider
          sx={{ mt: '12px', mb: '8px', borderColor: '#A3A3A3', opacity: 0.5 }}
        />

        <PostFooterActions
          likes={likes}
          isLiked={isLiked}
          commentsCount={comments}
          reposts={reposts}
          shares={shares}
          onOpenComments={handleCommentClick}
          showComments={showComments}
          setShowComments={setShowComments}
          postId={postId}
          post={{
            author: {
              name: user.name,
              avatar: user.profilePic,
            },
            content: content,
            images: enhancedLinkPreview?.image
              ? [enhancedLinkPreview.image]
              : [],
          }}
        />
      </Box>

      <ContentLinkPostDialog
        open={openDialog}
        onClose={() => setOpenDialog(false)}
        linkUrl={linkUrl}
        linkPreview={enhancedLinkPreview}
        user={user}
        content={content}
        likes={likes}
        comments={comments}
        reposts={reposts}
        shares={shares}
        isLiked={isLiked}
        postId={postId}
      />
    </>
  );
});

export default ContentLinkPost;
