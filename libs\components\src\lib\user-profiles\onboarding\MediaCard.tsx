import { Box, Typography } from '@mui/material';

export const MediaCard = ({ layout }: { layout: 'left' | 'right' | 'top' }) => {
  const isVertical = layout === 'top';
  return (
    <Box
      display="flex"
      flexDirection={
        isVertical ? 'column' : layout === 'left' ? 'row' : 'row-reverse'
      }
      alignItems={isVertical ? 'center' : 'flex-start'}
      p="8px"
      border="1px solid #A24295"
      borderRadius="12px"
      width={{ xs: '100%', sm: '300px' }}
      maxWidth={{ xs: '100%', sm: '259px' }}
      maxHeight="120px"
      bgcolor="#fff"
    >
      <Box
        width={layout === 'top' ? '100%' : '105px'}
        height={layout === 'top' ? '48px' : '105px'}
        bgcolor="#F6ECF4"
        borderRadius="8px"
        flexShrink={0}
      />
      <Box
        ml={layout === 'left' ? '12px' : 0}
        mr={layout === 'right' ? '12px' : 0}
        mt={layout === 'top' ? '0px' : 0}
        textAlign={'left'}
        overflow="hidden"
      >
        <Typography fontWeight={600} fontSize="12px" mb="4px">
          Title
        </Typography>
        <Typography
          fontSize="12px"
          color="text.secondary"
          fontWeight={400}
          sx={{
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            height: '81px',
          }}
        >
          This is the description of your custom section. Let’s keep the content
          relevant and respectful!
        </Typography>
      </Box>
    </Box>
  );
};
