'use client';

import { useState, useEffect } from 'react';

import {
  Box,
  TextField,
  Typography,
  Menu,
  MenuItem,
  Stack,
  useMediaQuery,
} from '@mui/material';
import { LoadingButton } from '../loading-button';

import CustomDialog from './CustomDialog';
import CustomToggleButtonGroup from '../buttons/CustomToggleButtonGroup';
import { Iconify } from '../iconify';
import { PostButton } from '../buttons/PostButton';
import { toast } from 'react-toastify';
import PostToast from '../toast/PostToast';
import { isContentEmpty } from '@minicardiac-client/utilities';
import { CustomEditorBase } from '../textEditor/CustomEditorBase';
import { BackButton } from '../buttons/Backbutton';
import { useTheme } from '@emotion/react';
import { useTranslations } from 'next-intl';
import { useCreateQuestionPost } from '@minicardiac-client/apis';
import SchedulePostDialog from './ScheduleDialog';
import { getDecodedToken } from '@minicardiac-client/utilities';
import { PostCreationNotification } from '../common/PostCreationNotification';

interface QuestionPostDialogProps {
  open: boolean;
  onClose: () => void;
  setOpenScheduleDialog: () => void;
  content: string;
  setContent: (content: string) => void;
}

const QuestionPostDialog = ({
  open,
  onClose,
  setOpenScheduleDialog,
  content,
  setContent,
}: QuestionPostDialogProps) => {
  const [tags, setTags] = useState('');

  const [audience, setAudience] = useState('PROFESSIONAL');
  const [speciality, setSpeciality] = useState('CARDIAC_SURGEON');
  const [postScheduleDate, setPostScheduleDate] = useState<string | undefined>(undefined);
  const [scheduleDialogOpen, setScheduleDialogOpen] = useState(false);

  // Check if user is PUBLIC account
  const [isPublicUser, setIsPublicUser] = useState(false);

  // Check user account type when dialog opens
  useEffect(() => {
    const checkUserType = async () => {
      try {
        const decodedToken = await getDecodedToken();
        const isPublic = decodedToken?.accountType === 'PUBLIC';
        setIsPublicUser(isPublic);
        
        // Set tags to PublicForum for public users
        if (isPublic) {
          setTags('PublicForum');
        } else {
          // Reset tags for non-public users
          setTags('');
        }
      } catch (error) {
        console.error('Error decoding token:', error);
        setIsPublicUser(false);
      }
    };
    if (open) {
      checkUserType();
    }
  }, [open]);

  const theme: any = useTheme();
  const screenBelowSM = useMediaQuery(theme.breakpoints.down('sm'));
  const t = useTranslations('questionPost');

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const openMenu = Boolean(anchorEl);

  // Handle tags change - prevent editing for PUBLIC users
  const handleTagsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!isPublicUser) {
      setTags(e.target.value);
    }
  };

  const { mutate: createQuestionPost } = useCreateQuestionPost({
    onSuccess: () => {
      onClose();
      setContent('');
      toast(<PostToast value={t('toast.posted')} />, {
        position: 'bottom-right',
        autoClose: 5000,
        hideProgressBar: true,
        closeButton: false,
        style: {
          padding: 0,
          width: 'fit-content',
          background: 'white',
          boxShadow: '0px 2px 10px rgba(0, 0, 0, 0.1)',
        },
      });
    },
    onError: (error) => {
      console.error('Failed to create question post:', error);
      toast.error(error.message || 'Failed to create question post');
    },
  });

  const handleSchedule = async (isoDate: string) => {
    setPostScheduleDate(isoDate);
    setScheduleDialogOpen(false);
    await handlePostWithSchedule(isoDate);
  };

  const handlePostWithSchedule = async (isoDate: string) => {
    if (isContentEmpty(content)) {
      toast.error(t('toast.empty'));
      return;
    }

    const postData = {
      community: audience as 'PROFESSIONAL' | 'PUBLIC' | 'BOTH',
      audience: speciality as 'CARDIAC_SURGEON' | 'CARDIOLOGIST' | 'BOTH',
      tags: tags
        .split(',')
        .map((tag) => tag.trim())
        .filter(Boolean),
      postStatus: 'scheduled' as const,
      postScheduleDate: isoDate,
      content: content,
    };

    createQuestionPost(postData);
  };

  const handlePost = () => {
    if (isContentEmpty(content)) {
      toast.error(t('toast.empty'));
      return;
    }

    const postStatus: 'draft' | 'published' | 'scheduled' = postScheduleDate ? 'scheduled' : 'published';
    
    // For PUBLIC users, ensure tags is 'PublicForum'
    const finalTags = isPublicUser ? 'PublicForum' : tags;
    
    const postData = {
      community: audience as 'PROFESSIONAL' | 'PUBLIC' | 'BOTH',
      audience: speciality as 'CARDIAC_SURGEON' | 'CARDIOLOGIST' | 'BOTH',
      tags: finalTags
        .split(',')
        .map((tag) => tag.trim())
        .filter(Boolean),
      postStatus,
      postScheduleDate,
      content: content,
    };

    createQuestionPost(postData);
  };

  return (
    <CustomDialog
      open={open}
      onClose={onClose}
      title=""
      sx={{
        p: 0,
        px: { xs: 0, sm: '80px' },
        pt: { xs: 0, sm: '50px' },
        alignItems: { xs: 'stretch', sm: 'start' },
        '.MuiDialog-paper': {
          maxHeight: { xs: '100%', sm: 'calc(100% - 64px' },
        },
      }}
    >
      <Box
        display="flex"
        flexDirection="column"
        padding={{ xs: '16px', sm: '40px' }}
        width={'100%'}
        sx={{ backgroundColor: 'white' }}
      >
        {/* Heading */}
        <Box
          display="flex"
          justifyContent="space-between"
          alignItems="center"
          height={'35px'}
        >
          <Box sx={{ display: 'flex', gap: '16px', alignItems: 'center' }}>
            {screenBelowSM && <BackButton onClick={onClose} />}
            <Typography
              sx={{
                fontFamily: 'Plus Jakarta Sans',
                fontWeight: 500,
                fontSize: { xs: '20px', sm: '28px' },
                color: '#1E1E1E',
              }}
            >
              {t('title')}
            </Typography>
          </Box>
          <Typography
            sx={{
              fontFamily: 'Plus Jakarta Sans',
              fontWeight: 600,
              fontSize: '16px',
              color: '#A24295',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
            }}
          >
            {t('drafts')} <Iconify icon="solar:arrow-right-linear" />
          </Typography>
        </Box>

        <CustomEditorBase
          value={content}
          onChange={setContent}
          label={t('editorLabel')}
          placeholder={t('editorPlaceholder')}
        />

        {/* Tags and Toggles */}
        <Box
          display="flex"
          flexDirection={{ xs: 'column', md: 'row', lg: 'row' }}
          gap={{ xs: '20px', lg: '40px' }}
          alignItems={{ xs: 'center', md: 'end' }}
          mt={{ xs: '40px', sm: '20px' }}
          mb={{ xs: '140px', sm: '0px' }}
        >
          {/* Tag field with conditional width for public users */}
          <Box width={isPublicUser ? '240px' : '100%'}>
            <TextField
              placeholder={isPublicUser ? "PublicForum" : t('tagsPlaceholder')}
              value={tags}
              onChange={handleTagsChange}
              fullWidth
              label={t('tagsLabel')}
              InputLabelProps={{ shrink: true }}
              disabled={isPublicUser}
              sx={isPublicUser ? {
                '& .MuiInputBase-input': {
                  color: '#666',
                  cursor: 'not-allowed',
                },
                '& .MuiOutlinedInput-root': {
                  backgroundColor: '#f5f5f5',
                  height: '45px',
                }
              } : {}}
            />
          </Box>
          
          {/* Show notification banner for public users - hidden on mobile */}
          {isPublicUser && (
            <Box sx={{ display: { xs: 'none', sm: 'block' } }}>
              <PostCreationNotification 
                width={800}
              />
            </Box>
          )}
          
          {/* Only show community and audience toggles for non-public users */}
          {!isPublicUser && (
            <>
              <Box width={{ xs: '100%', md: '224px' }}>
                <CustomToggleButtonGroup
                  label={t('community')}
                  options={['PROFESSIONAL', 'PUBLIC', 'BOTH']}
                  selected={audience}
                  onChange={setAudience}
                  width={{ xs: '100%', md: '224px' }}
                />
              </Box>
              <Box width={{ xs: '100%', md: '282px' }}>
                <CustomToggleButtonGroup
                  label={t('audience')}
                  options={['CARDIAC_SURGEON', 'CARDIOLOGIST', 'BOTH']}
                  selected={speciality}
                  onChange={setSpeciality}
                  width={{ xs: '100%', md: '282px' }}
                />
              </Box>
            </>
          )}
        </Box>

        {/* Footer Buttons */}
        <Stack
          direction={{ xs: 'column', sm: 'row' }}
          spacing={{ xs: '10px', sm: '20px' }}
          justifyContent={{ xs: 'center', sm: 'center' }}
          sx={{
            position: { xs: 'fixed', sm: 'static' },
            bottom: 0,
            left: 0,
            mt: { xs: '0px', sm: '40px' },
            width: { xs: '100%', sm: 'auto' },
            background: { xs: 'rgba(255,255,255,0.8)', sm: 'transparent' },
            backdropFilter: { xs: 'blur(20px)', sm: 'none' },
            padding: { xs: '20px', sm: 0 },
            boxShadow: { xs: '0 -4px 20px 0 rgba(0,0,0,0.1)', sm: 'none' },
          }}
        >
          <Box
            sx={{
              display: 'flex',
              width: '100%',
              justifyContent: 'center',
              alignItems: 'center',
              flexDirection: { xs: 'column', sm: 'row' },
              gap: '20px',
            }}
          >
            <LoadingButton
              variant="outlined"
              onClick={onClose}
              sx={{
                width: '156px',
                height: '40px',
                backgroundColor: 'white',
                border: { xs: 'none', sm: '1px solid #A24295' },
                color: '#A24295',
                '&:hover': {
                  backgroundColor: { xs: '#f5f5f5', sm: 'secondary.light' },
                },
                fontSize: '16px',
                fontWeight: 700,
              }}
            >
              {t('cancel')}
            </LoadingButton>

            <PostButton
              setAnchorEl={setAnchorEl}
              handlePost={handlePost}
              disabled={isContentEmpty(content)}
              isOpen={openMenu}
            />
          </Box>
          <Menu
            anchorEl={anchorEl}
            open={openMenu}
            onClose={() => setAnchorEl(null)}
          >
            <MenuItem onClick={() => setScheduleDialogOpen(true)}>
              {t('menu.schedule')}
            </MenuItem>
            <MenuItem onClick={() => setAnchorEl(null)}>
              {t('menu.draft')}
            </MenuItem>
            <MenuItem onClick={() => setAnchorEl(null)}>
              {t('menu.sponsor')}
            </MenuItem>
          </Menu>
        </Stack>
      </Box>
      <SchedulePostDialog
        open={scheduleDialogOpen}
        onClose={() => setScheduleDialogOpen(false)}
        onSchedule={handleSchedule}
      />
    </CustomDialog>
  );
};

export default QuestionPostDialog;
