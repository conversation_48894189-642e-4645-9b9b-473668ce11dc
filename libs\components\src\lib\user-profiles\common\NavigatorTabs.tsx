'use client';

import { Box, Typography } from '@mui/material';

const tabs = ['About', 'Activity', 'Articles', 'Endorsements'];

export default function NavigatorTabs({ activeTab }: { activeTab: string }) {
  return (
    <Box
      sx={{
        overflowX: 'auto',
        whiteSpace: 'nowrap',
        scrollbarWidth: 'none', // Firefox
        '&::-webkit-scrollbar': {
          display: 'none', // Chrome, Safari
        },
      }}
    >
      <Box
        display="inline-flex"
        gap="40px"
        px="16px" // optional padding
      >
        {tabs.map((tab) => {
          const isActive = tab === activeTab;
          return (
            <Box
              key={tab}
              display="flex"
              flexDirection="column"
              alignItems="center"
              flexShrink={0}
            >
              <Typography
                fontSize="16px"
                fontWeight={isActive ? 500 : 400}
                color={isActive ? '#A24295' : '#A3A3A3'}
                sx={{ cursor: 'pointer' }}
              >
                {tab}
              </Typography>
              {isActive && (
                <Box mt="4px" height="3px" width="100%" bgcolor="#A24295" />
              )}
            </Box>
          );
        })}
      </Box>
    </Box>
  );
}
