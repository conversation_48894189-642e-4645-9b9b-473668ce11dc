import { useMemo } from 'react';
import useSWR from 'swr';
import { AxiosRequestConfig } from 'axios';
import { fetcher } from '../http-client.js';
import { IFetchResult } from '@minicardiac-client/types';

export const useGetData = <ParamType, ResultType>({
  endpoint,
  params,
}: {
  endpoint: string | null;
  params?: ParamType;
}): IFetchResult<ResultType> => {
  let URL: string | [string, AxiosRequestConfig] | null;

  if (!endpoint) {
    URL = null;
  } else if (params) {
    URL = [endpoint, { params }];
  } else {
    URL = endpoint;
  }

  const { data, error, isLoading, isValidating } = useSWR<ResultType>(
    URL,
    fetcher
  );

  return useMemo(
    () => ({
      data,
      error,
      isLoading,
      isValidating,
    }),
    [data, error, isLoading, isValidating]
  );
};
