import {
  <PERSON>,
  Typo<PERSON>,
  IconButton,
  Card,
  CardContent,
  CardMedia,
  TextField,
} from '@mui/material';
import { Iconify } from '../iconify';
import { getCdnUrl } from '@minicardiac-client/utilities';
import { HighlightHtml } from '../common/HighlightHtml';
import { useState, useEffect, useRef, useMemo } from 'react';
import type { FeedPostType } from '@minicardiac-client/types';

interface DraftPostCardProps {
  post: FeedPostType & {
    id: string;
    title: string;
    content: string;
    date: string;
    type: string;
    media?: string;
    mediaType?: string;
    postType: string;
    postMedias?: Array<{
      mediaPath: string;
      mediaType: string;
      altText?: string;
    }>;
    coverImagePath?: string;
    linkUrl?: string;
  };
  onEdit: (id: string, newContent: string) => Promise<void>;
  onDelete: (id: string) => void;
  onReschedule: (id: string) => void; // This will be "Publish" for drafts
  isEditing?: boolean;
  onStartEdit?: (id: string) => void;
  onCancelEdit?: () => void;
}

export function DraftPostCard({
  post,
  onEdit,
  onDelete,
  onReschedule,
  isEditing = false,
  onStartEdit,
  onCancelEdit,
}: DraftPostCardProps) {
  const [editContent, setEditContent] = useState(post.content || '');
  const [isSaving, setIsSaving] = useState(false);
  const textFieldRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isEditing && textFieldRef.current) {
      // Find the actual textarea element within the TextField
      const textarea = textFieldRef.current.querySelector('textarea');
      if (textarea) {
        textarea.focus();
        // Position cursor at end for multiline text field
        const length = editContent.length;
        setTimeout(() => {
          textarea.setSelectionRange(length, length);
        }, 0);
      }
    }
  }, [isEditing, editContent]);

  const handleSave = async () => {
    if (!editContent.trim() || editContent === post.content) {
      onCancelEdit?.();
      return;
    }

    try {
      setIsSaving(true);
      await onEdit(post.id, editContent);
    } catch (error) {
      console.error('Error updating draft:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSave();
    } else if (event.key === 'Escape') {
      event.preventDefault();
      setEditContent(post.content || '');
      onCancelEdit?.();
    }
  };

  const handleBlur = () => {
    // Don't auto-save on blur, let user decide
    setEditContent(post.content || '');
    onCancelEdit?.();
  };

  // Format date to a readable format
  const formattedDate = useMemo(() => {
    try {
      const date = post.date ? new Date(post.date) : new Date();
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      });
    } catch (e) {
      return '';
    }
  }, [post.date]);

  // Truncate content if too long
  const truncatedContent = useMemo(() => {
    const maxLength = 200; // Maximum characters to show before truncation
    if (!post.content) return '';
    return post.content.length > maxLength
      ? `${post.content.substring(0, maxLength)}...`
      : post.content;
  }, [post.content]);

  const renderContentPreview = () => {
    // Render editing field or static content
    const contentElement = isEditing ? (
      <TextField
        ref={textFieldRef}
        value={editContent}
        onChange={(e) => setEditContent(e.target.value)}
        onKeyDown={handleKeyDown}
        onBlur={handleBlur}
        multiline
        minRows={1}
        maxRows={4}
        variant="standard"
        fullWidth
        disabled={isSaving}
        sx={{
          '& .MuiInput-underline:before': { display: 'none' },
          '& .MuiInput-underline:after': { display: 'none' },
          '& .MuiInputBase-root': {
            overflow: 'hidden',
            '& textarea': {
              overflow: 'hidden !important',
              resize: 'none',
            },
          },
        }}
      />
    ) : (
      <Box
        onClick={() => onStartEdit?.(post.id)}
        sx={{
          cursor: 'text',
          '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.02)' },
          padding: '2px 4px',
          borderRadius: '4px',
          transition: 'background-color 0.2s',
          display: '-webkit-box',
          WebkitLineClamp: 3,
          WebkitBoxOrient: 'vertical',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          '& *': {
            margin: 0,
            lineHeight: 1.4,
          }
        }}
      >
        <HighlightHtml
          html={truncatedContent || 'No content'}
          keyword=""
        />
      </Box>
    );

    switch (post.postType) {
      case 'media':
        return (
          <Box display="flex" gap={2} alignItems="flex-start">
            {post.postMedias?.[0] && (
              <CardMedia
                component="img"
                sx={{
                  width: 64,
                  height: 64,
                  borderRadius: 1,
                  objectFit: 'cover',
                }}
                image={post.postMedias[0].mediaPath} // Already has CDN URL from transformation
              />
            )}
            <Box sx={{ '& p': { margin: 0 } }}>{contentElement}</Box>
          </Box>
        );
      case 'article':
        return (
          <>
            {post.coverImagePath && (
              <CardMedia
                component="img"
                sx={{
                  width: 64,
                  height: 64,
                  borderRadius: 1,
                  objectFit: 'cover',
                  mb: 1,
                }}
                image={getCdnUrl(post.coverImagePath)}
              />
            )}
            <Typography variant="body2" fontWeight={600}>
              {post.title}
            </Typography>
            <Box sx={{ '& p': { margin: 0 } }}>{contentElement}</Box>
          </>
        );
      case 'link':
        return (
          <Box>
            {contentElement}
            {post.linkUrl && (
              <Typography variant="caption" color="primary" noWrap>
                {post.linkUrl}
              </Typography>
            )}
          </Box>
        );
      case 'poll':
        return (
          <>
            <Typography variant="body2" fontWeight={600}>
              {post.question || post.content}
            </Typography>
            <Typography variant="caption">
              Options:{' '}
              {post.options?.map((o: any, i: number) => (
                <span key={i}>
                  <strong>{i + 1}.</strong> {o.text || o}{' '}
                </span>
              )) || 'No options'}
            </Typography>
          </>
        );
      case 'question':
        return (
          <Typography variant="body2" fontWeight={600}>
            {post.question || post.content}
          </Typography>
        );
      default:
        return contentElement;
    }
  };

  return (
    <Card variant="outlined" sx={{ borderRadius: 1, my: 2, border: 'none' }}>
      <CardContent>
        <Box display="flex" alignItems="center">
          <Box width="75%" pr={2} sx={{ overflow: 'hidden' }}>
            <Typography
              variant="caption"
              color="text.secondary"
              textTransform="capitalize"
            >
              {post.postType}
            </Typography>
            <Typography variant="caption" color="text.secondary" display="block" mb={1}>
              {formattedDate}
            </Typography>
            <Box sx={{
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              maxHeight: '100px',
              '& > *': {
                margin: 0,
                lineHeight: 1.4,
              }
            }}>
              {renderContentPreview()}
            </Box>
          </Box>

          <Box
            width="25%"
            display="flex"
            justifyContent="flex-end"
            alignItems="center"
            gap={1}
          >
            <IconButton size="small" onClick={() => onStartEdit?.(post.id)}>
              <Iconify
                icon="solar:pen-bold"
                width={20}
                height={20}
                color="#A24295"
              />
            </IconButton>
            <IconButton size="small" onClick={() => onDelete(post.id)}>
              <Iconify
                icon="solar:trash-bin-minimalistic-bold"
                width={20}
                height={20}
                color="#A24295"
              />
            </IconButton>
          </Box>
        </Box>

        {/* Date and publish section - similar to schedule section */}

      </CardContent>
    </Card>
  );
}