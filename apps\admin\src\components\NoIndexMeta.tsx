// src/components/NoIndexMeta.jsx
import { useEffect } from 'react';

const NoIndexMeta = () => {
  useEffect(() => {
    // Check if this is not production
    const isProduction =
      (import.meta as any)?.env?.VITE_ENVIRONMENT === 'production' ||
      (import.meta as any)?.env?.NODE_ENV === 'production';

    if (!isProduction) {
      // Add meta tag programmatically
      const metaRobots = document.createElement('meta');
      metaRobots.name = 'robots';
      metaRobots.content = 'noindex, nofollow';
      document.head.appendChild(metaRobots);

      // Clean up on component unmount
      return () => {
        const existingMeta = document.querySelector('meta[name="robots"]');
        if (existingMeta) document.head.removeChild(existingMeta);
      };
    }
  }, []);

  return null;
};

export default NoIndexMeta;
