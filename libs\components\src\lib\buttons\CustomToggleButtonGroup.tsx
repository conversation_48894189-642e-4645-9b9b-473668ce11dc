import {
  ToggleButtonGroup,
  ToggleButton,
  Box,
  Typography,
  useTheme,
} from '@mui/material';

interface CustomToggleButtonGroupProps<T extends string> {
  label?: string;
  options: readonly T[];
  selected: T;
  onChange: (val: T) => void;
  isBasicPlan?: boolean;
  errorMessage?: string;
  width?: any;
}

function CustomToggleButtonGroup<T extends string>({
  label,
  options,
  selected,
  onChange,
  isBasicPlan = false,
  errorMessage,
  width,
}: CustomToggleButtonGroupProps<T>) {
  const theme: any = useTheme();

  const getStyle = (active: boolean) => ({
    border: 'none',
    backgroundColor: active ? '#E3C6DFBF' : 'transparent',
    color: active
      ? theme.palette.neutral?.[600] || theme.palette.secondary.main
      : '#1E1E1E',
    fontFamily: "'Plus Jakarta Sans', sans-serif",
    fontWeight: active ? 700 : 300,
    height: '36px',
    fontSize: '14px',
    textTransform: 'none',
    boxShadow: 'none',
    borderRadius: '8px',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    width: 'fit-content',
    '&:hover': {
      backgroundColor: active ? '#E3C6DFBF' : 'rgba(0, 0, 0, 0.04)',
    },
    '&.Mui-selected': {
      backgroundColor: '#E3C6DFBF',
      color: theme.palette.secondary.main,
      borderRadius: '8px',
    },
    '&.Mui-selected:hover': {
      backgroundColor: '#E3C6DF',
    },
  });

  const labelMap: Record<string, string> = {
    PROFESSIONAL: 'Professional',
    PUBLIC: 'Public',
    CARDIAC_SURGEON: 'Cardiac Surgeon',
    CARDIOLOGIST: 'Cardiologist',
    BOTH: 'Both',
    MEDIA: 'Media',
    TEXT: 'Text Only',
    CARDIAC_SURGERY: 'Cardiac Surgery',
    CARDIOLOGY: 'Cardiology',
  };

  const filteredOptions = isBasicPlan
    ? options.filter((opt) => opt !== 'BOTH')
    : options;

  return (
    <Box
      sx={{
        width: width,
        display: 'flex',
        flexDirection: 'column',
        gap: '4px',
      }}
    >
      {label && (
        <Typography
          sx={{
            fontSize: '16px',
            fontWeight: 500,
            height: '20px',
          }}
        >
          {label}
        </Typography>
      )}

      <Box
        sx={{
          border: selected ? '1px solid #A24295' : '1px solid #A3A3A3',
          borderRadius: '8px',
          p: '4px',
          width: '100%',
          height: '44px',
          display: 'flex',
          alignItems: 'center',
        }}
      >
        <ToggleButtonGroup
          fullWidth
          color="secondary"
          exclusive
          value={selected}
          onChange={(_, newValue) => {
            if (newValue !== null) onChange(newValue);
          }}
          sx={{
            width: '100%',
            display: 'flex',
            justifyContent: 'space-between',
            gap: '4px',
          }}
        >
          {filteredOptions.map((opt) => (
            <ToggleButton
              key={opt}
              value={opt}
              size="small"
              sx={getStyle(selected === opt)}
            >
              {labelMap[opt] || opt}
            </ToggleButton>
          ))}
        </ToggleButtonGroup>
      </Box>
      {errorMessage && (
        <Typography color="error" variant="caption" sx={{ mt: 1 }}>
          {errorMessage}
        </Typography>
      )}
    </Box>
  );
}

export default CustomToggleButtonGroup;
