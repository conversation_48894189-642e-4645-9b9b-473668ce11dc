import {
  Box,
  Card,
  Container,
  Table,
  TableBody,
  TableContainer,
} from '@mui/material';

import {
  CustomBreadCrumbs,
  TableHead,
  TableNoData,
  TablePaginationCustom,
  TableSearch,
  useTable,
} from '@minicardiac-client/components';
import { useCallback, useEffect, useMemo, useState } from 'react';
import UsersTableRow from '../list/pending-verifications-table-row';

import { useGetPendingUsersList } from '@minicardiac-client/apis';

type Filters = {
  searchText: string;
};

const defaultFilters: Filters = {
  searchText: '',
};

type UserRow = {
  name: string;
  email: string;
  user_type: 'Professional' | 'Organization';
  registration_date: string;
  verification_status: 'Pending' | 'Approved' | 'Rejected';
};

export default function PendingVerificationsListView() {
  const TABLE_COLUMNS = useMemo(
    () => [
      { field: 'name', title: 'Name', minWidth: 150 },
      { field: 'email', title: 'Email', minWidth: 200 },
      { field: 'user_type', title: 'User Type', minWidth: 180 }, // Professional/Organization
      { field: 'registration_date', title: 'Registration Date', minWidth: 180 },
      {
        field: 'verification_status',
        title: 'Verification Status',
        minWidth: 180,
      },
      { field: '', title: '' },
    ],
    []
  );

  const data = useGetPendingUsersList();
  console.log(data);

  const mockData = useMemo<UserRow[]>(
    () => [
      {
        name: 'John Doe',
        email: '<EMAIL>',
        user_type: 'Professional',
        registration_date: '2023-02-12',
        verification_status: 'Pending',
      },
      {
        name: 'Jane Smith',
        email: '<EMAIL>',
        user_type: 'Organization',
        registration_date: '2023-05-25',
        verification_status: 'Pending',
      },
      {
        name: 'Alice Johnson',
        email: '<EMAIL>',
        user_type: 'Professional',
        registration_date: '2023-08-14',
        verification_status: 'Pending',
      },
      {
        name: 'Bob Brown',
        email: '<EMAIL>',
        user_type: 'Organization',
        registration_date: '2023-01-30',
        verification_status: 'Pending',
      },
      {
        name: 'Charlie White',
        email: '<EMAIL>',
        user_type: 'Professional',
        registration_date: '2023-04-22',
        verification_status: 'Pending',
      },
      {
        name: 'David Lee',
        email: '<EMAIL>',
        user_type: 'Organization',
        registration_date: '2023-06-10',
        verification_status: 'Pending',
      },
      {
        name: 'Eva Green',
        email: '<EMAIL>',
        user_type: 'Professional',
        registration_date: '2023-07-17',
        verification_status: 'Pending',
      },
      {
        name: 'Frank Harris',
        email: '<EMAIL>',
        user_type: 'Organization',
        registration_date: '2023-09-03',
        verification_status: 'Pending',
      },
      {
        name: 'Grace Kim',
        email: '<EMAIL>',
        user_type: 'Professional',
        registration_date: '2023-10-22',
        verification_status: 'Pending',
      },
      {
        name: 'Henry Adams',
        email: '<EMAIL>',
        user_type: 'Organization',
        registration_date: '2023-11-01',
        verification_status: 'Pending',
      },
    ],
    []
  );

  const [tableData, setTableData] = useState<UserRow[]>([]);
  const [filters, setFilters] = useState<Filters>(defaultFilters);

  useEffect(() => {
    setTableData(mockData);
  }, [mockData]);

  const notFound = !tableData.length;

  const table = useTable({
    defaultRowsPerPage: 50,
    defaultOrderBy: 'name',
    defaultOrder: 'asc',
  });

  const handleFilters = useCallback(
    (name: string, value: string) => {
      table.onResetPage();
      setFilters((prevState) => ({
        ...prevState,
        [name]: value,
      }));
    },
    [table, setFilters]
  );

  return (
    <Container maxWidth={'xl'}>
      <CustomBreadCrumbs
        heading={'Pending Verifications'}
        links={[
          { name: 'Admin', href: '#' },
          { name: 'Pending Verifications' },
        ]}
        sx={{
          mb: { xs: 3, md: 5 },
        }}
      />
      <Card>
        <Box
          display="flex"
          flexDirection={{ xs: 'row', sm: 'row' }}
          alignItems="center"
          // justifyContent="space-between"
          px={{ xs: 1, sm: 2.5 }}
          py={1}
          mb={1}
        >
          <TableSearch
            filters={filters}
            onFilters={handleFilters}
            loading={false}
          />
        </Box>
        <TableContainer sx={{ maxHeight: '70vh', overflow: 'auto' }}>
          <Table size={'medium'} sx={{ minWidth: 960 }}>
            <TableHead
              order={table.order}
              orderBy={table.orderBy}
              headLabel={TABLE_COLUMNS}
              onSort={table.onSort}
            />
            <TableBody>
              {tableData.map((row: UserRow) => (
                <UsersTableRow row={row} headLabel={TABLE_COLUMNS} />
              ))}

              <TableNoData notFound={notFound} />
            </TableBody>
          </Table>
        </TableContainer>
        <TablePaginationCustom
          count={10}
          page={1}
          rowsPerPage={table.rowsPerPage}
          onPageChange={table.onChangePage}
          onRowsPerPageChange={table.onChangeRowsPerPage}
          //
          dense={table.dense}
          onChangeDense={table.onChangeDense}
        />
      </Card>
    </Container>
  );
}
