'use client';

import { Box, Typography } from '@mui/material';
import { Icon } from '@iconify/react';

interface LinkPreviewCardProps {
  linkUrl?: string;
  linkPreview?: {
    title: string;
    description: string;
    image: string;
    domain: string;
  };
  onClick?: () => void;
  size?: 'small' | 'large';
}

export const LinkPreviewCard = ({
  linkUrl,
  linkPreview,
  onClick,
  size = 'small',
}: LinkPreviewCardProps) => {
  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent parent click handlers
    if (onClick) {
      onClick();
    } else if (linkUrl) {
      // Ensure the URL has a protocol
      let fullUrl = linkUrl;
      if (!linkUrl.startsWith('http://') && !linkUrl.startsWith('https://')) {
        fullUrl = `https://${linkUrl}`;
      }
      window.open(fullUrl, '_blank', 'noopener,noreferrer');
    }
  };

  if (!linkPreview && !linkUrl) {
    return null;
  }

  const imageWidth = size === 'large' ? '300px' : '200px';
  const imageHeight = size === 'large' ? '200px' : '140px';

  return (
    <Box
      onClick={handleClick}
      sx={{
        display: 'flex',
        width: '100%',
        border: '1px solid #E0E0E0',
        borderRadius: '8px',
        overflow: 'hidden',
        cursor: 'pointer',
        backgroundColor: '#fff',
        transition: 'all 0.2s ease',
        '&:hover': {
          borderColor: '#A24295',
          transform: 'translateY(-1px)',
          boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)',
        },
      }}
    >
      {/* Image Section */}
      {linkPreview?.image && (
        <Box
          component="img"
          src={linkPreview.image}
          alt={linkPreview.title}
          sx={{
            width: imageWidth,
            height: imageHeight,
            objectFit: 'cover',
            flexShrink: 0,
          }}
        />
      )}

      {/* Content Section */}
      <Box
        sx={{
          flex: 1,
          p: '16px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          minHeight: imageHeight,
        }}
      >
        {/* Domain with icon */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px', mb: '8px' }}>
          <Icon
            icon="mdi:web"
            width={16}
            height={16}
            style={{ color: '#A24295' }}
          />
          <Typography
            sx={{
              fontSize: '12px',
              fontWeight: 500,
              color: '#A24295',
              textTransform: 'lowercase',
            }}
          >
            {linkPreview?.domain || new URL(linkUrl || '').hostname}
          </Typography>
        </Box>

        {/* Title */}
        <Typography
          sx={{
            fontSize: size === 'large' ? '18px' : '14px',
            fontWeight: 600,
            color: '#1E1E1E',
            lineHeight: '1.4',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            mb: '8px',
          }}
        >
          {linkPreview?.title || 'Link Preview'}
        </Typography>

        {/* Description */}
        <Typography
          sx={{
            fontSize: size === 'large' ? '14px' : '12px',
            fontWeight: 400,
            color: '#666',
            lineHeight: '1.5',
            display: '-webkit-box',
            WebkitLineClamp: size === 'large' ? 3 : 2,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          }}
        >
          {linkPreview?.description || 'Preview of the shared link content'}
        </Typography>
      </Box>
    </Box>
  );
};
