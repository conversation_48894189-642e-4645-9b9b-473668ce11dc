import { Routes, Route } from 'react-router-dom';
import PendingVerificationsPage from './pages/admin/pending-verifications';
import UserDetailView from './pages/admin/users/view/user-detail-view';

function FallbackPage() {
  return <h1>Welcome @minicardiac-client/admin</h1>;
}

export function App() {
  return (
    <Routes>
      <Route
        path="/admin/pending-verifications"
        element={<PendingVerificationsPage />}
      />
      <Route
        path="/admin/pending-verifications/:userId"
        element={<UserDetailView />}
      />
      {/* Add more routes here if needed */}
      <Route path="*" element={<FallbackPage />} />
    </Routes>
  );
}
