import { forwardRef, useState } from 'react';
import {
  Box,
  useMediaQuery,
  useTheme,
  Modal,
  Backdrop,
  Fade,
} from '@mui/material';
import PostHeader from '../content-posts/PostHeader';
import PostFooterActions from '../content-posts/PostFooterActions';

import { AnswerComment } from '@minicardiac-client/types';
import { QuestionAndAnswer } from '../question-post/QuestionAndAnswer';
import { FeedPostType } from '@minicardiac-client/types';

const ContentQuestionPost = forwardRef(function ContentQuestionPost(
  {
    postId,
    post,
    ghost = false,
    ...eventHandlers // get everything else
  }: {
    postId?: string;
    post?: FeedPostType;
    ghost?: boolean;
  } & React.HTMLAttributes<HTMLDivElement>,
  ref: React.Ref<HTMLDivElement>
) {
  const [showCommentsDialog, setShowCommentsDialog] = useState(false);
  const [showComments, setShowComments] = useState(false);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const handleCommentClick = () => {
    setShowCommentsDialog(true);
    setShowComments((prev) => !prev);
  };

  const handleCloseComments = () => {
    setShowCommentsDialog(false);
    setShowComments(false);
  };

  const PostContent = (
    <Box
      sx={{
        width: '100%',
        p: { xs: '16px', sm: '20px' },
        borderRadius: '8px',
        backgroundColor: '#fff',
        boxShadow: '0px 1px 6px rgba(0, 0, 0, 0.05)',
        display: 'flex',
        flexDirection: 'column',
        gap: '20px',
        cursor: eventHandlers.onMouseDown ? 'grab' : 'default',
      }}
      ref={ref}
      {...eventHandlers}
    >
      <PostHeader
        user={{
          name: post?.publisherName || '',
          profilePic:
            post?.profileImageUrlThumbnail || '/placeholder-avatar.png',
          postedAgo: post?.postedAt
            ? new Date(post.postedAt).toLocaleDateString()
            : '',
        }}
        showOptions
      />
      {/* Main question and answer  */}
      <QuestionAndAnswer
        pinnedAnswer={post?.featuredComment as AnswerComment | null}
        content={post?.content ?? ''}
        postId={post?.id ?? ''}
        tags={post?.tags}
      />

      <PostFooterActions
        likes={post?.likesCount ?? 0}
        isLiked={post?.isLiked ?? false}
        commentsCount={post?.commentsCount ?? 0}
        reposts={post?.repostCount ?? 0}
        shares={post?.shareCount ?? 0}
        allowPin
        onOpenComments={handleCommentClick}
        showComments={showComments}
        setShowComments={setShowComments}
        postId={post?.id}
      />
    </Box>
  );

  return (
    <>
      {(!showCommentsDialog || !isMobile) && PostContent}

      {showCommentsDialog && isMobile && (
        <Modal
          open={showCommentsDialog}
          onClose={handleCloseComments}
          closeAfterTransition
          BackdropComponent={Backdrop}
          BackdropProps={{
            timeout: 300,
            sx: { backgroundColor: 'rgba(30, 30, 30, 0.25)' },
          }}
        >
          <Fade in={showCommentsDialog}>
            <Box
              sx={{
                position: 'fixed',
                top: '90px',
                left: 0,
                right: 0,
                bottom: 0,
                bgcolor: '#fff',
                borderTopLeftRadius: '40px',
                borderTopRightRadius: '40px',
                boxShadow: '0px -4px 20px rgba(0, 0, 0, 0.15)',
                display: 'flex',
                flexDirection: 'column',
                p: '16px',
                overflowY: 'auto',
              }}
            >
              {PostContent}
            </Box>
          </Fade>
        </Modal>
      )}
    </>
  );
});

export default ContentQuestionPost;
