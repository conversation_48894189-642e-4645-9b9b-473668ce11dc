import { Dispatch, SetStateAction } from 'react';

export type AudienceType = 'PROFESSIONAL' | 'PUBLIC' | 'BOTH';
export type SpecialityType = 'CARDIAC_SURGEON' | 'CARDIOLOGIST' | 'BOTH';

export interface PollPostDetailsDialogProps {
  caption: string;
  setCaption: (value: string) => void;
  tags: string;
  setTags: (value: string) => void;
  audience: AudienceType;
  setAudience: (value: AudienceType) => void;
  speciality: SpecialityType;
  setSpeciality: (value: SpecialityType) => void;
  duration: number;
  setDuration: (value: number) => void;
  isPublicUser?: boolean;
}

export interface PollOption {
  id: string | number;
  label: string;
  placeholder: string;
  value: string;
}

export interface PollOptionInputProps {
  option: PollOption;
  onAdd: (id: string | number, value: string) => void;
  onDelete: (id: string | number) => void;
  canDelete: boolean;
}

export interface PollPostFormProps {
  pollQuestion: string;
  setPollQuestion: (value: string) => void;
  allowCustomAnswers: boolean;
  setAllowCustomAnswers: (value: boolean) => void;
  options: PollOption[];
  setOptions: Dispatch<
    SetStateAction<
      {
        id: number;
        label: string;
        value: string;
        placeholder: string;
      }[]
    >
  >;
}
