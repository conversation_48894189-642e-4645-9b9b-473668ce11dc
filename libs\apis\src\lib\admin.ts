import { User, UserFilters } from '@minicardiac-client/types';
import { IFetchResult } from '@minicardiac-client/types';

import { axiosInstance } from './http-client.js';
import { useGetData } from './hooks/index.js';

export function useGetPendingUsersList(
  params?: UserFilters
): IFetchResult<User> {
  return useGetData({
    endpoint: '/v1/users',
    params,
  });
}

export function useGetUserDetails(params?: UserFilters): IFetchResult<User> {
  return useGetData({
    endpoint: params?.userId ? `/v1/users/${params?.userId}` : null,
    params,
  });
}

export async function updateUserStatus(userId: string, status: string) {
  const URL = `v1/users/${userId}`;
  const res = await axiosInstance.post(URL, status);
  return res;
}
