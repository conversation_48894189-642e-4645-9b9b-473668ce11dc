import React from 'react';
import { Box, Typography, Button } from '@mui/material';

interface FundraiserCardSelectorProps {
  selectedIndex: number;
  onSelect: (index: number) => void;
}

const buttonStyle = {
  width: '70px',
  height: '20px',
  borderRadius: '8px',
  backgroundColor: '#F5D6EB',
  color: '#A24295',
  fontWeight: 600,
  fontSize: '12px',
  textTransform: 'none',
};

const boxStyle = (selected: boolean) => ({
  padding: '10px',
  flex: 1,
  borderRadius: '12px',
  border: selected ? '3px solid #A24295' : '0.5px solid #A24295',
  cursor: 'pointer',
  boxSizing: 'border-box',
  height: '80px',
});

const FundraiserCardSelector: React.FC<FundraiserCardSelectorProps> = ({
  selectedIndex,
  onSelect,
}) => {
  return (
    <Box
      display="flex"
      gap={{ xs: '20px', md: '16px' }}
      flexDirection={{ xs: 'column', md: 'row' }}
    >
      {/* Version 1 */}
      <Box
        sx={{
          ...boxStyle(selectedIndex === 0),
          backgroundColor: '#F6ECF4',
          minHeight: { xs: '80px', md: '120px' },
        }}
        onClick={() => onSelect(0)}
      >
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          gap="8px"
        >
          <Typography fontSize="12px">
            This is the description of your cause
          </Typography>
          <Button sx={buttonStyle}>Button</Button>
        </Box>
      </Box>

      {/* Version 2 */}
      <Box
        sx={{
          ...boxStyle(selectedIndex === 1),
          backgroundColor: '#FFFFFF',
          minHeight: { xs: '80px', md: '120px' },
          padding: '6px',
        }}
        onClick={() => onSelect(1)}
      >
        <Box display="flex" gap="16px" alignItems="center">
          <Box
            width="97px"
            height="65px"
            borderRadius="8px"
            bgcolor="#E0E0E0"
          />
          <Box
            display="flex"
            flexDirection="column"
            alignItems="flex-start"
            gap="8px"
          >
            <Typography fontSize="12px">
              This is the description of your cause
            </Typography>
            <Button sx={buttonStyle}>Button</Button>
          </Box>
        </Box>
      </Box>

      {/* Version 3 */}
      <Box
        sx={{
          ...boxStyle(selectedIndex === 2),
          background: 'linear-gradient(to right, #DFBEDA, #E3C6DF, #F6ECF4)',
          minHeight: { xs: '80px', md: '120px' },
        }}
        onClick={() => onSelect(2)}
      >
        <Box
          display="flex"
          flexDirection="column"
          alignItems="flex-start"
          gap="8px"
        >
          <Typography fontSize="12px">
            This is the description of your cause
          </Typography>
          <Button sx={buttonStyle}>Button</Button>
        </Box>
      </Box>
    </Box>
  );
};

export default FundraiserCardSelector;
