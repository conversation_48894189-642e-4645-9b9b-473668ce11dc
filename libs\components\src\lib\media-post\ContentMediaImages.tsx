import { useTheme } from '@emotion/react';
import { Box, useMediaQuery } from '@mui/material';
import { useState, TouchEvent } from 'react';
import { SUPPORTED_VIDEO_EXTENSIONS } from '@minicardiac-client/shared';

interface ContentMediaImagesProps {
  media: string[];
  setOpenDialog: (value: boolean) => void;
}

export const ContentMediaImages = ({
  media,
  setOpenDialog,
}: ContentMediaImagesProps) => {
  const theme: any = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));
  const [currentIndex, setCurrentIndex] = useState(0);

  let touchStartX = 0;
  let touchEndX = 0;

  const handleTouchStart = (e: TouchEvent) => {
    touchStartX = e.touches[0].clientX;
  };

  const handleTouchEnd = (e: TouchEvent) => {
    touchEndX = e.changedTouches[0].clientX;
    handleSwipe();
  };

  const handleSwipe = () => {
    const swipeThreshold = 50; // px
    if (touchStartX - touchEndX > swipeThreshold) {
      // Swipe Left → Next
      setCurrentIndex((prev) => (prev + 1) % media.length);
    } else if (touchEndX - touchStartX > swipeThreshold) {
      // Swipe Right → Prev
      setCurrentIndex((prev) => (prev - 1 + media.length) % media.length);
    }
  };

  const getFileExtension = (url: string): string => {
    if (!url) return '';
    try {
      const pathname = new URL(url, window.location.origin).pathname;
      return pathname.split('.').pop()?.toLowerCase() || '';
    } catch (e) {
      return (
        url.split('?')[0].split('#')[0].split('.').pop()?.toLowerCase() || ''
      );
    }
  };

  // Helper function to determine media type
  const renderMedia = (url: string, idx: number, props: any = {}) => {
    const ext = getFileExtension(url);
    const videoFormats = new Set(SUPPORTED_VIDEO_EXTENSIONS);
    const audioFormats = new Set(['mp3', 'wav', 'aac', 'm4a']);

    // Constants for media dimensions
    const LARGE_MEDIA_WIDTH = '679px';
    const LARGE_MEDIA_HEIGHT = '360px';
    const SMALL_MEDIA_WIDTH = '194px';
    const SMALL_MEDIA_HEIGHT = '184px';

    // Get dimensions based on media count and screen size
    const getMediaDimensions = () => {
      if (isSmallScreen) {
        return {
          width: '100%',
          height: '312px',
        };
      }

      // For large screens
      if (media.length === 1) {
        // Single image: 679px x 360px
        return {
          width: LARGE_MEDIA_WIDTH,
          height: LARGE_MEDIA_HEIGHT,
        };
      } else if (media.length === 2) {
        // Two images: equal width, side by side (approximately 329px each with 20px gap)
        return {
          width: `calc((${LARGE_MEDIA_WIDTH} - 20px) / 2)`,
          height: LARGE_MEDIA_HEIGHT,
        };
      } else {
        // Three or more images: smaller size for horizontal scroll
        return {
          width: SMALL_MEDIA_WIDTH,
          height: SMALL_MEDIA_HEIGHT,
        };
      }
    };

    const dimensions = getMediaDimensions();

    if (videoFormats.has(ext as any)) {
      return (
        <Box
          key={idx}
          width={dimensions.width}
          height={dimensions.height}
          display="flex"
          alignItems="center"
          justifyContent="center"
          sx={{
            flexShrink: media.length > 2 ? 0 : undefined,
          }}
        >
          <video
            src={url}
            controls
            style={{
              width: '100%',
              height: '100%',
              borderRadius: 8,
              background: '#000',
              objectFit: 'cover',
            }}
            onClick={() => setOpenDialog(true)}
            {...props}
          />
        </Box>
      );
    }

    if (audioFormats.has(ext)) {
      const audioWidth = isSmallScreen
        ? '100%'
        : media.length === 1
        ? LARGE_MEDIA_WIDTH
        : media.length === 2
        ? `calc((${LARGE_MEDIA_WIDTH} - 20px) / 2)`
        : SMALL_MEDIA_WIDTH;

      return (
        <Box
          key={idx}
          width={audioWidth}
          height="60px"
          display="flex"
          alignItems="center"
          justifyContent="center"
          sx={{
            flexShrink: media.length > 2 ? 0 : undefined,
          }}
        >
          <audio
            src={url}
            controls
            style={{ width: '100%' }}
            onClick={() => setOpenDialog(true)}
            {...props}
          />
        </Box>
      );
    }

    // Default to image
    return (
      <Box
        key={idx}
        component="img"
        src={url}
        alt={`media-${idx}`}
        width={dimensions.width}
        height={dimensions.height}
        sx={{
          borderRadius: '8px',
          objectFit: 'cover',
          cursor: 'pointer',
          flexShrink: media.length > 2 ? 0 : undefined,
          maxWidth: '100%',
        }}
        onClick={() => setOpenDialog(true)}
        {...props}
      />
    );
  };

  if (isSmallScreen) {
    return (
      <Box mt="20px" position="relative">
        {renderMedia(media[currentIndex], currentIndex, {
          onTouchStart: handleTouchStart,
          onTouchEnd: handleTouchEnd,
        })}

        {/* Top-right 1/4 indicator */}
        {media.length > 1 && (
          <Box
            position="absolute"
            top="8px"
            right="12px"
            px="8px"
            py="2px"
            borderRadius="12px"
            bgcolor="rgba(0,0,0,0.5)"
            color="#fff"
            fontSize="12px"
          >
            {currentIndex + 1}/{media.length}
          </Box>
        )}

        {/* Dots */}
        {media.length > 1 && (
          <Box display="flex" justifyContent="center" mt="8px" gap="6px">
            {media.map((_, index) => (
              <Box
                key={index}
                width="6px"
                height="6px"
                borderRadius="50%"
                bgcolor={index === currentIndex ? '#A24295' : '#A3A3A3'}
              />
            ))}
          </Box>
        )}
      </Box>
    );
  }

  // Large screens
  return (
    <Box
      mt="20px"
      sx={{
        display: 'flex',
        overflowX: media.length > 2 ? 'auto' : 'hidden',
        justifyContent: media.length <= 2 ? 'center' : 'flex-start',
        gap: media.length === 1 ? '0px' : '20px',
        scrollbarWidth: 'none',
        '&::-webkit-scrollbar': { display: 'none' },
        alignItems: 'center',
        width: media.length <= 2 ? '679px' : 'auto', // Fixed width container for 1-2 images
        maxWidth: '100%', // Responsive on smaller screens
        margin: '0 auto', // Center the container
      }}
    >
      {media.map((url, index) => renderMedia(url, index))}
    </Box>
  );
};
