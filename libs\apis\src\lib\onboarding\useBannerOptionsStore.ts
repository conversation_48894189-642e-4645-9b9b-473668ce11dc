import { BannerOptions } from '@minicardiac-client/types';
import { create } from 'zustand';

interface BannerOptionsState {
  bannerOptions: BannerOptions;
  setBannerOptions: (options: Partial<BannerOptions>) => void;
  resetBannerOptions: () => void;
}

const defaultOptions: BannerOptions = {
  blurBackground: false,
  addBackgroundTint: false,
  tintColor: '#AE7DA7',
  textColor: '#FFFFFF',
};

export const useBannerOptions = create<BannerOptionsState>((set) => ({
  bannerOptions: defaultOptions,

  setBannerOptions: (options) =>
    set((state) => ({
      bannerOptions: { ...state.bannerOptions, ...options },
    })),

  resetBannerOptions: () => set({ bannerOptions: defaultOptions }),
}));
