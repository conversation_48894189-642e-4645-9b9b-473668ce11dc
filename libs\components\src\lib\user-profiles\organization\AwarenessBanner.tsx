import { Box, Button, Typography } from '@mui/material';

export const AwarenessBanner = () => {
  return (
    <Box
      mt="40px"
      px={{ xs: '16px', sm: 0 }}
      display="flex"
      justifyContent="center"
    >
      <Box
        width="100%"
        maxWidth="976px"
        textAlign="center"
        bgcolor="#AE7DA799"
        py="40px"
        borderRadius="8px"
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Typography
          fontSize="20px"
          fontWeight={700}
          lineHeight="22px"
          mb="20px"
          color="white"
          width={'205px'}
        >
          Let's make everyone aware about cancer
        </Typography>

        <Button
          variant="outlined"
          sx={{
            borderRadius: '8px',
            backgroundColor: '#FFFFFF',
            color: '#A24295',
            border: 'none',
            fontWeight: 700,
            fontSize: '16px',
            textTransform: 'none',
            px: '20px',
            py: '8px',
            '&:hover': {
              backgroundColor: '#F3E6F1',
            },
          }}
        >
          Donate Now
        </Button>
      </Box>
    </Box>
  );
};
