import { create } from 'zustand';

interface PostDialogState {
  activeDialog: string | null;
  previousDialog: string | null;

  setActiveDialog: (dialog: PostDialogState['activeDialog']) => void;
  setPreviousDialog: (dialog: PostDialogState['previousDialog']) => void;
  resetDialogs: () => void;
}

export const usePostDialogStore = create<PostDialogState>((set) => ({
  activeDialog: null,
  previousDialog: null,

  setActiveDialog: (dialog) => set({ activeDialog: dialog }),
  setPreviousDialog: (dialog) => set({ previousDialog: dialog }),
  resetDialogs: () => set({ activeDialog: null, previousDialog: null }),
}));
