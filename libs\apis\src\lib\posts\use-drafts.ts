import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '../networking/api-client.js';
import type { FeedPostType } from '@minicardiac-client/types';

interface DraftsResponse {
  status: number;
  message: string;
  data: FeedPostType[];
  stack: string | null;
}

export function useDrafts() {
  return useQuery<FeedPostType[]>({
    queryKey: ['drafts'],
    queryFn: async () => {
      // Using the drafts endpoint to fetch draft posts
      const response = await apiClient.get<DraftsResponse>('/posts/drafts', {
        params: {
          postTypes: ['text', 'media', 'article'], // Include all post types that can be drafts
          limit: 100, // Set a reasonable limit for drafts
          offset: 0,
        },
      });
      return response.data.data || [];
    },
  });
}

// Update draft post hook
export function useUpdateDraft() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      postId,
      postType,
      payload,
    }: {
      postId: string;
      postType: string;
      payload: any;
    }) => {
      const response = await apiClient.patch(
        `/posts/${postType}/${postId}`,
        payload
      );
      console.log('>>>> Update draft response:', response.data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['drafts'] });
    },
  });
}

// Delete draft post hook
export function useDeleteDraft() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (postId: string) => {
      const response = await apiClient.delete(`/posts/${postId}`);

      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['drafts'] });
    },
  });
}
