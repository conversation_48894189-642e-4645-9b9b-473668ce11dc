import { getRequestConfig } from 'next-intl/server';
import { routing } from './routing';

export default getRequestConfig(async ({ requestLocale }) => {
  let locale = await requestLocale;

  if (
    !locale ||
    !routing.locales.includes(locale as (typeof routing.locales)[number])
  ) {
    locale = routing.defaultLocale;
  }

  let messages;

  try {
    messages = (await import(`../../messages/${locale}.json`)).default;
  } catch {
    if (locale !== routing.defaultLocale) {
      try {
        messages = (
          await import(`../../messages/${routing.defaultLocale}.json`)
        ).default;
        locale = routing.defaultLocale;
      } catch {
        throw new Error('Failed to load both primary and fallback messages.');
      }
    } else {
      throw new Error('Failed to load default locale messages.');
    }
  }

  return {
    locale,
    messages,
  };
});
