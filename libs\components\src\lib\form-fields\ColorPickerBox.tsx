import React from 'react';
import { Box, Typography } from '@mui/material';

interface ColorPickerBoxProps {
  label: string;
  color: string;
  onChange?: (value: string) => void;
}

const ColorPickerBox: React.FC<ColorPickerBoxProps> = ({
  label,
  color,
  onChange,
}) => {
  const handleColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange?.(e.target.value);
  };

  return (
    <Box display="flex" alignItems="center" gap="12px">
      <Typography fontWeight={400} fontSize="16px" noWrap>
        {label}
      </Typography>

      <Box
        display="flex"
        alignItems="center"
        bgcolor="#fff"
        borderRadius="12px"
        p="8px"
        component="label"
        sx={{ cursor: 'pointer' }}
      >
        <Box
          width="23px"
          height="23px"
          bgcolor={color}
          borderRadius="4px"
          flexShrink={0}
        />

        <Typography ml="12px" fontSize="14px" color="#000000">
          {color}
        </Typography>

        {/* Hidden color input */}
        <input
          type="color"
          value={color}
          onChange={handleColorChange}
          style={{
            opacity: 0,
            width: 0,
            height: 0,
            position: 'absolute',
          }}
        />
      </Box>
    </Box>
  );
};

export default ColorPickerBox;
