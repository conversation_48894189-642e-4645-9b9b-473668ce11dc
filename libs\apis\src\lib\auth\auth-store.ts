import { create } from 'zustand';
import { persist } from 'zustand/middleware';

/**
 * Interface for the auth UI state store
 */
interface AuthUIState {
  // UI states
  isSigningUp: boolean;
  isSigningIn: boolean;
  isProcessing: boolean;
  selectedUserType: string | null;
  selectedProfessionalType: string | null;

  // Actions
  setSigningUp: (status: boolean) => void;
  setSigningIn: (status: boolean) => void;
  setProcessing: (status: boolean) => void;
  setSelectedUserType: (type: string | null) => void;
  setSelectedProfessionalType: (type: string | null) => void;

  // Reset state
  resetState: () => void;

  // Navigation helpers
  startNavigation: (navigationState: {
    isProcessing?: boolean;
    isSigningUp?: boolean;
    isSigningIn?: boolean;
  }) => void;
}

/**
 * Zustand store for auth UI state
 * Uses persist middleware to save state to localStorage
 */
export const useAuthStore = create<AuthUIState>()(
  persist(
    (set) => ({
      // Initial state
      isSigningUp: false,
      isSigningIn: false,
      isProcessing: false,
      selectedUserType: null,
      selectedProfessionalType: null,

      // Actions
      setSigningUp: (status: boolean) => set({ isSigningUp: status }),
      setSigningIn: (status: boolean) => set({ isSigningIn: status }),
      setProcessing: (status: boolean) => set({ isProcessing: status }),
      setSelectedUserType: (type: string | null) =>
        set({ selectedUserType: type }),
      setSelectedProfessionalType: (type: string | null) =>
        set({ selectedProfessionalType: type }),

      // Navigation helpers
      startNavigation: (navigationState) =>
        set({
          ...(navigationState.isProcessing !== undefined && {
            isProcessing: navigationState.isProcessing,
          }),
          ...(navigationState.isSigningUp !== undefined && {
            isSigningUp: navigationState.isSigningUp,
          }),
          ...(navigationState.isSigningIn !== undefined && {
            isSigningIn: navigationState.isSigningIn,
          }),
        }),

      // Reset state
      resetState: () =>
        set({
          isSigningUp: false,
          isSigningIn: false,
          isProcessing: false,
          selectedUserType: null,
          selectedProfessionalType: null,
        }),
    }),
    {
      name: 'auth-ui-storage',
    }
  )
);
