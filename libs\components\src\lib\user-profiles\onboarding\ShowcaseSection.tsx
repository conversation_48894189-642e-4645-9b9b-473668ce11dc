import { useState } from 'react';
import { Box, Typography } from '@mui/material';
import TextInput from '../../form-fields/TextInput';
import LargeTextInput from '../../form-fields/LargeTextInput';
import ImageUploader from '../../form-fields/ImageUploader';
import DeleteIcon from '../../Icons/DeleteIcon';

type ShowcaseFormItemKey = 'title' | 'description' | 'thumbnail';
type ShowcaseFormItemValue = string | File | null;

const ShowcaseSection = () => {
  const [formItems, setFormItems] = useState([
    { title: '', description: '', thumbnail: null as File | null },
  ]);

  const handleInputChange = (
    index: number,
    key: ShowcaseFormItemKey,
    value: ShowcaseFormItemValue
  ) => {
    const updatedForms = [...formItems];
    updatedForms[index] = { ...updatedForms[index], [key]: value };
    setFormItems(updatedForms);
  };

  const canAddMore = () => {
    const last = formItems[formItems.length - 1];
    return last.title && last.description && last.thumbnail;
  };

  const addNewForm = () => {
    if (!canAddMore()) return;
    setFormItems([
      ...formItems,
      { title: '', description: '', thumbnail: null as File | null },
    ]);
  };

  return (
    <Box
      maxWidth="976px"
      width="100%"
      mx="auto"
      p={{ xs: '16px', sm: '20px' }}
      borderRadius="8px"
      bgcolor="#fff"
    >
      {/* Section Title */}
      <Typography fontSize="24px" fontWeight={600} mb="20px">
        Showcase
      </Typography>

      {formItems.map((form, index) => (
        <Box
          key={index}
          display="flex"
          gap="20px"
          mb="30px"
          flexDirection={{ xs: 'column', md: 'row' }}
        >
          {/* Left Column - Media Upload */}
          <Box display="flex" gap="8px">
            <DeleteIcon fill="#A3A3A3" />
            <ImageUploader
              thumbnail={form.thumbnail}
              onFileChange={(e) =>
                handleInputChange(
                  index,
                  'thumbnail',
                  e.target.files?.[0] || null
                )
              }
              label="Click to add photo/video"
              subtitles="Or drag and drop"
              sx={{
                maxWidth: '381px',
                width: '381px',
                maxHeight: '280px',
                height: '280px',
              }}
            />
          </Box>

          {/* Right Column - Text Input */}
          <Box
            flex={1}
            display="flex"
            flexDirection="column"
            justifyContent="space-between"
          >
            <Box display={'flex'} flexDirection="column" gap="20px">
              <TextInput
                label="Title"
                placeholder="Highlight your skills/ expertise here"
                value={form.title}
                onChange={(val) => handleInputChange(index, 'title', val)}
              />

              <LargeTextInput
                label="Description"
                placeholder="A short description of your skill/ expertise"
                value={form.description}
                onChange={(val) => handleInputChange(index, 'description', val)}
              />
            </Box>
          </Box>
        </Box>
      ))}

      {/* Add Item */}
      <Typography
        fontSize="14px"
        fontWeight={500}
        color={canAddMore() ? '#A24295' : '#BDBDBD'}
        mt="20px"
        sx={{
          cursor: canAddMore() ? 'pointer' : 'not-allowed',
          userSelect: 'none',
        }}
        onClick={addNewForm}
      >
        + Add Item
      </Typography>
    </Box>
  );
};

export default ShowcaseSection;
