import React from 'react';
import CustomCheckbox from '../../form-fields/CustomCheckbox';
import ColorPickerBox from '../../form-fields/ColorPickerBox';
import { BannerOptions, LayoutType } from '@minicardiac-client/types';
import { useBannerOptions } from '@minicardiac-client/apis';

interface BannerControlsProps {
  layoutType?: LayoutType;
}

const BannerControls: React.FC<BannerControlsProps> = ({ layoutType = 0 }) => {
  const { bannerOptions, setBannerOptions } = useBannerOptions();

  const handleCheckboxChange =
    (field: keyof BannerOptions) =>
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setBannerOptions({
        [field]: event.target.checked,
      });
    };

  const handleColorChange = (field: keyof BannerOptions) => (value: string) => {
    setBannerOptions({
      [field]: value,
    });
  };

  return (
    <>
      {layoutType === 2 ? (
        <ColorPickerBox
          label="Gradient Color"
          color={bannerOptions.tintColor}
          onChange={handleColorChange('tintColor')}
        />
      ) : (
        <>
          <CustomCheckbox
            label="Blur Background"
            checked={bannerOptions.blurBackground}
            onChange={handleCheckboxChange('blurBackground')}
          />
          <CustomCheckbox
            label="Add Background Tint"
            checked={bannerOptions.addBackgroundTint}
            onChange={handleCheckboxChange('addBackgroundTint')}
          />
          <ColorPickerBox
            label="Tint Color"
            color={bannerOptions.tintColor}
            onChange={handleColorChange('tintColor')}
          />
        </>
      )}

      <ColorPickerBox
        label="Text Color"
        color={bannerOptions.textColor}
        onChange={handleColorChange('textColor')}
      />
    </>
  );
};

export default BannerControls;
