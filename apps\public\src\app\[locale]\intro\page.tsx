'use client';

import { IntroPage } from '@minicardiac-client/components';
import { useRouter } from '@/apps/public/src/i18n/navigation';
import { useEffect, useState } from 'react';
import { useLoading } from '../../../providers/loading-provider';

export default function IntroPageWrapper() {
  const router = useRouter();
  const [shouldRenderIntro, setShouldRenderIntro] = useState<boolean | null>(
    null
  );
  const { setHasSeenIntro } = useLoading();

  useEffect(() => {
    const handleResize = () => {
      const screenWidth = window.innerWidth;
      if (screenWidth >= 600) {
        router.replace('/signin');
      } else {
        setShouldRenderIntro(true);
        setHasSeenIntro(true);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);

    return () => window.removeEventListener('resize', handleResize);
  }, [router, setHasSeenIntro]);

  if (shouldRenderIntro === null) return null;
  return <IntroPage />;
}
