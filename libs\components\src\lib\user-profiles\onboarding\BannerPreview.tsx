import { useBannerOptions } from '@minicardiac-client/apis';
import { LayoutType } from '@minicardiac-client/types';
import { Box, Button, Typography } from '@mui/material';

interface BannerProps {
  image: File;
  description: string;
  buttonText: string;
  layoutType?: LayoutType;
}

export const BannerPreview = ({
  image,
  description,
  buttonText,
  layoutType = 0,
}: BannerProps) => {
  const { bannerOptions } = useBannerOptions();

  const layoutMap: Record<0 | 1 | 2, 'center' | 'side-by-side' | 'left'> = {
    0: 'center',
    1: 'side-by-side',
    2: 'left',
  };

  const resolvedLayout = layoutMap[layoutType as 0 | 1 | 2];

  const backgroundStyle =
    resolvedLayout === 'left'
      ? `linear-gradient(to right, ${bannerOptions.tintColor}, white)`
      : resolvedLayout === 'center' && bannerOptions.addBackgroundTint
      ? bannerOptions.tintColor
      : 'transparent';

  return (
    <Box
      position="relative"
      border="1px solid #A24295"
      borderRadius="12px"
      textAlign="center"
      height="240px"
      bgcolor={!image ? '#F9F1F7' : '#FFFFFF'}
      overflow="hidden"
    >
      {image ? (
        resolvedLayout === 'side-by-side' ? (
          // Side-by-side layout
          <Box display="flex" height="100%" px={'12px'} py={'8px'}>
            <Box
              component="img"
              src={URL.createObjectURL(image)}
              alt="Preview"
              sx={{
                height: '100%',
                width: '50%',
                objectFit: 'cover',
                borderTopLeftRadius: '12px',
                borderBottomLeftRadius: '12px',
                filter: bannerOptions.blurBackground ? 'blur(4px)' : 'none',
              }}
            />
            <Box
              display="flex"
              flexDirection="column"
              justifyContent="center"
              alignItems="flex-start"
              p={3}
              gap="20px"
              width="50%"
              sx={{
                backgroundColor: backgroundStyle,
                color: bannerOptions.textColor,
                borderTopRightRadius: '12px',
                borderBottomRightRadius: '12px',
              }}
            >
              <Typography
                fontWeight={700}
                fontSize="20px"
                color={bannerOptions.textColor}
              >
                {description}
              </Typography>
              <Button
                variant="outlined"
                sx={{
                  borderRadius: '8px',
                  backgroundColor: '#FFFFFF',
                  color: '#A24295',
                  border: 'none',
                  fontWeight: 700,
                  fontSize: '16px',
                  textTransform: 'none',
                  px: '20px',
                  py: '8px',
                  '&:hover': {
                    backgroundColor: '#F3E6F1',
                  },
                }}
              >
                {buttonText}
              </Button>
            </Box>
          </Box>
        ) : (
          // Center and left layouts
          <>
            <Box
              component="img"
              src={URL.createObjectURL(image)}
              alt="Preview"
              sx={{
                width: '100%',
                height: '100%',
                objectFit: 'cover',
                borderRadius: '12px',
                filter: bannerOptions.blurBackground ? 'blur(4px)' : 'none',
              }}
            />

            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                borderRadius: '12px',
                background: backgroundStyle,
                opacity: 0.4,
                zIndex: 1,
              }}
            />

            <Box
              position="absolute"
              sx={{
                top: '50%',
                left: resolvedLayout === 'center' ? '50%' : '80px',
                transform: 'translate(-50%, -50%)',
                alignItems: 'center',

                display: 'flex',
                flexDirection: 'column',
                zIndex: 2,
              }}
            >
              <Typography
                fontWeight={700}
                fontSize="20px"
                maxWidth="205px"
                color={bannerOptions.textColor}
                textAlign={resolvedLayout === 'center' ? 'center' : 'left'}
              >
                {description}
              </Typography>

              <Button
                variant="outlined"
                sx={{
                  mt: '20px',
                  borderRadius: '8px',
                  backgroundColor: '#FFFFFF',
                  color: '#A24295',
                  border: 'none',
                  fontWeight: 700,
                  fontSize: '16px',
                  textTransform: 'none',
                  px: '20px',
                  py: '8px',
                  '&:hover': {
                    backgroundColor: '#F3E6F1',
                  },
                }}
              >
                {buttonText}
              </Button>
            </Box>
          </>
        )
      ) : (
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          flexDirection="column"
          height="100%"
          px={3}
        >
          <Typography fontSize="12px" color="#666" textAlign="center">
            You can see how your banner will appear on your profile once you
            upload your image and add text!
          </Typography>
        </Box>
      )}
    </Box>
  );
};
