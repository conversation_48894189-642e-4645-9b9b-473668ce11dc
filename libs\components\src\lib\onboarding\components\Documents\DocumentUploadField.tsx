import {
  Box,
  Button,
  IconButton,
  LinearProgress,
  Typography,
  alpha,
  linearProgressClasses,
  styled,
  Grid,
} from '@mui/material';

import {
  Close as CloseIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';

import TooltipExtended from './Tooltip';
import { UploadStatus } from '@minicardiac-client/types';

const BorderLinearProgress = styled(LinearProgress)(({ theme }) => ({
  height: 12,
  borderRadius: 99,
  [`&.${linearProgressClasses.colorPrimary}`]: {
    backgroundColor: alpha(theme.palette.grey[500], 0.25),
  },
  [`& .${linearProgressClasses.bar}`]: {
    borderRadius: 99,
    backgroundImage: `linear-gradient(to right, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
    backgroundRepeat: 'no-repeat',
    backgroundSize: '100% 100%',
  },
}));

const networkError = 'Network error.';

export const DocumentUploadField = ({
  fieldId,
  label,
  optional = false,
  maxCount = 1,
  onFileUpload,
  uploadStatuses,
  onFileRemove,
  onFileRetry,
  instructions,
}: {
  fieldId: string;
  label: string;
  optional: boolean;
  maxCount: number;
  onFileUpload: (file: File, fieldId: string) => void;
  uploadStatuses: UploadStatus[];
  onFileRemove: (fieldId: string, uploadId: string) => void;
  onFileRetry: (fieldId: string, uploadId: string) => void;
  instructions: string;
}) => {
  const handleFileSelect = (e: any) => {
    if (e.target.files.length > 0) {
      onFileUpload(e.target.files[0], fieldId);
    }
  };

  // Check if max file limit is reached
  const isMaxLimitReached = uploadStatuses.length >= maxCount;

  return (
    <Grid
      container
      sx={{
        pl: { xs: '16px', sm: '40px' },
        pr: { xs: '16px', sm: 2 },
        alignItems: 'center',
        borderBottom: (theme) =>
          `1px dashed ${alpha((theme.palette as any).neutral[500], 0.25)}`,
      }}
    >
      <Grid
        container
        flexGrow={1}
        // height={100}
        minHeight={90}
        alignItems={'center'}
        sx={{
          flexDirection: { xs: 'row', sm: 'row' },
          flexWrap: 'wrap',
          justifyContent: {
            xs: 'space-between',
            sm: 'flex-start',
          },
          rowGap: { xs: 1, sm: 0 },
        }}
      >
        {/* Left section - Label */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            flexWrap: 'wrap',
            gap: 1,
          }}
        >
          {/* Label section */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              flexWrap: 'wrap',
              maxWidth: '300px',
              width: '100%',
            }}
          >
            <Typography
              variant={'subtitle4' as any}
              sx={{ fontSize: { xs: '12px', sm: '16px' }, fontWeight: 600 }}
            >
              {label}
              {optional && (
                <Typography
                  variant="caption"
                  color="text.secondary"
                  component="span"
                  sx={{ ml: 0.5 }}
                >
                  (Optional)
                </Typography>
              )}
            </Typography>
            <TooltipExtended />
          </Box>

          {/* Add files button */}
          <Button
            component="label"
            sx={{ color: 'secondary' }}
            disabled={isMaxLimitReached}
          >
            Add files
            <input
              type="file"
              hidden
              onChange={handleFileSelect}
              disabled={isMaxLimitReached}
            />
          </Button>

          {/* Right section - First file info and status */}
          <Grid size={{ xs: 12, sm: 6 }} sx={{ width: '100%' }}>
            {uploadStatuses.length > 0 && (
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 1,
                  borderRadius: 2,
                  padding: 2,
                }}
              >
                {/* File name */}
                <Typography
                  variant="body2"
                  noWrap
                  sx={{
                    width: '100%',
                    fontWeight: 500,
                  }}
                >
                  {uploadStatuses[0].file.name}
                </Typography>

                {/* Status and actions */}
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'row',
                    // alignItems: {
                    //   xs: 'flex-start',
                    //   sm: 'center',
                    // },
                    justifyContent: 'space-between',
                    gap: 1.5,
                    width: '100%',
                  }}
                >
                  {/* Status Display */}
                  <Box sx={{ flexGrow: 1 }}>
                    {uploadStatuses[0].status === 'uploading' && (
                      <BorderLinearProgress
                        variant="determinate"
                        value={uploadStatuses[0].progress}
                        sx={{ width: { xs: '100%', sm: '280px' } }}
                      />
                    )}

                    {uploadStatuses[0].status === 'success' && (
                      <Box
                        sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
                      >
                        <CheckCircleIcon color="success" fontSize="small" />
                        <Typography variant="body2">
                          Upload successful
                        </Typography>
                      </Box>
                    )}

                    {uploadStatuses[0].status === 'error' && (
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 0.5,
                          color: 'error.main',
                        }}
                      >
                        <ErrorIcon color="error" fontSize="small" />
                        <Typography variant="caption" color="error">
                          {uploadStatuses[0].errorMessage}
                        </Typography>
                      </Box>
                    )}
                  </Box>

                  {/* Action Buttons */}
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'row',
                      gap: 1.5,
                      flexWrap: 'wrap',
                    }}
                  >
                    {uploadStatuses[0].status === 'error' ? (
                      uploadStatuses[0].errorMessage === networkError ? (
                        <IconButton
                          size="small"
                          onClick={() =>
                            onFileRetry(fieldId, uploadStatuses[0].id)
                          }
                        >
                          <RefreshIcon color="secondary" />
                        </IconButton>
                      ) : (
                        <IconButton
                          size="small"
                          onClick={() =>
                            onFileRemove(fieldId, uploadStatuses[0].id)
                          }
                        >
                          <CloseIcon color="secondary" />
                        </IconButton>
                      )
                    ) : uploadStatuses[0].status === 'success' ? (
                      <IconButton
                        size="small"
                        onClick={() =>
                          onFileRemove(fieldId, uploadStatuses[0].id)
                        }
                      >
                        <DeleteIcon color="secondary" />
                      </IconButton>
                    ) : (
                      <IconButton
                        size="small"
                        onClick={() =>
                          onFileRemove(fieldId, uploadStatuses[0].id)
                        }
                      >
                        <CloseIcon color="secondary" />
                      </IconButton>
                    )}
                  </Box>
                </Box>
              </Box>
            )}
          </Grid>
        </Box>
      </Grid>

      {/* Additional files section - Reusing the commented code for multiple files */}
      {uploadStatuses.length > 1 &&
        uploadStatuses.slice(1).map((uploadStatus, index) => (
          <Grid
            key={uploadStatus.id}
            size={{ xs: 6 }}
            offset={6}
            height={{ xs: '70px', sm: '90px' }}
            alignContent={'center'}
            justifySelf={'end'}
            container
          >
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                width: '100%',
              }}
            >
              <Box
                sx={{ display: 'flex', alignItems: 'center', width: '100%' }}
              >
                <Typography
                  variant="body2"
                  noWrap
                  sx={{
                    width: 120,
                    flexShrink: 0,
                  }}
                >
                  {uploadStatus.file.name}
                </Typography>

                <Box sx={{ flexGrow: 1, ml: 8 }}>
                  {uploadStatus.status === 'uploading' && (
                    <BorderLinearProgress
                      variant="determinate"
                      value={uploadStatus.progress}
                      sx={{ width: '280px' }}
                    />
                  )}

                  {uploadStatus.status === 'success' && (
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                      }}
                    >
                      <CheckCircleIcon
                        color="success"
                        fontSize="small"
                        width={16}
                        sx={{ mr: 0.5 }}
                      />
                      <Typography
                        variant="body2"
                        noWrap
                        sx={{
                          flexShrink: 0,
                          ml: 0.5,
                        }}
                      >
                        Upload successful
                      </Typography>
                    </Box>
                  )}

                  {uploadStatus.status === 'error' && (
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        color: 'error.main',
                      }}
                    >
                      <ErrorIcon
                        color="error"
                        fontSize="small"
                        width={16}
                        sx={{ mr: 0.5 }}
                      />
                      <Typography variant="caption" color="error">
                        {uploadStatus.errorMessage}
                      </Typography>
                    </Box>
                  )}
                </Box>

                {uploadStatus.status === 'error' ? (
                  uploadStatus.errorMessage === networkError ? (
                    <IconButton
                      size="small"
                      onClick={() => onFileRetry(fieldId, uploadStatus.id)}
                    >
                      <RefreshIcon
                        fontSize="small"
                        color="secondary"
                        sx={{
                          width: 24,
                          height: 24,
                        }}
                      />
                    </IconButton>
                  ) : (
                    <IconButton
                      size="small"
                      onClick={() => onFileRemove(fieldId, uploadStatus.id)}
                    >
                      <CloseIcon
                        fontSize="small"
                        color="secondary"
                        sx={{
                          width: 24,
                          height: 24,
                        }}
                      />
                    </IconButton>
                  )
                ) : uploadStatus.status === 'success' ? (
                  <IconButton
                    size="small"
                    onClick={() => onFileRemove(fieldId, uploadStatus.id)}
                  >
                    <DeleteIcon
                      fontSize="small"
                      color="secondary"
                      sx={{
                        width: 24,
                        height: 24,
                      }}
                    />
                  </IconButton>
                ) : (
                  <IconButton
                    size="small"
                    onClick={() => onFileRemove(fieldId, uploadStatus.id)}
                  >
                    <CloseIcon
                      fontSize="small"
                      color="secondary"
                      sx={{
                        width: 24,
                        height: 24,
                      }}
                    />
                  </IconButton>
                )}
              </Box>
            </Box>
          </Grid>
        ))}
    </Grid>
  );
};
