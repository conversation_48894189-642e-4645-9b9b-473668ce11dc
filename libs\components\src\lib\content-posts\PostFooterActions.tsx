'use client';

import { useState, useMemo, useCallback } from 'react';
import { Box, Typography, useMediaQuery, useTheme } from '@mui/material';
import { Icon } from '@iconify/react';
import CommentIcon from '../Icons/ContentPostIcons/CommentIcon';
import CommentsList from '../comments/CommentsList';
import AddCommentInput from '../comments/AddCommentInput';
import ActionButtonWithCount from './ActionButtonWithCount';
import ShareDialog from '../dialogs/ShareDialog';
import { Comment } from '@minicardiac-client/types';
import { useTranslations } from 'next-intl';
import { useComments, useLikePost } from '@minicardiac-client/apis';
import { useFeedStore } from '../store/useFeedStore';

interface PostFooterActionsProps {
  likes: number;
  commentsCount: number;
  reposts: number;
  shares: number;
  comments?: Comment[];
  allowPin?: boolean;
  onOpenComments?: () => void;
  showComments?: boolean;
  setShowComments?: (show: boolean) => void;
  onClickComment?: () => void;
  postId?: string;
  isLiked: boolean;
  post?: {
    author?: {
      name: string;
      avatar?: string;
    };
    content?: string;
    images?: string[];
  };
}

const PostFooterActions = ({
  likes,
  commentsCount,
  reposts,
  shares,
  comments = [],
  allowPin = false,
  onOpenComments,
  showComments = false,
  setShowComments,
  onClickComment,
  postId,
  isLiked = false,
  post,
}: PostFooterActionsProps) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const t = useTranslations('questionPost');

  const likeMutation = useLikePost();

  const [liked, setLiked] = useState(isLiked);
  const [likeCount, setLikeCount] = useState(likes);
  const [reposted, setReposted] = useState(false);
  const [repostCount, setRepostCount] = useState(reposts);
  const [shareDialogOpen, setShareDialogOpen] = useState(false);

  const handleLikeChange = useCallback(
    (postId: string, like: boolean) => {
      useFeedStore.getState().updateLike(postId, like);

      likeMutation.mutate(
        { postId, like },
        {
          onError: (error) => {
            console.error('Failed to like post:', error);
            useFeedStore.getState().updateLike(postId, !like);
            setLiked(!like);
            setLikeCount((prev) => prev + (like ? -1 : 1));
          },
        }
      );
    },
    [likeMutation]
  );

  const handleLike = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();

    if (!postId) return;

    const newLikedState = !isLiked;

    setLikeCount((prev) => prev + (newLikedState ? 1 : -1));
    handleLikeChange(postId, newLikedState);
  };

  const handleRepostToggle = () => {
    setReposted((prev) => !prev);
    setRepostCount((prev) => prev + (reposted ? -1 : 1));
    // You can optionally add a repost mutation here
  };

  const handleCommentClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    onClickComment?.();
    onOpenComments?.();
  };

  const { comments: fetchedComments, isLoading: isCommentsLoading } =
    useComments(postId || '', {
      enabled: showComments && !!postId,
    });

  const counts = useMemo(
    () => ({
      likes: likeCount,
      comments: commentsCount,
      reposts: repostCount,
      shares,
    }),
    [likeCount, commentsCount, repostCount, shares]
  );

  return (
    <Box sx={{ width: '100%' }}>
      {/* Action Buttons */}
      <Box
        display="flex"
        alignItems="center"
        justifyContent="space-between"
        gap="40px"
        sx={{ height: '38px', width: '100%' }}
      >
        <ActionButtonWithCount
          icon={
            <Icon
              icon={liked ? 'mdi:heart' : 'mdi:heart-outline'}
              style={{ color: '#A24295', fontSize: 24 }}
            />
          }
          label={t('like')}
          count={counts.likes}
          onClick={handleLike}
        />
        <ActionButtonWithCount
          icon={<CommentIcon />}
          label={t('comment')}
          count={counts.comments}
          onClick={handleCommentClick}
        />
        <ActionButtonWithCount
          icon={
            <Icon
              icon={
                reposted
                  ? 'material-symbols:autorenew-rounded'
                  : 'garden:arrow-retweet-stroke-12'
              }
              style={{ color: '#A24295', fontSize: 24 }}
            />
          }
          label={t('repost')}
          count={counts.reposts}
          onClick={handleRepostToggle}
        />
        <ActionButtonWithCount
          icon={
            <Icon icon="mdi:share" style={{ color: '#A24295', fontSize: 24 }} />
          }
          label={t('share')}
          count={counts.shares}
          onClick={() => setShareDialogOpen(true)}
        />
      </Box>

      {showComments && (
        <Box
          mt={2}
          display="flex"
          flexDirection="column"
          gap="20px"
          sx={{
            position: isMobile ? 'relative' : 'static',
            height: isMobile ? '100%' : 'auto',
          }}
        >
          <Box
            sx={{
              flex: 1,
              overflowY: isMobile ? 'auto' : 'visible',
              p: isMobile ? '16px' : 0,
              display: 'flex',
              flexDirection: 'column',
              gap: '20px',
            }}
          >
            {!isMobile && postId && <AddCommentInput postId={postId} />}

            {isCommentsLoading ? (
              <Typography
                fontSize="14px"
                fontWeight={500}
                color="text.secondary"
                p={2}
              >
                Loading comments...
              </Typography>
            ) : fetchedComments.length > 0 ? (
              <CommentsList
                comments={fetchedComments}
                allowPin={allowPin}
                postId={postId}
              />
            ) : (
              <Typography
                fontSize="14px"
                fontWeight={500}
                color="text.secondary"
                p={2}
              >
                {t('noComments')}
              </Typography>
            )}
          </Box>

          {isMobile && (
            <Box
              sx={{
                position: 'fixed',
                bottom: 0,
                left: 0,
                p: '16px',
                backgroundColor: '#fff',
                boxShadow: '0px -4px 20px rgba(0,0,0,0.15)',
                width: '100%',
              }}
            >
              {postId && <AddCommentInput postId={postId} />}
            </Box>
          )}
        </Box>
      )}

      {/* Share Dialog */}
      <ShareDialog
        open={shareDialogOpen}
        onClose={() => setShareDialogOpen(false)}
        post={post ? {
          postId: postId || '',
          content: post.content || '',
          postType: 'text' as const,
          workspaceId: '',
          publisherName: post.author?.name || '',
          username: post.author?.name || '',
          profileImageUrlThumbnail: post.author?.avatar || '',
          medias: post.images?.map((image, index) => ({
            mediaPath: image,
            mediaType: 'image',
            altText: null
          })) || []
        } : undefined}
      />
    </Box>
  );
};

export default PostFooterActions;
