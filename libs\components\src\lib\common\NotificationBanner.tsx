'use client';

import { Box, Typography, IconButton, useTheme } from '@mui/material';
import { Close as CloseIcon } from '@mui/icons-material';
import { forwardRef } from 'react';

export interface NotificationBannerProps {
  /** The message to display in the banner */
  message: string;
  /** Custom width for the banner (default: 719px) */
  width?: string | number;
  /** Custom height for the banner (default: 48px) */
  height?: string | number;
  /** Background color (default: #F6ECF4) */
  backgroundColor?: string;
  /** Text color */
  textColor?: string;
  /** Whether to show the close button */
  showCloseButton?: boolean;
  /** Callback when close button is clicked */
  onClose?: () => void;
  /** Additional custom styles */
  sx?: object;
  /** Custom CSS class */
  className?: string;
}

export const NotificationBanner = forwardRef<
  HTMLDivElement,
  NotificationBannerProps
>(function NotificationBanner(
  {
    message,
    width = '719px',
    height = '48px',
    backgroundColor = '#F6ECF4',
    textColor = '#8B5A8B',
    showCloseButton = true,
    onClose,
    sx = {},
    className,
    ...props
  },
  ref
) {
  const theme = useTheme();

  return (
    <Box
      ref={ref}
      className={className}
      sx={{
        width,
        height: {
          xs: 'auto', // Auto height on mobile to accommodate text wrapping
          sm: height, // Fixed height on desktop and up
        },
        minHeight: {
          xs: '56px', // Minimum height on mobile
          sm: height, // Original height on desktop
        },
        backgroundColor,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: `${theme.spacing(1.5)} ${theme.spacing(2)}`,
        borderRadius: '4px',
        boxSizing: 'border-box',
        position: 'relative',
        ...sx,
      }}
      {...props}
    >
      <Typography
        variant="body2"
        sx={{
          color: textColor,
          fontSize: theme.typography.fontSize || '14px',
          fontWeight: theme.typography.fontWeightRegular || 400,
          lineHeight: '20px',
          textAlign: 'center',
          flex: 1,
          paddingRight: showCloseButton ? '32px' : '0',
          fontFamily: theme.typography.fontFamily,
        }}
      >
        {message}
      </Typography>

      {showCloseButton && (
        <IconButton
          onClick={onClose}
          size="small"
          sx={{
            position: 'absolute',
            right: theme.spacing(1),
            top: '50%',
            transform: 'translateY(-50%)',
            color: textColor,
            padding: theme.spacing(0.5),
            '&:hover': {
              backgroundColor:
                theme.palette.action?.hover || 'rgba(139, 90, 139, 0.1)',
            },
          }}
        >
          <CloseIcon fontSize="small" />
        </IconButton>
      )}
    </Box>
  );
});

export default NotificationBanner;
