'use client';

import React from 'react';
import { Box, Container, useMediaQuery } from '@mui/material';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  BackButton,
  CustomizedSteppers,
  ExtendedTheme,
  PatientProfileWelcome,
  Subtitle,
} from '@minicardiac-client/components';
import {
  ProfileSetupForm,
  ProfileFormData,
} from '@minicardiac-client/components';
import { useAuth, useProfessionalProfileSetup } from '@minicardiac-client/apis';
import { useSnackbar } from '@minicardiac-client/components';
import { useTheme } from '@emotion/react';

export default function ProfessionalProfileSetupPage() {
  const router = useRouter();
  const [activeStep] = React.useState(0);
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const { showSuccess, showError } = useSnackbar();
  const [formData, setFormData] = React.useState<ProfileFormData>({
    introductoryStatement: '',
    profileImageUrl: '',
    profileImageUrlThumbnail: '',
    title: '',
    qualifications: '',
    jobTitle: '',
    employerId: '',
    mainProfession: '',
    category: '',
  });
  const [error, setError] = React.useState<string | null>(null);
  const { authState } = useAuth();
  const theme = useTheme() as ExtendedTheme;
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));
  const searchParams = useSearchParams();
  const professionalType = searchParams?.get('type');

  const { mutateAsync: saveProfessionalProfile } = useProfessionalProfileSetup({
    onSuccess: () => {
      showSuccess('Profile saved successfully!');
      setTimeout(() => {
        router.push('/professional/document-upload');
      }, 1000);
    },
    onError: (errMsg) => {
      setError(errMsg);
      showError(errMsg);
    },
  });

  const handleFormChange = React.useCallback((data: ProfileFormData) => {
    setFormData(data);
  }, []);

  const handleContinue = async () => {
    setIsSubmitting(true);
    setError(null);

    try {
      await saveProfessionalProfile({
        ...formData,
        professionalType: professionalType as
          | 'ALLIED_CARDIAC'
          | 'CARDIAC_SURGEON'
          | 'CARDIOLOGIST',
      });
    } catch (err) {
      console.error('Unexpected error in handleContinue:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDoThisLater = () => {
    // Navigate to another page or dashboard
    router.push('/feed?fromSignup=true');
  };

  return (
    <Container maxWidth="lg">
      <Box mt={'20px'}>
        {/* Welcome Header */}

        {!isSmallScreen && (
          <PatientProfileWelcome
            patientName={authState.user?.displayName || ''}
            subtitle={''}
          />
        )}

        <BackButton />

        <Subtitle
          text={"Let's set up your Professional Account!"}
          sx={{ fontSize: { xs: '12px', sm: '16px' } }}
          marginBottom={'34px'}
        />

        <CustomizedSteppers
          activeStep={activeStep}
          steps={['Profile Setup', 'Document Upload', 'Adding Network']}
        />

        {/* Use the ProfileSetupForm component with isBasicPlan=false for paid users */}
        <Box
          sx={{
            mt: { xs: '25px', sm: '30px', md: '40px' },
            py: { xs: 0, sm: '50px' },
            px: { xs: '0px', sm: '30px', md: '74px' },
          }}
        >
          <ProfileSetupForm
            isBasicPlan={false}
            onChange={handleFormChange}
            onSave={handleContinue}
            onSkip={handleDoThisLater}
            isSubmitting={isSubmitting}
            userData={authState?.user}
            professionalType={professionalType || ''}
          />
          {error && (
            <Box sx={{ color: 'error.main', mt: 2, textAlign: 'center' }}>
              {error}
            </Box>
          )}
        </Box>
      </Box>
    </Container>
  );
}
