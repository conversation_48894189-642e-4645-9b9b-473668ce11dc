'use client';

import { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  TextField,
  Button,
  Avatar,
  Dialog,
  DialogContent,
  IconButton,
  useMediaQuery,
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import CloseIcon from '@mui/icons-material/Close';
import EmojiEmotionsOutlinedIcon from '@mui/icons-material/EmojiEmotionsOutlined';

import PostCard from '../content-posts/PostCard';
import { TagPost } from '@minicardiac-client/apis';
import { getCdnUrl } from '@minicardiac-client/utilities';

// Asset path constants for social platform SVGs
const WHATSAPP_SHARE_SVG = 'assets/whats_app_share.svg';
const FACEBOOK_SHARE_SVG = 'assets/facebook_share.svg';
const INSTAGRAM_SHARE_SVG = 'assets/instagram_share.svg';
const LINKEDIN_SHARE_SVG = 'assets/linkedin_share.svg';

interface SharePostDialogProps {
  open: boolean;
  onClose: () => void;
  post?: TagPost; // Changed to use TagPost interface
}

const contacts = [
  {
    id: 1,
    name: '<PERSON>',
    avatar: 'https://i.pravatar.cc/48?img=1',
    initials: 'GL',
  },
  {
    id: 2,
    name: 'Kevin Patel',
    avatar: 'https://i.pravatar.cc/48?img=2',
    initials: 'KP',
  },
  {
    id: 3,
    name: 'Rosa Diaz',
    avatar: 'https://i.pravatar.cc/48?img=3',
    initials: 'RD',
  },
  {
    id: 4,
    name: 'Laverne Holt',
    avatar: 'https://i.pravatar.cc/48?img=4',
    initials: 'LH',
  },
  {
    id: 5,
    name: 'Roger Peralta',
    avatar: 'https://i.pravatar.cc/48?img=5',
    initials: 'RP',
  },
  {
    id: 6,
    name: 'Charles Boyle',
    avatar: 'https://i.pravatar.cc/48?img=6',
    initials: 'CB',
  },
  {
    id: 7,
    name: 'Amy Santiago',
    avatar: 'https://i.pravatar.cc/48?img=7',
    initials: 'AS',
  },
];

const socialPlatforms = [
  {
    name: 'WhatsApp',
    svgUrl: getCdnUrl(WHATSAPP_SHARE_SVG),
    color: '#25D366',
  },
  {
    name: 'Facebook',
    svgUrl: getCdnUrl(FACEBOOK_SHARE_SVG),
    color: '#1877F2',
  },
  {
    name: 'Instagram',
    svgUrl: getCdnUrl(INSTAGRAM_SHARE_SVG),
    color: '#E4405F',
  },
  {
    name: 'LinkedIn',
    svgUrl: getCdnUrl(LINKEDIN_SHARE_SVG),
    color: '#0A66C2',
  },
];

const mockPostImages = [
  'https://images.unsplash.com/photo-**********-2a8555f1a136?crop=entropy&cs=srgb&fm=jpg&ixid=****************************************************************************************************************&ixlib=rb-4.1.0&q=85',
  'https://images.unsplash.com/photo-1649877510851-10effb9a59b4?crop=entropy&cs=srgb&fm=jpg&ixid=**************************************************************************************************************&ixlib=rb-4.1.0&q=85',
  'https://images.unsplash.com/photo-**********-038aab8f3f3b?crop=entropy&cs=srgb&fm=jpg&ixid=**********************************************************************************************************************&ixlib=rb-4.1.0&q=85',
  'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?crop=entropy&cs=srgb&fm=jpg&ixid=***********************************************************************************&ixlib=rb-4.1.0&q=85',
  'https://images.unsplash.com/photo-**********-5c350d0d3c56?crop=entropy&cs=srgb&fm=jpg&ixid=**********************************************************************************&ixlib=rb-4.1.0&q=85',
];

// Header Component
const ShareDialogHeader = ({ onClose }: { onClose: () => void }) => {
  const theme = useTheme();

  return (
    <Box
      display="flex"
      justifyContent="space-between"
      alignItems="flex-start"
      sx={{
        px: '40px',
        pt: '40px',
        pb: '20px',
        height: '95px',
        flexShrink: 0,
      }}
    >
      <Typography
        sx={{
          fontFamily: theme.typography.fontFamily,
          fontWeight: 500,
          fontSize: '28px',
          fontStyle: 'normal',
          lineHeight: 'normal',
          color: 'text.primary', // Uses theme's text.primary which should be #1E1E1E
          mt: '-2px', // Adjust title position up slightly
        }}
      >
        Share Post
      </Typography>
      <IconButton
        onClick={onClose}
        sx={{
          color: '#666666',
          p: 0,
          width: '24px',
          height: '24px',
          mt: '-4px', // Move close button up
          mr: '-2px', // Move close button left
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.04)',
          },
        }}
      >
        <CloseIcon sx={{ fontSize: '24px' }} />
      </IconButton>
    </Box>
  );
};

// Post Preview Component - Now using real PostCard component
const PostPreviewCard = ({ post }: { post?: TagPost }) => {
  // If no post provided, use mock data for preview
  const mockPost: TagPost = {
    postId: 'preview-123',
    content:
      'Performed a challenging mitral valve repair today on a patient...',
    postType: 'image',
    workspaceId: 'workspace456',
    publisherName: 'Roger Taylor',
    username: 'rogertaylor',
    profileImageUrlThumbnail: 'https://i.pravatar.cc/24?img=8',
    medias: mockPostImages.slice(0, 3).map((url, index) => ({
      mediaPath: url, // Keep full URL for external images
      mediaType: 'image',
      altText: `Medical procedure ${index + 1}`, // Added required altText property
    })),
  };

  return (
    <Box
      sx={{
        width: '207px', // Slight adjustment for better alignment
        minWidth: '207px',
        height: '166px',
        minHeight: '166px',
        flexShrink: 0,
        ml: '2px', // Add small left margin for proper alignment
      }}
    >
      <PostCard post={post || mockPost} />
    </Box>
  );
};

// Message Input Component
const MessageInputSection = ({
  message,
  setMessage,
}: {
  message: string;
  setMessage: (value: string) => void;
}) => {
  const theme = useTheme();

  return (
    <Box flex={1} sx={{ position: 'relative' }}>
      <TextField
        label="Share with a Message (optional)"
        placeholder="Write something about this"
        fullWidth
        multiline
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        InputLabelProps={{
          shrink: true,
          sx: {
            '&.MuiInputLabel-root': {
              color: '#333333',
              fontSize: '14px',
              fontFamily: theme.typography.fontFamily,
              fontWeight: 500,
              backgroundColor: 'white',
              padding: '0 8px',
              zIndex: 1,
              position: 'absolute',
              top: '0px', // Position label exactly on the border
              left: '12px', // Align with input padding
              transformOrigin: 'top left',
              transform: 'translateY(-50%)', // Center the label on the border line
            },
            '&.MuiInputLabel-shrink': {
              color: '#333333',
              backgroundColor: 'white',
              padding: '0 8px',
              top: '0px',
              left: '12px',
              transform: 'translateY(-50%)',
            },
            '&.Mui-focused': {
              color: '#A24295',
              top: '0px',
              left: '12px',
              transform: 'translateY(-50%)',
            },
          },
        }}
        sx={{
          width: '420px', // Slight adjustment for proper alignment
          height: '166px',
          mt: '5px', // Move textbox down by 5px to give label space
          '& .MuiInputBase-root': {
            height: '166px',
            alignItems: 'flex-start',
            fontFamily: theme.typography.fontFamily,
            padding: '16px',
            fontSize: '14px',
            '& .MuiInputBase-input': {
              height: '100% !important',
              overflow: 'auto !important',
              fontFamily: theme.typography.fontFamily,
              padding: '0 !important',
              resize: 'none',
              '&::placeholder': {
                color: '#999999',
                opacity: 1,
              },
            },
          },
          '& .MuiOutlinedInput-notchedOutline': {
            borderColor: '#E0E0E0',
            borderWidth: '1px',
          },
          '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: '#CCCCCC',
          },
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderColor: '#A24295',
            borderWidth: '2px',
          },
        }}
      />
      {/* Emoji Button */}
      <IconButton
        sx={{
          position: 'absolute',
          bottom: '12px',
          right: '12px',
          color: '#E91E63',
          width: '20px',
          height: '20px',
          p: 0,
          '&:hover': {
            backgroundColor: 'rgba(233, 30, 99, 0.04)',
          },
        }}
      >
        <EmojiEmotionsOutlinedIcon sx={{ fontSize: '18px' }} />
      </IconButton>
    </Box>
  );
};

// Contacts Grid Component
const ContactsGrid = ({
  selectedContacts,
  toggleContact,
}: {
  selectedContacts: number[];
  toggleContact: (id: number) => void;
}) => {
  const theme = useTheme();

  return (
    <Box sx={{ mt: '20px' }}>
      {' '}
      {/* Add margin top to move section up */}
      <Box display="flex" alignItems="center" gap="4px" mb="20px">
        <Typography
          sx={{
            fontFamily: theme.typography.fontFamily,
            fontWeight: 400,
            fontSize: '16px',
            lineHeight: '20px',
            color: '#000000',
          }}
        >
          Share as a
        </Typography>
        <Box
          component="img"
          src="/favicon.svg"
          alt="Heart"
          sx={{
            width: '20px',
            height: '20px',
            mx: '4px',
          }}
        />
        <Typography
          sx={{
            fontFamily: theme.typography.fontFamily,
            fontWeight: 400,
            fontSize: '16px',
            lineHeight: '20px',
            color: '#000000',
          }}
        >
          Message
        </Typography>
      </Box>
      {/* Contacts Grid */}
      <Box display="flex" flexWrap="wrap" gap="16px" sx={{ mr: '2px' }}>
        {contacts.map((contact) => (
          <Box
            key={contact.id}
            display="flex"
            flexDirection="column"
            alignItems="center"
            gap="6px"
            sx={{
              cursor: 'pointer',
              p: '6px',
              borderRadius: '8px',
              backgroundColor: selectedContacts.includes(contact.id)
                ? '#E3F2FD'
                : 'transparent',
              border: selectedContacts.includes(contact.id)
                ? '2px solid #2196F3'
                : '2px solid transparent',
              '&:hover': {
                backgroundColor: selectedContacts.includes(contact.id)
                  ? '#E3F2FD'
                  : '#F5F5F5',
              },
              transition: 'all 0.2s ease',
            }}
            onClick={() => toggleContact(contact.id)}
          >
            <Avatar
              src={contact.avatar}
              sx={{
                width: 48,
                height: 48,
                fontSize: '16px',
                fontWeight: 600,
              }}
            >
              {contact.initials}
            </Avatar>
            <Typography
              sx={{
                fontFamily: theme.typography.fontFamily,
                fontSize: '12px',
                lineHeight: '14px',
                color: '#000000',
                textAlign: 'center',
                maxWidth: '64px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                fontWeight: 400,
              }}
            >
              {contact.name.split(' ')[0]}
            </Typography>
            <Typography
              sx={{
                fontFamily: theme.typography.fontFamily,
                fontSize: '12px',
                lineHeight: '14px',
                color: '#000000',
                textAlign: 'center',
                maxWidth: '64px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                mt: '-2px',
                fontWeight: 400,
              }}
            >
              {contact.name.split(' ')[1] || ''}
            </Typography>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

// Social Platforms Component
const SocialPlatformsRow = () => {
  const theme = useTheme();

  return (
    <Box sx={{ mt: '32px' }}>
      {' '}
      {/* Use margin top instead of absolute positioning */}
      <Typography
        sx={{
          fontFamily: theme.typography.fontFamily,
          fontWeight: 500,
          fontSize: '20px',
          fontStyle: 'normal',
          lineHeight: 'normal',
          color: '#1E1E1E',
          mb: '16px',
        }}
      >
        Share to:
      </Typography>
      <Box display="flex" gap="32px" alignItems="center">
        {socialPlatforms.map((platform) => {
          return (
            <IconButton
              key={platform.name}
              sx={{
                width: '48px', // Adjust size for better alignment
                height: '48px',
                flexShrink: 0,
                borderRadius: '50%',
                backgroundColor: 'transparent',
                padding: 0,
                '&:hover': {
                  backgroundColor: 'rgba(0, 0, 0, 0.04)',
                },
                transition: 'background-color 0.2s ease',
              }}
              title={platform.name}
              onClick={() => {
                console.log(`Sharing to ${platform.name}`);
              }}
            >
              <Box
                component="img"
                src={platform.svgUrl}
                alt={platform.name}
                sx={{
                  width: '48px',
                  height: '48px',
                  borderRadius: '50%',
                  objectFit: 'cover',
                }}
              />
            </IconButton>
          );
        })}
      </Box>
    </Box>
  );
};

// Action Buttons Component
const DialogActionButtons = ({
  onClose,
  onShare,
}: {
  onClose: () => void;
  onShare: () => void;
}) => {
  const theme = useTheme();

  return (
    <Box
      sx={{
        display: 'flex',
        gap: '20px',
        justifyContent: 'center',
        alignItems: 'center',
        mt: '37px', // Increased from 32px to move down 5px
        mb: '40px',
        flexShrink: 0,
        mr: '200px', // Move buttons 5px to the right
        width: '100%',
      }}
    >
      <Button
        variant="outlined"
        onClick={onClose}
        sx={{
          width: '156px',
          height: '36px',
          backgroundColor: 'transparent',
          border: '1px solid #A24295',
          color: '#A24295',
          fontSize: '16px',
          fontWeight: 600,
          fontFamily: theme.typography.fontFamily,
          textTransform: 'none',
          borderRadius: '8px',
          '&:hover': {
            backgroundColor: 'rgba(162, 66, 149, 0.04)',
            border: '1px solid #A24295',
          },
        }}
      >
        Cancel
      </Button>
      <Button
        variant="contained"
        onClick={onShare}
        sx={{
          width: '156px',
          height: '36px',
          backgroundColor: '#A24295',
          border: '1px solid #A24295',
          borderRadius: '8px',
          color: 'white',
          fontSize: '16px',
          fontWeight: 600,
          fontFamily: theme.typography.fontFamily,
          textTransform: 'none',
          boxShadow: 'none',
          '&:hover': {
            backgroundColor: '#8B3A7F',
            border: '1px solid #8B3A7F',
            boxShadow: 'none',
          },
        }}
      >
        Share
      </Button>
    </Box>
  );
};

export default function SharePostDialog({
  open,
  onClose,
  post,
}: SharePostDialogProps) {
  const [message, setMessage] = useState('');
  const [selectedContacts, setSelectedContacts] = useState<number[]>([]);
  const theme = useTheme();
  const screenBelowSM = useMediaQuery(theme.breakpoints.down('sm'));

  const toggleContact = (contactId: number) => {
    setSelectedContacts((prev) =>
      prev.includes(contactId)
        ? prev.filter((id) => id !== contactId)
        : [...prev, contactId]
    );
  };

  const handleShare = () => {
    console.log('Sharing post with message:', message);
    console.log('Selected contacts:', selectedContacts);
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth={false}
      fullScreen={screenBelowSM}
      sx={{
        '& .MuiDialog-paper': {
          width: '798px',
          maxWidth: '798px',
          height: '722px',
          maxHeight: { xs: '100%', sm: '722px' },
          borderRadius: { xs: 0, sm: '8px' },
          margin: 0,
          overflow: 'hidden',
        },
        '& .MuiBackdrop-root': {
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
        },
      }}
    >
      <DialogContent sx={{ p: 0, height: '100%', overflow: 'hidden' }}>
        <Box
          display="flex"
          flexDirection="column"
          width="100%"
          height="100%"
          sx={{
            backgroundColor: '#FFFFFF',
          }}
        >
          {/* Header */}
          <ShareDialogHeader onClose={onClose} />

          {/* Content Area */}
          <Box
            sx={{
              px: '40px',
              flex: 1,
              display: 'flex',
              flexDirection: 'column',
              overflow: 'auto',
              alignItems: 'flex-start', // Ensure proper alignment
            }}
          >
            {/* Post Preview and Message Input Row */}
            <Box
              display="flex"
              gap="22px"
              sx={{ height: '166px', flexShrink: 0 }}
            >
              <PostPreviewCard post={post} />
              <MessageInputSection message={message} setMessage={setMessage} />
            </Box>

            {/* Share as Message Section */}
            <ContactsGrid
              selectedContacts={selectedContacts}
              toggleContact={toggleContact}
            />

            {/* Share to Social Platforms */}
            <SocialPlatformsRow />

            {/* Footer */}
            <DialogActionButtons onClose={onClose} onShare={handleShare} />
          </Box>
        </Box>
      </DialogContent>
    </Dialog>
  );
}
