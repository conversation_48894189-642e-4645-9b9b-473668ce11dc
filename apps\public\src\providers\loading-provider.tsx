import React, {
  ReactNode,
  createContext,
  useContext,
  useEffect,
  useState,
} from 'react';
import { Box, LinearProgress } from '@mui/material';
import { useIsFetching, useIsMutating } from '@tanstack/react-query';

// Types for LoadingContext (including carousel state)
interface LoadingContextType {
  isManualLoading: boolean;
  setLoading: (loading: boolean) => void;
  hasSeenIntro: boolean;
  setHasSeenIntro: (value: boolean) => void;
}

// Create context
const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

// Custom hook to use the context
export const useLoading = () => {
  const context = useContext(LoadingContext);
  if (!context) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
};

// Provider component
interface LoadingProviderProps {
  children: ReactNode;
}

export const LoadingProvider: React.FC<LoadingProviderProps> = ({
  children,
}) => {
  const [isManualLoading, setIsManualLoading] = useState(false);
  const [hasSeenIntro, setHasSeenIntroState] = useState<boolean>(true);

  // React Query states
  const isFetching = useIsFetching();
  const isMutating = useIsMutating();

  const isLoading = isFetching > 0 || isMutating > 0 || isManualLoading;

  // Load from localStorage on mount
  useEffect(() => {
    const seen = localStorage.getItem('hasSeenIntro');
    setHasSeenIntroState(seen === 'true');
  }, []);

  // Persist to localStorage
  const setHasSeenIntro = (value: boolean) => {
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem('hasSeenIntro', value.toString());
    }
    setHasSeenIntroState(value);
  };

  const contextValue: LoadingContextType = {
    isManualLoading,
    setLoading: setIsManualLoading,
    hasSeenIntro,
    setHasSeenIntro,
  };

  return (
    <LoadingContext.Provider value={contextValue}>
      {isLoading && (
        <Box
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            zIndex: 9999,
          }}
        >
          <LinearProgress
            sx={{
              height: '3px',
              '& .MuiLinearProgress-bar': {
                backgroundColor: 'primary.main',
              },
            }}
          />
        </Box>
      )}
      {children}
    </LoadingContext.Provider>
  );
};
