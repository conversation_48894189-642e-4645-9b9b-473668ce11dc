export interface Media {
  mediaPath: string;
  mediaType: string;
  altText: string | null;
}

export interface TagPost {
  postId: string;
  content: string;
  postType: 'text' | 'image' | 'video' | string;
  workspaceId: string;
  publisherName: string;
  username: string;
  profileImageUrlThumbnail: string;
  medias: Media[];
}

export interface TagWithPosts {
  tagId: string;
  tagName: string;
  posts: TagPost[];
}

export interface FollowingTagsWithPostsResponse {
  data: TagWithPosts[];
}
