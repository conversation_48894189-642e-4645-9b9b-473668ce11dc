import React from 'react';
import { Box, SxProps } from '@mui/material';
import ImageUploader from '../form-fields/ImageUploader';
import TextInput from '../form-fields/TextInput';
import LargeTextInput from '../form-fields/LargeTextInput';
import DeleteIcon from '../Icons/DeleteIcon';

interface MediaTextFormItemProps {
  form: {
    thumbnail: File | null;
    title: string;
    description: string;
  };
  handleInputChange?: (
    index: number,
    field: 'thumbnail' | 'title' | 'description',
    value: string | File | null
  ) => void;
  enableDelete?: boolean;

  mediaBoxSX: SxProps;
  titleLabel?: string;
  titlePlaceholder?: string;
  descriptionLabel?: string;
  descriptionPlaceholder?: string;
  descriptionHeight: string;
}

const MediaTextFormItem: React.FC<MediaTextFormItemProps> = ({
  form,
  handleInputChange,
  enableDelete = false,

  mediaBoxSX,
  descriptionHeight,
  titleLabel = 'Title',
  titlePlaceholder = 'Highlight your skills/ expertise here',
  descriptionLabel = 'Description',
  descriptionPlaceholder = 'A short description of your skill/ expertise',
}) => {
  return (
    <Box
      display="flex"
      gap="20px"
      flexDirection={{ xs: 'column', md: 'row' }}
      alignItems={'center'}
      width={'100%'}
    >
      {/* Left Column - Media Upload */}
      <Box display="flex" gap="8px">
        {enableDelete && <DeleteIcon fill="#A3A3A3" />}
        <ImageUploader
          thumbnail={form.thumbnail}
          label="Click to add photo/video"
          subtitles="Or drag and drop"
          sx={{
            maxWidth: { xs: '100%', md: '381px' },
            width: { xs: '100%', md: '381px' },
            maxHeight: '280px',
            height: '280px',
            ...mediaBoxSX,
          }}
        />
      </Box>

      {/* Right Column - Text Inputs */}
      <Box
        flex={1}
        display="flex"
        flexDirection="column"
        justifyContent="space-between"
        width={'100%'}
      >
        <Box display="flex" flexDirection="column" gap="20px">
          <TextInput
            label={titleLabel}
            placeholder={titlePlaceholder}
            value={form.title}
            onChange={(val) => handleInputChange?.(0, 'title', val)}
          />

          <LargeTextInput
            label={descriptionLabel}
            placeholder={descriptionPlaceholder}
            value={form.description}
            onChange={(val) => handleInputChange?.(0, 'description', val)}
            sx={{ height: descriptionHeight }}
          />
        </Box>
      </Box>
    </Box>
  );
};

export default MediaTextFormItem;
