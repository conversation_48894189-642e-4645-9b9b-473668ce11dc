'use client';

import React from 'react';
import {
  Box,
  Button,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { useRouter } from '@/apps/public/src/i18n/navigation';

import {
  ConnectWithOthers,
  NetworkingPageTemplate,
  LoadingButton,
} from '@minicardiac-client/components';
import {
  // Enhanced hooks with Zustand state
  useGetDiscoverProfilesWithState,
  useSendConnectionRequestWithState,
  useFollowProfileWithState,
  useUnfollowProfileWithState,
  useCancelConnectionRequestWithState,
  useNetworkingStore,
  useAuth,
  useCompleteNetworkingStage,
  refreshSession,
} from '@minicardiac-client/apis';
import { useSnackbar } from '@minicardiac-client/components';

export default function ProfessionalPaidAddNetworkPage() {
  const theme = useTheme();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const { showSuccess, showError } = useSnackbar();

  const { authState } = useAuth();

  // Dialog state
  const [dialogOpen, setDialogOpen] = React.useState(false);
  const [dialogAction, setDialogAction] = React.useState<
    'unfollow' | 'cancelRequest'
  >('unfollow');
  const [selectedProfile, setSelectedProfile] = React.useState<{
    id: string;
    name: string;
  } | null>(null);

  // Subscribe to Zustand store changes to force re-renders when connection state changes
  const [connectionRequests, setConnectionRequest] = React.useState(
    useNetworkingStore.getState().connectionRequests
  );
  const [followedProfiles, setFollowedProfiles] = React.useState(
    useNetworkingStore.getState().followedProfiles
  );

  // Set up subscription to Zustand store
  React.useEffect(() => {
    const unsubscribe = useNetworkingStore.subscribe((state) => {
      setConnectionRequest(state.connectionRequests);
      setFollowedProfiles(state.followedProfiles);
    });

    return () => unsubscribe();
  }, []);

  // Fetch profiles using the enhanced API hook with Zustand state
  // This hook automatically enhances profiles with UI state from the Zustand store
  const {
    data: profiles,
    isLoading: isLoadingProfiles,
    error: profilesError,
  } = useGetDiscoverProfilesWithState();

  // Enhanced mutation hooks with Zustand state
  const sendConnectionRequest = useSendConnectionRequestWithState();
  const followProfile = useFollowProfileWithState();
  const unfollowProfile = useUnfollowProfileWithState();
  const cancelConnectionRequest = useCancelConnectionRequestWithState();

  // Handle connect button click
  const handleConnect = async (profileId: string) => {
    try {
      setError(null);

      await sendConnectionRequest.mutateAsync(profileId);

      const currentConnectionRequests =
        useNetworkingStore.getState().connectionRequests;

      setConnectionRequest({ ...currentConnectionRequests });

      showSuccess('Connection request sent successfully');
    } catch (err) {
      console.error('Error sending connection request:', err);
      showError('Failed to send connection request');
    }
  };

  const handleFollow = async (profileId: string) => {
    try {
      setError(null);
      console.log('Following profile with ID:', profileId);

      // Make API call - UI state is handled automatically by the enhanced hook
      await followProfile.mutateAsync(profileId);

      const currentFollowedProfiles =
        useNetworkingStore.getState().followedProfiles;
      setFollowedProfiles({ ...currentFollowedProfiles });

      showSuccess('Profile followed successfully');
    } catch (err) {
      console.error('Error following profile:', err);
      showError('Failed to follow profile');
    }
  };

  const handleUnfollow = (profileId: string, profileName: string) => {
    setSelectedProfile({ id: profileId, name: profileName });
    setDialogAction('unfollow');
    setDialogOpen(true);
  };

  const handleCancelRequest = (profileId: string, profileName: string) => {
    setSelectedProfile({ id: profileId, name: profileName });
    setDialogAction('cancelRequest');
    setDialogOpen(true);
  };

  const confirmUnfollow = async () => {
    if (!selectedProfile) return;

    try {
      await unfollowProfile.mutateAsync(selectedProfile.id);

      const currentFollowedProfiles =
        useNetworkingStore.getState().followedProfiles;
      setFollowedProfiles({ ...currentFollowedProfiles });

      showSuccess('Profile unfollowed successfully');
    } catch (err) {
      console.error('Error unfollowing profile:', err);
      showError('Failed to unfollow profile');
    } finally {
      setDialogOpen(false);
      setSelectedProfile(null);
    }
  };

  const confirmCancelRequest = async () => {
    if (!selectedProfile) return;

    try {
      await cancelConnectionRequest.mutateAsync(selectedProfile.id);

      // Force update the local state to trigger a re-render
      const currentConnectionRequests =
        useNetworkingStore.getState().connectionRequests;
      setConnectionRequest({ ...currentConnectionRequests });

      showSuccess('Connection request unsent successfully');
    } catch (err) {
      console.error('Error unsending connection request:', err);
      showError('Failed to unsend connection request');
    } finally {
      setDialogOpen(false);
      setSelectedProfile(null);
    }
  };

  const completeNetworkingStage = useCompleteNetworkingStage();

  const handleContinue = async () => {
    setIsSubmitting(true);
    setError(null);

    try {
      await refreshSession(); // Ensure session cookie is fresh before onboarding network call
    } catch (err) {
      showError('Session expired. Please sign in again.');
      setIsSubmitting(false);
      return;
    }

    try {
      // Call the API to update the onboarding stage
      await completeNetworkingStage.mutateAsync();

      // Redirect to feed with fromSignup parameter
      router.push('/feed?fromSignup=true');
    } catch (err: any) {
      console.error('Error completing networking stage:', err);
      setError(err.message || 'Failed to complete networking stage');
      showError('Failed to complete networking stage');
    } finally {
      setIsSubmitting(false);
    }
  };

  // We don't need to manually enhance profiles anymore as the useGetDiscoverProfilesWithState hook
  // already enhances the profiles with the Zustand store state

  return (
    <NetworkingPageTemplate
      userName={authState.user?.displayName || ''}
      subtitleText="Let's set up your Professional Account!"
      showBackButton={false}
      currentStep={2}
      steps={['Profile Setup', 'Document Upload', 'Adding Network']}
    >
      <ConnectWithOthers
        profiles={profiles}
        isLoading={isLoadingProfiles}
        onConnect={handleConnect}
        onFollow={handleFollow}
        onUnfollow={handleUnfollow}
        onCancelRequest={handleCancelRequest}
        connectionRequests={connectionRequests}
        followedProfiles={followedProfiles}
        hideSteppers={true}
      />

      {/* Bottom Stick */}
      <Box
        sx={{
          position: 'fixed',
          bottom: 0,
          left: 0,
          width: '100%',
          display: 'flex',
          justifyContent: 'center',
          gap: '20px',
          py: '36px',
          backgroundColor: 'background.paper',
          borderTop: '1px solid',
          borderColor: 'divider',
          zIndex: 100,
          boxShadow: '0 -4px 20px #A3A3A31F',
        }}
      >
        {/* Do this later button */}
        <Button
          variant="outlined"
          onClick={() => router.push('/feed?fromSignup=true')}
          // disabled={isSubmitting}
          sx={{
            width: '172px',
            height: '40px',
            borderRadius: '8px',
            color: 'secondary.main',
            borderColor: 'secondary.main',
            textTransform: 'none',
            '&:hover': {
              backgroundColor: 'secondary.light',
            },
            display: { xs: 'none', sm: 'block' },
          }}
        >
          Do this later
        </Button>

        {/* Confirm and Finish button */}
        <LoadingButton
          onClick={handleContinue}
          loading={isSubmitting}
          loadingText=" Confirm and Finish..."
          sx={{
            width: '225px',
            height: '40px',
            borderRadius: '8px',
            fontWeight: theme.typography.button.fontWeight,
            fontSize: theme.typography.button.fontSize,
            lineHeight: theme.typography.button.lineHeight,
            textTransform: 'none',
            padding: '0 40px',
            bgcolor: theme.palette.secondary.main,
            color: '#FFFFFF',
            '&:hover': {
              bgcolor:
                theme.palette.tint?.hover || theme.palette.secondary.light,
            },
            '&.Mui-disabled': {
              bgcolor: theme.palette.neutral[500],
            },
          }}
        >
          Confirm and Finish
        </LoadingButton>
      </Box>

      {(error || profilesError) && (
        <Box sx={{ color: 'error.main', mt: 2, textAlign: 'center' }}>
          {error || 'Failed to load profiles. Please try again later.'}
        </Box>
      )}

      {/* Confirmation Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        aria-labelledby="confirmation-dialog-title"
        PaperProps={{
          sx: (theme) => ({
            width: 490,
            height: 186,
            borderRadius: '8px',
            position: 'absolute',
            boxShadow: theme.shadows[3],
            p: 0,
          }),
        }}
      >
        <DialogTitle id="confirmation-dialog-title" sx={{ pt: 3, pb: 1 }}>
          <Typography
            component="div"
            variant="subtitle2"
            sx={{
              fontSize: '20px',
              lineHeight: '28px',
              fontWeight: 600,
              textAlign: 'center',
            }}
          >
            {dialogAction === 'unfollow'
              ? 'Unfollow this user?'
              : 'Unsend Request?'}
          </Typography>
        </DialogTitle>
        <DialogContent
          sx={{
            px: 3,
            pb: 0,
            overflowY: 'hidden',
            '&::-webkit-scrollbar': {
              display: 'none',
            },
          }}
        >
          <Typography
            align="center"
            sx={(theme) => ({
              color: theme.palette.text.secondary,
              fontSize: '16px',
              lineHeight: '24px',
            })}
          >
            {dialogAction === 'unfollow'
              ? `Are you sure you want to unfollow Dr. ${selectedProfile?.name}?`
              : `Are you sure you want to unsend the connection request you sent to Dr. ${selectedProfile?.name}?`}
          </Typography>
        </DialogContent>
        <DialogActions sx={{ justifyContent: 'center', pb: 3, pt: 2, gap: 2 }}>
          <Button
            onClick={() => setDialogOpen(false)}
            variant="outlined"
            sx={{
              width: '136px',
              borderColor: '#A24295',
              color: '#A24295',
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={
              dialogAction === 'unfollow'
                ? confirmUnfollow
                : confirmCancelRequest
            }
            variant="contained"
            sx={{
              width: '150px',
              backgroundColor: '#A24295',
              whiteSpace: 'nowrap',
              '&:hover': {
                backgroundColor: '#B24E9F',
              },
            }}
          >
            {dialogAction === 'unfollow' ? 'Unfollow' : 'Unsend'}
          </Button>
        </DialogActions>
      </Dialog>
    </NetworkingPageTemplate>
  );
}
