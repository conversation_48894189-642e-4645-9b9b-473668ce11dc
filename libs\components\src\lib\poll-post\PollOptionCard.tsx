import { Box, Typography } from '@mui/material';
import dayjs from 'dayjs';

interface PollOptionCardProps {
  label: string;
  votes: number;
  totalVotes: number;
  selected: boolean;
  onSelect: () => void;
  percentage: number;
  isCustom: boolean;
  expiresAt: string;
  children?: React.ReactNode;
}

const PollOptionCard = ({
  label,
  votes,
  totalVotes,
  percentage,
  selected,
  onSelect,
  isCustom,
  expiresAt,
  children,
}: PollOptionCardProps) => {
  const hasExpired = dayjs().isAfter(dayjs(expiresAt));

  // Determine background color
  const getBackgroundColor = (): string => {
    if (hasExpired) {
      return selected ? '#F6ECF4' : 'transparent';
    } else {
      if (isCustom && selected) return '#F6ECF4';
      if (selected) return '#A3A3A340';
      return 'transparent';
    }
  };

  const filledBackgroundColor = getBackgroundColor();
  const typographyWeight = selected ? 700 : 400;

  return (
    <Box
      onClick={!hasExpired ? onSelect : undefined}
      sx={{
        position: 'relative',
        height: '56px',
        borderRadius: '8px',
        border: '1px solid #A24295',
        overflow: 'hidden',
        cursor: hasExpired ? 'default' : 'pointer',
      }}
    >
      {/* Filled Background */}
      <Box
        sx={{
          position: 'absolute',
          height: '100%',
          width: `${hasExpired || selected ? percentage : 0}%`,
          backgroundColor: filledBackgroundColor,
          transition: 'width 0.3s, background-color 0.3s',
        }}
      />

      {/* Option Content */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          height: '100%',
          px: '16px',
          position: 'relative',
          zIndex: 1,
        }}
      >
        <Typography
          sx={{
            fontSize: '16px',
            fontWeight: typographyWeight,
            color: '#1E1E1E',
          }}
        >
          {children || label}
        </Typography>
        <Typography
          sx={{ fontSize: '16px', fontWeight: 400, color: '#1E1E1E' }}
        >
          {hasExpired || selected ? `${percentage}%` : ''}
        </Typography>
      </Box>
    </Box>
  );
};

export default PollOptionCard;
