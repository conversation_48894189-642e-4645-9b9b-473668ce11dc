import { useState } from 'react';
import {
  Box,
  Typography,
  TextField,
  // useMediaQuery,
  // useTheme,
} from '@mui/material';
import { Image } from '@mui/icons-material';
import FundraiserCardSelector from '../common/FundraiserCardSelector';
import ImageUploader from '../../form-fields/ImageUploader';
import TextInput from '../../form-fields/TextInput';
import { CustomEditorBase } from '../../textEditor/CustomEditorBase';
import CustomRadioButton from '../../form-fields/ShapeOption';
import FormatMediaOptions from '../common/FormatMediaOptions';
import { BannerPreview } from './BannerPreview';
import { LayoutType } from '@minicardiac-client/types';

const FundraiserSection = () => {
  const [selectedLayout, setSelectedLayout] = useState<number>(
    LayoutType.Default
  );
  const [image, setImage] = useState<File | null>(null);
  const [description, setDescription] = useState<string>('');
  const [buttonText, setButtonText] = useState<string>('Donate Now');

  // Radio selection state
  const [donationOption, setDonationOption] = useState<'services' | 'donation'>(
    'services'
  );

  return (
    <Box
      maxWidth="976px"
      width="100%"
      mx="auto"
      p={{ xs: '16px', md: '20px' }}
      borderRadius="8px"
      gap={{ xs: '40px', md: '20px' }}
      bgcolor="#fff"
      display="flex"
      flexDirection="column"
    >
      <Typography fontWeight={600} fontSize="20px">
        Fundraiser
      </Typography>

      {/* Layout */}
      <Box
        display="flex"
        gap={{ xs: '16px', sm: '20px' }}
        mb={3}
        flexDirection={{ xs: 'column', md: 'row' }}
      >
        <Typography fontWeight={600} fontSize="16px">
          Layout:
        </Typography>
        <FundraiserCardSelector
          selectedIndex={selectedLayout}
          onSelect={setSelectedLayout}
        />
      </Box>

      {/* Image + Editor + Button Text */}
      <Box
        sx={{
          display: 'flex',
          gap: { xs: '40px', md: '20px' },
          flexDirection: { xs: 'column', md: 'row' },
        }}
      >
        <ImageUploader
          thumbnail={image}
          onFileChange={(event) => {
            const file = event.target.files?.[0] || null;
            setImage(file);
          }}
          sx={{
            height: '204px',
          }}
        />

        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: { xs: '40px', md: '20px' },
          }}
        >
          <CustomEditorBase
            label="Describe your cause"
            placeholder="Tell your audience what your fundraiser aims to do"
            value={description}
            onChange={setDescription}
            sx={{
              maxWidth: { xs: '100%', md: '400px' },
              width: '100%',
              height: '135px',
              mt: '0px',
            }}
          />

          <TextInput
            label="Call to Action (Button)"
            placeholder="Donate Now"
            value={buttonText}
            onChange={setButtonText}
          />
        </Box>
      </Box>

      {/* Banner Format */}
      <FormatMediaOptions variant="banner" layoutType={selectedLayout} />
      {/* Donation Type */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexWrap: 'wrap',
          gap: '20px',
        }}
      >
        {/* First Radio */}
        <CustomRadioButton
          label="Use MiniCardiac Donation Service"
          value="services"
          checked={donationOption === 'services'}
          onChange={() => setDonationOption('services')}
        />

        {/* Donation Link + Input */}
        <Box
          display="flex"
          alignItems={{ xs: 'flex-start', md: 'center' }}
          gap={'12px'}
          flexDirection={{ xs: 'column', md: 'row' }}
        >
          <CustomRadioButton
            label="Donation Link"
            value="donation"
            checked={donationOption === 'donation'}
            onChange={() => setDonationOption('donation')}
          />
          <TextField
            label="Link"
            placeholder="https:// - Enter donation link here"
            size="medium"
            sx={{ maxWidth: { xs: '100%', md: '375px' }, width: '100%' }}
          />
        </Box>
      </Box>

      {/* Preview Box */}
      <Box
        border="2px solid #A24295"
        borderRadius="12px"
        p={0}
        textAlign="center"
        height="240px"
        bgcolor="#F9F1F7"
        overflow="hidden"
        position="relative"
      >
        {image ? (
          <BannerPreview
            image={image}
            description={description}
            buttonText={buttonText}
            layoutType={selectedLayout}
          />
        ) : (
          <Box
            display="flex"
            justifyContent="center"
            alignItems="center"
            flexDirection="column"
            height="100%"
            px={3}
          >
            <Image sx={{ fontSize: 32, color: '#A24295', mb: 1 }} />
            <Typography fontSize="12px" color="#666" textAlign="center">
              You can see how your banner will appear on your profile once you
              upload your image and add text!
            </Typography>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default FundraiserSection;
