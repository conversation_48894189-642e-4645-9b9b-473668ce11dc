'use client';

import { useEffect, useRef, useState } from 'react';
import { Box, Typography, useMediaQuery } from '@mui/material';
import { useTheme } from '@emotion/react';

const MAX_LINES = 5;
const LINE_HEIGHT_PX = '22px';

export default function AboutSection({ about }: { about: string }) {
  const theme: any = useTheme();
  const isXs = useMediaQuery(theme.breakpoints.down('sm'));

  const contentRef = useRef<HTMLDivElement>(null);
  const [showMore, setShowMore] = useState(false);
  const [showSeeMore, setShowSeeMore] = useState(false);

  useEffect(() => {
    if (contentRef.current && isXs) {
      const lineHeight = parseInt(LINE_HEIGHT_PX, 10);
      const maxHeight = MAX_LINES * lineHeight;
      if (contentRef.current.scrollHeight > maxHeight) {
        setShowSeeMore(true);
      }
    }
  }, [about, isXs]);

  return (
    <Box mt="20px" borderRadius="8px" bgcolor="#fff" maxWidth="976px">
      <Box
        ref={contentRef}
        sx={{
          fontSize: '16px',
          fontWeight: 400,
          lineHeight: LINE_HEIGHT_PX,
          color: '#1E1E1E',
          whiteSpace: 'pre-line',
          overflow: 'hidden',
          display: '-webkit-box',
          WebkitLineClamp: showMore ? 'unset' : MAX_LINES,
          WebkitBoxOrient: 'vertical',
          textOverflow: 'ellipsis',
        }}
      >
        {about}
      </Box>

      {!showMore && showSeeMore && isXs && (
        <Typography
          onClick={() => setShowMore(true)}
          sx={{
            mt: '4px',
            fontSize: '14px',
            fontWeight: 500,
            color: '#A24295',
            cursor: 'pointer',
            userSelect: 'none',
            display: 'inline-block',
          }}
        >
          See more
        </Typography>
      )}
    </Box>
  );
}
