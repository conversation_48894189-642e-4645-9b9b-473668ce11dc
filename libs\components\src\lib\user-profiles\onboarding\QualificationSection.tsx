import { Box, Typography } from '@mui/material';
import CustomForm from './CustomForm';

export const QualificationSection = () => {
  const formData = [
    {
      title: 'Academic Degree',
      rows: [
        {
          columns: 2,
          fields: [
            {
              label: 'Degree',
              placeholder: 'PhD, MD, MBBS',
              sx: { maxWidth: '370px', width: '100%' },
            },
            {
              label: 'Specialisation (optional)',
              placeholder: 'Cardiology',
              sx: { maxWidth: '370px', width: '100%' },
              optional: true,
            },
          ],
        },
        {
          columns: 2,
          fields: [
            {
              label: 'Year of Award',
              placeholder: '2015',
              sx: { maxWidth: '370px', width: '100%' },
            },
            {
              label: 'Institution',
              placeholder: 'Name of your institution',
              sx: { maxWidth: '370px', width: '100%' },
            },
          ],
        },
      ],
      addButtonLabel: 'Degree',
      sx: {
        borderBottom: '1px solid #A3A3A3',
      },
    },
    {
      title: 'Professional Degree',
      rows: [
        {
          columns: 2,
          fields: [
            {
              label: 'Degree',
              placeholder: 'PhD, MD, MBBS',
              sx: { maxWidth: '370px', width: '100%' },
            },
            {
              label: 'Specialisation (optional)',
              placeholder: 'Cardiology',
              sx: { maxWidth: '370px', width: '100%' },
              optional: true,
            },
          ],
        },
        {
          columns: 2,
          fields: [
            {
              label: 'Year of Award',
              placeholder: '2015',
              sx: { maxWidth: '370px', width: '100%' },
            },
            {
              label: 'Institution',
              placeholder: 'Name of your institution',
              sx: { maxWidth: '370px', width: '100%' },
            },
          ],
        },
      ],
      addButtonLabel: 'Degree',
      sx: {
        borderBottom: '1px solid #A3A3A3',
      },
    },

    {
      title: 'Registration and License',
      rows: [
        {
          columns: 2,
          fields: [
            {
              label: 'Registered With',
              placeholder: 'Name of the institution',
              sx: { maxWidth: '370px', width: '100%' },
            },
            {
              label: 'Year of Registration',
              placeholder: '2015',
              sx: { maxWidth: '370px', width: '100%' },
            },
          ],
        },
        {
          columns: 1,
          fields: [
            {
              label: 'Details',
              placeholder: 'Details of registration',
              sx: { maxWidth: '812px', width: '100%' },
            },
          ],
        },
      ],
      addButtonLabel: 'Registration',
    },
  ];

  return (
    <Box
      maxWidth="976px"
      width="100%"
      mx="auto"
      p={{ xs: '16px', sm: '20px' }}
      borderRadius="8px"
      bgcolor="#fff"
    >
      <Typography fontSize="24px" fontWeight={600} mb="20px">
        Qualifications
      </Typography>

      {formData.map((form, idx) => (
        <CustomForm
          key={idx}
          title={form.title}
          rows={form.rows}
          addButtonLabel={`Add ${form.addButtonLabel}`}
          sx={form.sx}
        />
      ))}
    </Box>
  );
};
