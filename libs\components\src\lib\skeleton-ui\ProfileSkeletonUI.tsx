'use client';

import { Box, Skeleton, Card, Divider } from '@mui/material';

export default function ProfileSkeletonUI() {
  return (
    <Box
      p={2}
      maxWidth={900}
      mx="auto"
      display={'flex'}
      flexDirection={'column'}
      gap={'20px'}
    >
      {/* Header Avatar and Cover */}

      <Card sx={{ p: '16px' }}>
        <Box
          position="relative"
          borderRadius={2}
          overflow="hidden"
          height={220}
          mb={3}
        >
          <Skeleton variant="rectangular" height="100%" />
          <Box position="absolute" bottom={16} left={16}>
            <Skeleton variant="circular" width={120} height={120} />
          </Box>
        </Box>

        <Skeleton width="100%" height={30} />
        <Skeleton width="80%" height={20} />
      </Card>

      <Card sx={{ p: '16px' }}>
        {/* Top Section */}
        <Box
          sx={{
            display: 'flex',
            gap: '16px',
            justifyContent: 'space-between',
            alignItems: 'stretch',
            mb: 2,
            height: '300px',
          }}
        >
          {/* Left - Full Height Skeleton */}
          <Skeleton variant="rectangular" width="50%" height="100%" />

          {/* Right - Full Height with stacked skeletons */}
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'space-between',
              width: '50%',
            }}
          >
            <Skeleton width="80%" height={20} />
            <Skeleton width="70%" height={20} />
            <Skeleton width="90%" height={20} />
            <Skeleton width="60%" height={20} />
            <Skeleton width="75%" height={20} />
            <Skeleton width="50%" height={20} />
          </Box>
        </Box>

        {/* Bottom Text Skeletons */}
        <Box>
          <Skeleton width="100%" height={20} />
          <Skeleton width="95%" height={20} />
          <Skeleton width="90%" height={20} />
          <Skeleton width="85%" height={20} />
        </Box>
      </Card>

      <Card sx={{ p: 2 }}>
        {/* Top Text */}
        <Box mb={1}>
          <Skeleton width="30%" height={20} />
          <Skeleton width="50%" height={20} />
        </Box>

        {/* Repeated Items */}
        {[1, 2, 3, 4].map((item, index) => (
          <Box key={index}>
            <Box
              sx={{
                display: 'flex',
                gap: 2,
                alignItems: 'flex-start',
                my: 2,
              }}
            >
              {/* Image Skeleton */}
              <Skeleton variant="rectangular" width={56} height={56} />

              {/* Text Skeletons */}
              <Box sx={{ flexGrow: 1 }}>
                <Skeleton width="80%" height={16} />
                <Skeleton width="60%" height={16} />
                <Skeleton width="70%" height={16} />
              </Box>
            </Box>

            {shouldPlaceDivider(index) && <Divider />}
          </Box>
        ))}
      </Card>
    </Box>
  );
}

function shouldPlaceDivider(index: number): boolean {
  // Place a divider after the first and third items
  return index === 0 || index === 2;
}
