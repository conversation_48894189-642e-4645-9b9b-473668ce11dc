import { useState } from 'react';
import { Box, Typography, Divider } from '@mui/material';
import TextInput from '../../form-fields/TextInput';
import AddSectionButton from '../common/AddSectionButton';

const INITIAL_PHONE_ROW = { title: '', phone: '' };
const INITIAL_EMAIL_ROW = { title: '', email: '' };
const INITIAL_ADDRESS = {
  address1: '',
  address2: '',
  city: '',
  postcode: '',
  country: '',
};

export const GetInTouchSection = () => {
  const [phoneRows, setPhoneRows] = useState([{ ...INITIAL_PHONE_ROW }]);
  const [emailRows, setEmailRows] = useState([{ ...INITIAL_EMAIL_ROW }]);
  const [address, setAddress] = useState({ ...INITIAL_ADDRESS });

  const handlePhoneChange = (
    index: number,
    field: 'title' | 'phone',
    value: string
  ) => {
    const updated = [...phoneRows];
    updated[index][field] = value;
    setPhoneRows(updated);
  };

  const handleEmailChange = (
    index: number,
    field: 'title' | 'email',
    value: string
  ) => {
    const updated = [...emailRows];
    updated[index][field] = value;
    setEmailRows(updated);
  };

  const handleAddressChange = (field: string, value: string) => {
    setAddress((prev) => ({ ...prev, [field]: value }));
  };

  const handleAddPhoneRow = () => {
    setPhoneRows([...phoneRows, { title: '', phone: '' }]);
  };

  const handleAddEmailRow = () => {
    setEmailRows([...emailRows, { title: '', email: '' }]);
  };

  return (
    <Box
      maxWidth="976px"
      width="100%"
      mx="auto"
      p="20px"
      borderRadius="8px"
      bgcolor="#fff"
    >
      {/* Section Title */}
      <Typography fontSize="24px" fontWeight={600} mb="20px">
        Get in Touch
      </Typography>

      <Typography fontSize="16px" fontWeight={400} mb="20px">
        To book a consultation today, send us a message{' '}
        <Box component="span" color="#A24295" fontWeight={600} display="inline">
          right here
        </Box>{' '}
        on MiniCardiac!
      </Typography>

      <Typography fontSize="16px" fontWeight={400} mb="20px">
        Alternatively, here’s how you can find us:
      </Typography>

      {/* ---------------- PHONE SECTION ---------------- */}
      {phoneRows.map((row, rowIndex) => (
        <Box
          key={`phone-${rowIndex}`}
          display="grid"
          gap={{ xs: '20px', sm: '48px' }}
          mb={{ xs: '20px', sm: '40px' }}
          sx={{
            gridTemplateColumns: {
              xs: '1fr',
              md: '1fr 1fr',
            },
          }}
        >
          <TextInput
            label="Title (optional)"
            placeholder="Front Desk"
            value={row.title}
            onChange={(val) => handlePhoneChange(rowIndex, 'title', val)}
          />
          <TextInput
            label="Phone Number"
            placeholder="Enter number"
            value={row.phone}
            onChange={(val) => handlePhoneChange(rowIndex, 'phone', val)}
          />
        </Box>
      ))}
      <AddSectionButton label="Phone Number" onClick={handleAddPhoneRow} />

      <Divider sx={{ my: '20px' }} />

      {emailRows.map((row, rowIndex) => (
        <Box
          key={`email-${rowIndex}`}
          display="grid"
          gap={{ xs: '20px', sm: '48px' }}
          mb={{ xs: '20px', sm: '40px' }}
          sx={{
            gridTemplateColumns: {
              xs: '1fr',
              md: '1fr 1fr',
            },
          }}
        >
          <TextInput
            label="Title (optional)"
            placeholder="Office Email"
            value={row.title}
            onChange={(val) => handleEmailChange(rowIndex, 'title', val)}
          />
          <TextInput
            label="E-mail"
            placeholder="Enter e-mail"
            value={row.email}
            onChange={(val) => handleEmailChange(rowIndex, 'email', val)}
          />
        </Box>
      ))}
      <AddSectionButton label="E-mail" onClick={handleAddEmailRow} />

      <Divider sx={{ my: '20px' }} />

      <Box mb="40px">
        <TextInput
          label="Address Line 1"
          placeholder="Enter address"
          value={address.address1}
          onChange={(val) => handleAddressChange('address1', val)}
          sx={{ mb: { xs: '20px', sm: '40px' } }}
        />
        <TextInput
          label="Address Line 2"
          placeholder="Enter address"
          value={address.address2}
          onChange={(val) => handleAddressChange('address2', val)}
          sx={{ mb: { xs: '20px', sm: '40px' } }}
        />
        <Box
          display="grid"
          gap="20px"
          sx={{
            gridTemplateColumns: {
              xs: '1fr',
              md: '1fr 1fr 1fr',
            },
          }}
        >
          <TextInput
            label="City"
            placeholder="Enter city name"
            value={address.city}
            onChange={(val) => handleAddressChange('city', val)}
          />
          <TextInput
            label="Post Code"
            placeholder="Enter postal code"
            value={address.postcode}
            onChange={(val) => handleAddressChange('postcode', val)}
          />
          <TextInput
            label="Country"
            placeholder="Enter country name"
            value={address.country}
            onChange={(val) => handleAddressChange('country', val)}
          />
        </Box>
      </Box>
    </Box>
  );
};
