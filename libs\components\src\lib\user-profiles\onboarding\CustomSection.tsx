import { useState } from 'react';
import { Box, Typography } from '@mui/material';
import CustomToggleButtonGroup from '../../buttons/CustomToggleButtonGroup';
import { MediaCard } from './MediaCard';
import LargeTextInput from '../../form-fields/LargeTextInput';
import TextInput from '../../form-fields/TextInput';
import MediaTextFormItem from '../../form-fields/MediaTextFormItem';
import FormatMediaOptions from '../common/FormatMediaOptions';

interface FormItem {
  thumbnail: File | null;
  title: string;
  description: string;
}

const CustomSection = () => {
  const [contentType, setContentType] = useState<'MEDIA' | 'TEXT'>('MEDIA');

  const [formItems, setFormItems] = useState<FormItem>({
    thumbnail: null,
    title: '',
    description: '',
  });

  const handleInputChange = <K extends keyof FormItem>(
    field: K,
    value: FormItem[K]
  ) => {
    setFormItems((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <Box
      maxWidth="976px"
      width="100%"
      mx="auto"
      p="20px"
      borderRadius="8px"
      bgcolor="#fff"
      gap="20px"
      display="flex"
      flexDirection="column"
    >
      <CustomToggleButtonGroup
        label="Content Type"
        options={['MEDIA', 'TEXT']}
        selected={contentType}
        onChange={setContentType}
        width="148px"
      />

      {contentType === 'MEDIA' && (
        <>
          <Box
            display="flex"
            mb="12px"
            gap="20px"
            flexDirection={{ xs: 'column', sm: 'row' }}
          >
            <Typography fontWeight={600} fontSize="16px" mr="8px">
              Layout:
            </Typography>

            <Box display="flex" gap="20px" flexWrap="wrap">
              <MediaCard layout="left" />
              <MediaCard layout="right" />
              <MediaCard layout="top" />
            </Box>
          </Box>

          <MediaTextFormItem
            form={formItems}
            titleLabel="Title"
            titlePlaceholder="What would you like to talk about?"
            descriptionLabel="Description"
            descriptionPlaceholder="A short description of your topic"
            mediaBoxSX={{
              maxWidth: '360px',
              width: '360px',
              maxHeight: '360px',
              height: '360px',
            }}
            descriptionHeight="295px"
          />

          <FormatMediaOptions />
        </>
      )}

      {contentType === 'TEXT' && (
        <>
          <TextInput
            label="Title"
            placeholder="What would you like to talk about?"
            value={formItems.title}
            onChange={(value: string) => handleInputChange('title', value)}
          />

          <LargeTextInput
            label="Description"
            placeholder="A short description of your topic"
            value={formItems.description}
            onChange={(value: string) =>
              handleInputChange('description', value)
            }
            sx={{ height: '215px' }}
          />
        </>
      )}
    </Box>
  );
};

export default CustomSection;
