import { useState } from 'react';
import {
  Box,
  Typography,
  useMediaQuery,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  useTheme,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import BannerControls from './BannerControls';
import MediaControls from './MediaControls';
import { LayoutType } from '@minicardiac-client/types';

interface FormatMediaOptionsProps {
  variant?: 'media' | 'banner';

  layoutType?: LayoutType;
}

const FormatMediaOptions = ({
  variant = 'media',

  layoutType = 0,
}: FormatMediaOptionsProps) => {
  const theme = useTheme();
  const isSmDown = useMediaQuery(theme.breakpoints.down('sm'));
  const [expanded, setExpanded] = useState(true);

  const isBanner = variant === 'banner';

  const renderControls = () => {
    if (isBanner) {
      return <BannerControls layoutType={layoutType} />;
    }
    return <MediaControls />;
  };

  if (isSmDown) {
    return (
      <Accordion
        expanded={expanded}
        onChange={() => setExpanded((prev) => !prev)}
        sx={{
          bgcolor: '#F3F4F6',
          borderRadius: '12px',
          border: 'none',
          boxShadow: 'none',
        }}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography fontWeight={600} fontSize="16px">
            {isBanner ? 'Format Banner:' : 'Format Media:'}
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Box display="flex" flexDirection="column" gap="12px">
            {renderControls()}
          </Box>
        </AccordionDetails>
      </Accordion>
    );
  }

  return (
    <Box
      bgcolor="#F3F4F6"
      borderRadius="12px"
      p="12px"
      display="flex"
      alignItems="center"
      gap="12px"
      flexWrap="wrap"
    >
      <Typography fontWeight={600} fontSize="16px" minWidth="120px">
        {isBanner ? 'Format Banner:' : 'Format Media:'}
      </Typography>

      {renderControls()}
    </Box>
  );
};

export default FormatMediaOptions;
