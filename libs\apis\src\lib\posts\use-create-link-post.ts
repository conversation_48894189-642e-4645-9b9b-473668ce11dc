import { useMutation } from '@tanstack/react-query';
import { postApi, type CreateLinkPostRequest } from './post-api.js';

export type { CreateLinkPostRequest };

export const useCreateLinkPost = (options?: {
  onSuccess?: (data: any) => void;
  onError?: (error: any) => void;
}) => {
  return useMutation({
    mutationFn: async (data: CreateLinkPostRequest) => {
      const response = await postApi.createLinkPost({
        ...data,
        community: data.community || 'PUBLIC',
      });
      return response;
    },
    onSuccess: options?.onSuccess,
    onError: options?.onError,
  });
};
