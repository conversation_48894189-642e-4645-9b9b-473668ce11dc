'use client';

import React from 'react';
import {
  <PERSON>,
  Button,
  Container,
  CircularProgress,
  useMediaQuery,
} from '@mui/material';
import { useRouter } from 'next/navigation';
import {
  useSnackbar,
  CustomizedSteppers,
  PatientProfileWelcome,
  BackButton,
  Subtitle,
} from '@minicardiac-client/components';
import DocumentUploadForm from '@/libs/components/src/lib/onboarding/components/Documents/DocumentUpload';
import { useAuth } from '@minicardiac-client/apis';
import { useTheme } from '@emotion/react';

export default function OrganisationPrestigeDocumentUploadPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const { showSuccess, showError } = useSnackbar();

  const { authState } = useAuth();
  const theme: any = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));

  const handleDoThisLater = () => {
    // Navigate to landing page
    router.push('/feed?fromSignup=true');
  };

  const handleContinue = async () => {
    // Set submitting state
    setIsSubmitting(true);
    setError(null);

    try {
      // Post the document data to the API with the correct structure
      // Using the correct endpoint path
      // await axiosInstance.post('/onboarding/document-upload', {
      //   documents: []
      // });

      // Show success message
      showSuccess('Documents saved successfully!');

      // Navigate to the next step in the onboarding flow after a short delay
      setTimeout(() => {
        router.push('/professional/paid/add-network');
      }, 1000);
    } catch (err: any) {
      console.error('Error saving document data:', err);
      setError(err.message || 'Failed to save document data');
      showError(err.message || 'Failed to save document data');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Container maxWidth="lg">
      <Box mt={'20px'}>
        {/* Welcome Header */}
        {!isSmallScreen && (
          <PatientProfileWelcome
            patientName={authState.user?.displayName || ''}
            subtitle={''}
          />
        )}

        <BackButton handleBackButton={() => router.back()} />

        <Subtitle
          text={'Let’s set up your Organisation Account!'}
          sx={{ fontSize: { xs: '12px', sm: '16px' } }}
          marginBottom={'34px'}
        />
        <CustomizedSteppers
          activeStep={1}
          steps={['Profile Setup', 'Document Upload', 'Adding Network']}
        />

        <DocumentUploadForm hideSteppers={true} />

        <Box
          sx={{
            position: 'fixed',
            bottom: 0,
            left: 0,
            width: '100%',
            display: 'flex',
            justifyContent: 'center',
            gap: 2,
            p: { xs: '20px', sm: '36px' },
            bgcolor: 'white',
            zIndex: 1000,
            boxShadow: '0 -4px 10px rgba(0,0,0,0.1)',
          }}
        >
          {/* Do this later button */}
          <Button
            variant="outlined"
            onClick={handleDoThisLater}
            disabled={isSubmitting}
            sx={{
              width: '172px',
              height: '40px',
              borderRadius: '8px',
              color: 'secondary.main',
              borderColor: 'secondary.main',
              textTransform: 'none',
              '&:hover': {
                borderColor: 'secondary.dark',
                backgroundColor: 'secondary.light',
              },
            }}
          >
            Do this later
          </Button>

          {/* Save and Continue button */}
          <Button
            variant="contained"
            onClick={handleContinue}
            disabled={isSubmitting}
            sx={{
              width: '225px',
              height: '40px',
              borderRadius: '8px',
              fontFamily: "'Plus Jakarta Sans', sans-serif",
              fontWeight: 700,
              fontSize: '16px',
              lineHeight: '24px',
              textTransform: 'none',
              padding: '0 40px',
              backgroundColor: '#A3A3A3', // Neutral color
              color: '#FFFFFF',
              '&:hover': {
                backgroundColor: '#737678', // Darker gray for hover
              },
            }}
          >
            {isSubmitting ? (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <CircularProgress size={20} color="inherit" />
                Saving...
              </Box>
            ) : (
              'Save and Continue'
            )}
          </Button>
        </Box>

        {error && (
          <Box sx={{ color: 'error.main', mt: 2, textAlign: 'center' }}>
            {error}
          </Box>
        )}
      </Box>
    </Container>
  );
}
