// Supported video file extensions
export const SUPPORTED_VIDEO_EXTENSIONS = ['mp4', 'webm', 'ogg', 'mov'] as const;

// Supported audio file extensions
export const SUPPORTED_AUDIO_EXTENSIONS = ['mp3', 'wav', 'aac', 'm4a'] as const;

// Supported video MIME types
export const SUPPORTED_VIDEO_MIME_TYPES = {
  'video/mp4': [],
  'video/webm': [],
  'video/ogg': [],
  'video/quicktime': [],
} as const;

// Supported audio MIME types
export const SUPPORTED_AUDIO_MIME_TYPES = {
  'audio/mpeg': [],
  'audio/wav': [],
  'audio/aac': [],
  'audio/mp4': [],
  'audio/x-m4a': [],
} as const;

export type VideoExtension = typeof SUPPORTED_VIDEO_EXTENSIONS[number];
export type AudioExtension = typeof SUPPORTED_AUDIO_EXTENSIONS[number];

export type VideoMimeType = keyof typeof SUPPORTED_VIDEO_MIME_TYPES;
export type AudioMimeType = keyof typeof SUPPORTED_AUDIO_MIME_TYPES;

export const SUPPORTED_MEDIA_EXTENSIONS = {
  ...SUPPORTED_VIDEO_EXTENSIONS,
  ...SUPPORTED_AUDIO_EXTENSIONS,
} as const;

export type MediaExtension = VideoExtension | AudioExtension;
