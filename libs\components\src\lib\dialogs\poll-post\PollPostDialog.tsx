'use client';

import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Stack,
  useMediaQuery,
  Menu,
  MenuItem,
} from '@mui/material';
import CustomDialog from '../CustomDialog';
import { LoadingButton } from '../../loading-button';
import { Iconify } from '../../iconify';
import { toast } from 'react-toastify';
import PostToast from '../../toast/PostToast';
import { CustomizedSteppers } from '../../onboarding';
import { BackButton } from '../../buttons/Backbutton';
import { useTheme } from '@emotion/react';
import { PostButton } from '../../buttons/PostButton';
import PollPostDetailsDialog from './PollPostDetailsDialog';
import PollPostForm from './PollPostForm';
import { useTranslations } from 'next-intl';
import { useCreatePollPost, useAuthStore } from '@minicardiac-client/apis';
import SchedulePostDialog from '../ScheduleDialog';
import { getDecodedToken } from '@minicardiac-client/utilities';
import { PUBLIC_FORUM_TAG } from './constants';

interface PollPostDialogProps {
  open: boolean;
  onClose: () => void;
  setOpenScheduleDialog: () => void;
  content: string;
  setContent: (content: string) => void;
}

const DEFAULT_OPTIONS = [
  { id: 1, label: 'Option 1', value: '', placeholder: 'Yes' },
  { id: 2, label: 'Option 2', value: '', placeholder: 'No' },
];

const PollPostDialog = ({
  open,
  onClose,
  setOpenScheduleDialog,
}: PollPostDialogProps) => {
  const t = useTranslations('pollPost');

  const [activeStep, setActiveStep] = useState(0);
  const [pollQuestion, setPollQuestion] = useState('');
  const [options, setOptions] = useState(DEFAULT_OPTIONS);
  const [allowCustomAnswers, setAllowCustomAnswers] = useState(false);
  const [caption, setCaption] = useState('');
  const [tags, setTags] = useState('');
  const { selectedProfessionalType } = useAuthStore();
  const [audience, setAudience] = useState<'PROFESSIONAL' | 'PUBLIC' | 'BOTH'>('PROFESSIONAL');
  const [speciality, setSpeciality] = useState<'CARDIAC_SURGEON' | 'CARDIOLOGIST' | 'BOTH'>(
    selectedProfessionalType === 'CARDIOLOGIST' ? 'CARDIOLOGIST' : 'CARDIAC_SURGEON'
  );

  // Check if user is public
  const [isPublicUser, setIsPublicUser] = useState(false);

  // Set initial audience and specialty based on user type
  useEffect(() => {
    const checkUserType = async () => {
      try {
        const decodedToken = await getDecodedToken();
        const isPublic = decodedToken?.accountType === 'PUBLIC';

        setIsPublicUser(isPublic);


        if (isPublic) {
          setTags(PUBLIC_FORUM_TAG);
          setAudience('PUBLIC');
        } else {
          // Reset tags for non-public users
          setTags('');
          // Set initial specialty based on selectedProfessionalType
          if (selectedProfessionalType) {
            setSpeciality(selectedProfessionalType === 'CARDIOLOGIST' ? 'CARDIOLOGIST' : 'CARDIAC_SURGEON');
          }
        }
      } catch (error) {
        console.error('Error decoding token:', error);
        setIsPublicUser(false);
      }
    };

    if (open) {
      checkUserType();
    }
  }, [open, selectedProfessionalType]);
  const [duration, setDuration] = useState<number>(0);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  // Scheduling state
  const [scheduleDialogOpen, setScheduleDialogOpen] = useState(false);

  const theme: any = useTheme();
  const screenBelowSM = useMediaQuery(theme.breakpoints.down('sm'));

  const filledOptions = options.filter((opt) => opt.value.trim() !== '');
  const isNextEnabled = pollQuestion.trim() && filledOptions.length >= 2;

  const handleNext = () => setActiveStep(activeStep + 1);
  const handleBack = () => setActiveStep(activeStep - 1);

  const canPost = Boolean(caption.trim() && duration);

  const { mutate: createPollPost } = useCreatePollPost({
    onSuccess: () => {
      onClose();
      toast(<PostToast value={t('pollPosted')} />, {
        position: 'bottom-right',
        autoClose: 5000,
        hideProgressBar: true,
        closeButton: false,
        style: {
          padding: 0,
          width: 'fit-content',
          background: 'white',
          boxShadow: '0px 2px 10px rgba(0, 0, 0, 0.1)',
        },
      });
    },
  });

  // Helper to determine post status
  const getPostStatus = (scheduleDate?: string): 'draft' | 'published' | 'scheduled' => {
    return scheduleDate ? 'scheduled' : 'published';
  };

  // Helper to build post data
  const buildPostData = (status: 'draft' | 'published' | 'scheduled', scheduleDate?: string) => {
    // For public users, use different payload structure
    if (isPublicUser) {
      return {
        question: pollQuestion,
        options: options.map((opt) => opt.value.trim()).filter(Boolean),
        allowCustomAnswer: allowCustomAnswers,
        content: caption.trim(),
        tags: ['PublicForum'],
        community: 'PUBLIC' as const,
        audience: 'BOTH' as const, // Default for public users
        postStatus: status,
        durationDays: duration,
        ...(scheduleDate ? { postScheduleDate: scheduleDate } : {}),
      };
    }


    return {
      question: pollQuestion,
      options: options.map((opt) => opt.value.trim()).filter(Boolean),
      allowCustomAnswer: allowCustomAnswers,
      content: caption.trim(),
      tags: tags
        .split(',')
        .map((tag) => tag.trim())
        .filter(Boolean),
      community: audience as 'PROFESSIONAL' | 'PUBLIC' | 'BOTH',
      audience: speciality as 'CARDIAC_SURGEON' | 'CARDIOLOGIST' | 'BOTH',
      postStatus: status,
      durationDays: duration,
      ...(scheduleDate ? { postScheduleDate: scheduleDate } : {}),
    };
  };

  // Handle scheduling
  const handleSchedule = (isoDate: string) => {
    setScheduleDialogOpen(false);
    handlePostWithSchedule(isoDate);
  };

  const handlePostWithSchedule = (isoDate: string) => {
    const postData = buildPostData(getPostStatus(isoDate), isoDate);
    createPollPost(postData);
  };

  const handlePost = () => {
    const postData = buildPostData(getPostStatus(), undefined);
    createPollPost(postData);
  };

  const handleSaveDraft = () => {
    const postData = buildPostData('draft', undefined);
    createPollPost(postData);
  };

  return (
    <CustomDialog
      open={open}
      onClose={onClose}
      title=""
      sx={{
        p: 0,
        alignItems: { xs: 'stretch', sm: 'start' },
        px: { xs: '0px', sm: '30px', md: '50px', lg: '257px' },
        pt: { xs: 0, sm: '50px' },
        '.MuiDialog-paper': {
          maxHeight: { xs: '100%', sm: 'calc(100% - 64px)' },
          width: { xs: '100%', sm: '766px' },
          maxWidth: { xs: '100%', sm: '766px' },
        },
      }}
    >
      <Box
        display="flex"
        flexDirection="column"
        flex={1}
        bgcolor="white"
        height={{ xs: '100%', sm: '716px' }}
        width={{ xs: '100%', sm: '766px' }}
        mb={{ xs: '100px', sm: '140px', md: '20px' }}
      >
        <Box
          display="flex"
          justifyContent={{ xs: 'space-between', sm: 'center' }}
          alignItems="center"
          paddingX={{ xs: '16px', sm: '40px' }}
          pt={{ xs: '16px', sm: '40px' }}
          pb={'20px'}
        >
          <Box display="flex" gap="16px" alignItems="center" height={'35px'}>
            {screenBelowSM && <BackButton onClick={onClose} />}
            <Typography
              sx={{
                fontFamily: 'Plus Jakarta Sans',
                fontWeight: 500,
                fontSize: { xs: '20px', sm: '28px' },
                color: '#1E1E1E',
              }}
            >
              {t('newPollPost')}
            </Typography>
          </Box>

          <Typography
            sx={{
              position: 'absolute',
              right: 40,
              top: { xs: 10, sm: 40 },
              fontFamily: 'Plus Jakarta Sans',
              fontWeight: 600,
              fontSize: '16px',
              color: '#A24295',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
            }}
          >
            {t('drafts')} <Iconify icon="solar:arrow-right-linear" />
          </Typography>
        </Box>

        <CustomizedSteppers
          activeStep={activeStep}
          steps={[t('createPoll'), t('addDetails')]}
        />

        <Box flex={1} overflow="auto" px={{ xs: '16px', sm: '40px' }}>
          {activeStep === 0 ? (
            <PollPostForm
              pollQuestion={pollQuestion}
              setPollQuestion={setPollQuestion}
              allowCustomAnswers={allowCustomAnswers}
              setAllowCustomAnswers={setAllowCustomAnswers}
              options={options}
              setOptions={setOptions}
            />
          ) : (
            <PollPostDetailsDialog
              caption={caption}
              setCaption={setCaption}
              tags={tags}
              setTags={setTags}
              audience={audience}
              setAudience={setAudience}
              speciality={speciality}
              setSpeciality={setSpeciality}
              duration={duration}
              setDuration={setDuration}
              isPublicUser={isPublicUser}
            />
          )}
        </Box>
      </Box>

      <Box
        sx={{
          boxShadow: '0 -4px 20px rgba(0,0,0,0.1)',
          bgcolor: 'white',
          backdropFilter: 'blur(20px)',
          p: '24px',
          position: { xs: 'fixed', sm: 'static' },
          bottom: 0,
          left: 0,
          width: '100%',
          alignItems: 'center',
          display: 'flex',
        }}
      >
        <Stack
          direction={{ xs: 'column', sm: 'row' }}
          spacing={{ xs: 1, sm: 2 }}
          justifyContent="center"
          alignItems={'center'}
          display={'flex'}
          width={'100%'}
        >
          <LoadingButton
            variant="outlined"
            onClick={activeStep ? handleBack : onClose}
            sx={{
              width: '156px',
              height: '40px',
              fontSize: '16px',
              fontWeight: 700,
              color: '#A24295',
              backgroundColor: 'white',
              borderColor: '#A24295',
              '&:hover': { backgroundColor: 'secondary.light' },
            }}
          >
            {t('cancel')}
          </LoadingButton>

          {activeStep === 0 ? (
            <LoadingButton
              onClick={handleNext}
              variant="contained"
              disabled={!isNextEnabled}
              sx={{
                width: '156px',
                height: '40px',
                backgroundColor: isNextEnabled ? '#A24295' : '#ccc',
                color: 'white',
                fontSize: '16px',
                fontWeight: 700,
                '&:hover': {
                  backgroundColor: isNextEnabled ? '#8d2a7b' : '#ccc',
                },
              }}
            >
              {t('next')}
            </LoadingButton>
          ) : (
            <PostButton
              setAnchorEl={setAnchorEl}
              handlePost={handlePost}
              disabled={!canPost}
              isOpen={Boolean(anchorEl)}
            />
          )}

          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={() => setAnchorEl(null)}
          >
            <MenuItem
              onClick={() => {
                setScheduleDialogOpen(true);
                setAnchorEl(null);
              }}
            >
              {t('schedule')}
            </MenuItem>
            <MenuItem
              onClick={() => {
                handleSaveDraft();
                setAnchorEl(null);
              }}
            >
              {t('saveDraft')}
            </MenuItem>
            <MenuItem onClick={() => setAnchorEl(null)}>
              {t('sponsorshipQueue')}
            </MenuItem>
          </Menu>
        </Stack>
      </Box>

      {/* Schedule Dialog */}
      <SchedulePostDialog
        open={scheduleDialogOpen}
        onClose={() => setScheduleDialogOpen(false)}
        onSchedule={handleSchedule}
      />
    </CustomDialog>
  );
};

export default PollPostDialog;
