'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@minicardiac-client/apis';
import { AuthLayout, OtpVerification } from '@minicardiac-client/components';

export default function VerifyOtpPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [email, setEmail] = useState<string>('');
  const [displayName, setDisplayName] = useState<string>('');

  const { verifyOtp, regenerateOtp, signInWithCustomToken } = useAuth();

  useEffect(() => {
    if (searchParams) {
      const emailParam = searchParams.get('email');
      const nameParam = searchParams.get('name');

      if (emailParam) {
        setEmail(emailParam);
      }

      if (nameParam) {
        setDisplayName(nameParam);
      }
    }
  }, [searchParams]);

  const handleVerify = async (otp: string) => {
    if (!email) return;

    try {
      const response = await verifyOtp({ email, otp });

      if (response.customToken) {
        await signInWithCustomToken(response.customToken);

        // Redirect to profile setup page
        const hostname = window.location.origin;
        const profileUrl = `${hostname}/patient/profile`;
        window.location.href = profileUrl;
      }
    } catch (error) {
      console.error('OTP verification failed:', error);
    }
  };

  const handleResendOtp = async () => {
    if (!email) return Promise.reject(new Error('Email is required'));

    try {
      // Use the regenerateOtp function from the auth context (uses TanStack Query)
      await regenerateOtp({ email });
      return Promise.resolve();
    } catch (error) {
      console.error('Failed to resend OTP:', error);
      return Promise.reject(error);
    }
  };

  if (!email) {
    return (
      <AuthLayout
        showCarousel={true}
        showBackButton={true}
        onBackClick={() => router.back()}
      >
        <div>Missing email parameter. Please go back to the sign-up page.</div>
      </AuthLayout>
    );
  }

  return (
    <AuthLayout
      showCarousel={true}
      showBackButton={true}
      onBackClick={() => router.back()}
    >
      <OtpVerification
        email={email}
        displayName={displayName}
        onVerify={handleVerify}
        onResendOtp={handleResendOtp}
      />
    </AuthLayout>
  );
}
