'use client';

import { Box, Typography } from '@mui/material';
import { Award } from '@minicardiac-client/types';
import EmptyAboutFallback from './EmptyAboutFallback';
import Image from 'next/image';

export default function VideoAndAwardsSection({
  video,
  awards,
}: {
  video?: string;
  awards?: Award[];
}) {
  return (
    <Box display="flex" gap="20px" flexWrap="wrap" justifyContent={'center'}>
      {video ? (
        <Box
          width="100%"
          maxWidth={{ xs: '100%', lg: '516px' }}
          height="260px"
          borderRadius="8px"
          overflow="hidden"
          bgcolor="#000"
        >
          <video
            src={video}
            width="100%"
            height="100%"
            controls
            aria-label={'Video content'}
          />
        </Box>
      ) : (
        <Box
          maxWidth="516px"
          width="100%"
          display={{ xs: 'none', sm: 'block' }}
        >
          <Image
            src="/assets/user-profile/placeholder-video-image.png"
            alt="Empty About Illustration"
            width={516}
            height={272}
            style={{ width: '100%', height: 'auto' }}
          />
        </Box>
      )}

      {awards && awards.length > 0 ? (
        <Box
          flex="1"
          display="flex"
          flexDirection={{ xs: 'column', smd: 'column' }}
          gap="16px"
        >
          {awards.map((award, idx) => (
            <Box
              key={idx}
              display="flex"
              gap="12px"
              minHeight="86px"
              borderRadius="8px"
              padding="12px"
              border="1px solid #A24295"
              alignItems="center"
              position="relative"
              overflow="hidden"
            >
              {/* Background Circle */}
              <Box
                sx={{
                  position: 'absolute',
                  width: 130,
                  height: 124,
                  backgroundColor: '#F6ECF4',
                  borderRadius: '50%',
                  left: -30,
                  top: '50%',
                  transform: 'translateY(-50%)',
                  zIndex: 0,
                }}
              />

              {/* Icon with higher z-index */}
              <Box sx={{ zIndex: 1, ml: '10px' }}>
                <Box
                  component="img"
                  src={award.icon}
                  alt="Award"
                  sx={{
                    width: 48,
                    height: 48,
                    objectFit: 'contain',
                  }}
                />
              </Box>

              {/* Content */}
              <Box sx={{ zIndex: 1, ml: 6 }}>
                <Typography fontSize="12px" fontWeight={300} color="#737678">
                  {award.recipient}
                </Typography>
                <Typography fontSize="16px" fontWeight={500}>
                  {award.title}
                </Typography>
                <Typography fontSize="12px" fontWeight={400}>
                  {award.description}
                </Typography>
              </Box>
            </Box>
          ))}
        </Box>
      ) : (
        <EmptyAboutFallback />
      )}
    </Box>
  );
}
