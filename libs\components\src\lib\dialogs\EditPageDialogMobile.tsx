'use client';

import {
  DndContext,
  closestCenter,
  DragEndEvent,
  useSensor,
  useSensors,
  PointerSensor,
} from '@dnd-kit/core';
import {
  SortableContext,
  arrayMove,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

import {
  Box,
  Button,
  Dialog,
  DialogContent,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { useState } from 'react';
import { Iconify } from '../iconify';
import { useTheme } from '@emotion/react';
import { FIXED_SECTION, INITIAL_SECTIONS } from '@minicardiac-client/types';

const SortableSection = ({ id }: { id: string }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    backgroundColor: '#fff',
    borderRadius: '8px',
    padding: '16px',
    fontWeight: 600,
    fontSize: '16px',
    color: '#1E1E1E',
    border: '1px solid #A24295',
    cursor: 'grab',
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    opacity: isDragging ? 0.6 : 1,
  };

  return (
    <Box ref={setNodeRef} sx={style} {...attributes} {...listeners}>
      <Iconify
        icon="sidekickicons:dots-2x3"
        width={24}
        height={24}
        sx={{ color: '#A24295' }}
      />
      {id}
    </Box>
  );
};

const EditPageDialogMobile = ({
  open,
  onClose,
}: {
  open: boolean;
  onClose: () => void;
}) => {
  const theme: any = useTheme();
  const isAboveSm = useMediaQuery(theme.breakpoints.up('sm'));

  const [sections, setSections] = useState(INITIAL_SECTIONS);

  const sensors = useSensors(useSensor(PointerSensor));

  // Replace with api logic while api integration
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    if (!over || active.id === FIXED_SECTION || over.id === FIXED_SECTION)
      return;

    const oldIndex = sections.indexOf(active.id as string);
    const newIndex = sections.indexOf(over.id as string);
    setSections((items) => arrayMove(items, oldIndex, newIndex));
  };

  if (isAboveSm) {
    return null;
  }

  return (
    <Dialog
      open={open}
      onClose={onClose}
      hideBackdrop
      PaperProps={{
        sx: {
          position: 'fixed',
          bottom: -30,
          left: 0,
          right: 0,
          mx: 'auto',
          width: '100%',
          borderTopLeftRadius: '40px',
          borderTopRightRadius: '40px',
          height: '664px',
          backgroundColor: '#fff',
          zIndex: 2000,
        },
      }}
    >
      <DialogContent
        sx={{
          padding: '16px',
          pt: '47px',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Typography fontSize={24} fontWeight={600} mb={2}>
          Jump To / Re-Order
        </Typography>
        <Typography fontSize={14} color="#6B7280" mb={4}>
          Tap on a section to jump to it, or hold and drag to re-order them.
        </Typography>

        <Box
          display="flex"
          flexDirection="column"
          gap="16px"
          overflow="auto"
          flexGrow={1}
          sx={{
            '&::-webkit-scrollbar': { display: 'none' },
            scrollbarWidth: 'none',
            width: '100%',
          }}
        >
          <Box
            sx={{
              backgroundColor: 'white',
              borderRadius: '8px',
              padding: '16px',
              fontWeight: 600,
              fontSize: '16px',
              color: '#1E1E1E',
              border: '1px solid #A24295',
            }}
          >
            About
          </Box>

          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext
              items={sections.filter((s) => s !== 'About')}
              strategy={verticalListSortingStrategy}
            >
              {sections
                .filter((section) => section !== 'About')
                .map((section) => (
                  <SortableSection key={section} id={section} />
                ))}
            </SortableContext>
          </DndContext>
        </Box>

        <Box mt={4}>
          <Button
            fullWidth
            onClick={onClose}
            sx={{
              height: '40px',
              fontWeight: 700,
              color: '#A24295',
              '&:hover': { backgroundColor: '#f9f0f5' },
            }}
          >
            Cancel
          </Button>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default EditPageDialogMobile;
