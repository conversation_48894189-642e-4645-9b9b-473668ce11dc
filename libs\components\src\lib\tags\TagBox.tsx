'use client';

import { Box, Button, Typography, useMediaQuery } from '@mui/material';
import PostCard from '../content-posts/PostCard';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { SxProps } from '@mui/system';
import { useTheme } from '@emotion/react';
import { TagPost } from '@minicardiac-client/types';
import { ExtendedTheme } from '../auth';

interface FollowButtonProps {
  isFollowing: boolean;
  onClick: () => void;
}

interface TagBoxProps {
  posts: TagPost[];
  tagName: string;
  sx?: SxProps;
  followButtonProps?: FollowButtonProps;
  following?: boolean;
}

export default function TagBox({
  posts,
  tagName,
  sx,
  followButtonProps,
  following = false,
}: TagBoxProps) {
  const theme = useTheme() as ExtendedTheme;
  const router = useRouter();
  const isSmallOrMedium = useMediaQuery(theme.breakpoints.down('lg'));

  const [isFollowing, setIsFollowing] = useState(following);

  const handleFollowClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent box click
    if (followButtonProps) {
      setIsFollowing((prev) => !prev);
      followButtonProps.onClick();
    }
  };

  const handleNavigate = () => {
    router.push(`/feed?hashtag=${encodeURIComponent(tagName)}`);
  };

  useEffect(() => {
    setIsFollowing((prev) => {
      if (prev !== following) return following;
      return prev;
    });
  }, [following]);

  return (
    <Box
      onClick={handleNavigate}
      sx={{
        backgroundColor: '#F8F9FA',
        borderRadius: '8px',
        padding: '16px',
        transition: 'background-color 0.2s ease',
        '&:hover': {
          backgroundColor: '#F6ECF4',
        },
        width: '100%',
        cursor: 'pointer',
        ...sx,
      }}
    >
      {/* Tag name and follow button row */}
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={2}
      >
        <Typography fontWeight={700} fontSize="16px" color="#A24295">
          #{tagName}
        </Typography>

        {followButtonProps && (
          <Button
            onClick={handleFollowClick}
            variant="outlined"
            sx={{
              height: '28px',
              borderRadius: '8px',
              borderColor: '#A24295',
              color: '#A24295',
              textTransform: 'none',
              fontWeight: 700,
              fontSize: '12px',
              minWidth: 'auto',
              padding: '0 12px',
              lineHeight: 1.5,
            }}
          >
            {isFollowing ? 'Following' : 'Follow'}
          </Button>
        )}
      </Box>

      {/* Post previews */}
      <Box
        sx={{
          display: 'flex',
          gap: '16px',
          justifyContent: { xs: 'space-around', md: 'space-between' },
          overflowX: isSmallOrMedium ? 'auto' : 'visible',
          paddingBottom: '4px',
          scrollbarWidth: 'none',
          msOverflowStyle: 'none',
          '&::-webkit-scrollbar': {
            display: 'none',
          },
        }}
      >
        {posts.map((post, i) => (
          <PostCard key={post.postId || i} post={post} />
        ))}
      </Box>
    </Box>
  );
}
