import { TableRow, TableCell, MenuItem, Box } from '@mui/material';
import { CustomPopover } from '@minicardiac-client/components';
import { Children } from 'react';
import { Link } from 'react-router-dom';

type HeadLabel = {
  field?: string;
  field_mod?: string;
  width?: number | string;
  minWidth?: number | string;
  align?: 'left' | 'right' | 'center' | 'inherit' | 'justify';
};

type UsersTableRowProps = {
  row: Record<string, string>;
  headLabel: HeadLabel[];
};

export default function UsersTableRow({ row, headLabel }: UsersTableRowProps) {
  const tableActions = (
    <TableCell align="right" sx={{ px: 1, whiteSpace: 'nowrap' }}>
      <CustomPopover>
        <>
          <MenuItem onClick={() => console.log('Approved')}>Approve</MenuItem>
          <MenuItem onClick={() => console.log('Rejected')}>Reject</MenuItem>
          <MenuItem onClick={() => console.log('Approved')}>Edit</MenuItem>
        </>
      </CustomPopover>
    </TableCell>
  );

  return (
    <TableRow hover>
      {Children.toArray(
        headLabel.map((headCell) => {
          if (headCell?.field) {
            const key = headCell?.field_mod || headCell?.field;
            const cellValue = key ? row?.[key] : '';

            if (headCell.field === 'name') {
              return (
                <TableCell
                  sx={{
                    whiteSpace: 'nowrap',
                    width: headCell?.width,
                    minWidth: headCell?.minWidth,
                  }}
                  align={headCell?.align || 'left'}
                >
                  <Box
                    component="span"
                    sx={{
                      '& a': {
                        textDecoration: 'none',
                        color: 'black',
                        '&:hover': {
                          textDecoration: 'underline',
                        },
                      },
                    }}
                  >
                    <Link to={`/admin/pending-verifications/12`}>
                      {cellValue}
                    </Link>
                  </Box>
                </TableCell>
              );
            }

            return (
              <TableCell
                sx={{
                  whiteSpace: 'nowrap',
                  width: headCell?.width,
                  minWidth: headCell?.minWidth,
                }}
                align={headCell?.align || 'left'}
              >
                {cellValue}
              </TableCell>
            );
          }
          return tableActions;
        })
      )}
    </TableRow>
  );
}
