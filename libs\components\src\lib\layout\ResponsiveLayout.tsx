'use client';
import { Box, useMediaQuery } from '@mui/material';
import Sidebar from '../navigations/Sidebar';
import BottomNavBar from '../navigations/BottomNavBar';
import { useTheme } from '@emotion/react';
import React, { useRef, useEffect } from 'react';
import { useAuth, usePostDialogStore } from '@minicardiac-client/apis';
import JoinSignupBanner from './JoinSignupBanner';
import ActivePostDialogRenderer from './ActivePostDialogRenderer';

type ResponsiveLayoutProps = {
  topBar?: React.ReactNode;
  children: React.ReactNode;
  rightSidebar?: React.ReactNode;
  handlePostCreated?: () => void;
};

function ResponsiveLayout({
  topBar,
  children,
  rightSidebar,
  handlePostCreated,
}: ResponsiveLayoutProps) {
  const theme: any = useTheme();
  const { authState } = useAuth();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const mainContentRef = useRef<HTMLDivElement>(null);
  const { activeDialog } = usePostDialogStore();

  // Global scroll handler to always scroll the main content area
  useEffect(() => {
    if (isMobile) return; // Don't apply on mobile

    const handleWheel = (e: WheelEvent) => {
      // Check if any modal/dialog is open by looking for common modal indicators
      const hasOpenModal =
        activeDialog !== null ||
        document.querySelector('[role="dialog"]') ||
        document.querySelector('.MuiModal-root') ||
        document.querySelector('.MuiDialog-root') ||
        document.querySelector('[data-testid="modal"]') ||
        document.body.style.overflow === 'hidden';

      // If modal is open, don't intercept scroll - let it handle naturally
      if (hasOpenModal) {
        return;
      }

      const mainContent = mainContentRef.current;
      if (!mainContent) return;

      if (mainContent.contains(e.target as Node)) {
        return;
      }

      // For all other areas (sidebars, empty spaces), prevent default and scroll main content
      e.preventDefault();
      mainContent.scrollTop += e.deltaY;
    };

    // Add wheel event listener to the entire document for LinkedIn-style scrolling
    document.addEventListener('wheel', handleWheel, { passive: false });

    return () => {
      document.removeEventListener('wheel', handleWheel);
    };
  }, [isMobile, activeDialog]);

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          flexDirection: isMobile ? 'column' : 'row',
          height: '100vh',
          width: '100%',
          justifyContent: 'center',
          backgroundColor: '#F3F4F6',
          position: 'relative',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            gap: { xs: '0px', sm: '20px', md: '20px', lg: '40px' },
            width: { xs: '100%', lg: '1280px' },
          }}
        >
          {/* Sidebar */}
          {!isMobile && (
            <Box
              sx={{
                height: '100vh',
                overflowY: 'hidden', // Prevent sidebar from scrolling independently
                backgroundColor: 'white',
                width: { sm: 100, xs: 100, md: 224 },
                minWidth: { sm: 100, xs: 100, md: 224 },
                scrollbarWidth: 'none',
                '&::-webkit-scrollbar': {
                  display: 'none',
                },
              }}
            >
              <Sidebar />
            </Box>
          )}

          {/* Main Section */}
          <Box
            sx={{
              display: 'flex',
              flex: 1,
              flexDirection: 'column',
              overflow: 'hidden',
              gap: '20px',
              position: 'relative',
            }}
          >
            {/* Top Bars */}
            <Box
              sx={{
                position: 'fixed',
                top: 0,
                zIndex: 100,
                width: '100%',
                backgroundColor: '#F3F4F6',
                pr: { sm: '140px', md: '244px', lg: '0px' },
              }}
            >
              {topBar}
            </Box>

            {/* Scrollable Main Content */}
            <Box
              ref={mainContentRef}
              sx={{
                overflowY: 'auto',
                width: { smd: '100%', lg: '100%' },
                pr: { sm: '20px', lg: '0px' },
                scrollbarWidth: 'none',
                '&::-webkit-scrollbar': {
                  display: 'none',
                },
                height: '100vh',
                mt: '40px',
              }}
            >
              {children}
            </Box>
          </Box>

          {/* Right Sidebar */}
          {!isMobile && (
            <Box
              sx={{
                height: '100vh',
                overflowY: 'hidden', // Prevent right sidebar from scrolling independently
                width: 'auto',
              }}
            >
              {rightSidebar}
            </Box>
          )}

          {!authState.isAuthenticated && <JoinSignupBanner />}

          {/* Bottom Nav - Mobile Only */}
          {isMobile && (
            <Box
              sx={{
                position: 'sticky',
                bottom: 0,
                zIndex: 10,
                backgroundColor: 'white',
              }}
            >
              <BottomNavBar />
            </Box>
          )}
        </Box>
      </Box>

      <ActivePostDialogRenderer onPostCreated={handlePostCreated} />
    </>
  );
}

export default React.memo(ResponsiveLayout);
