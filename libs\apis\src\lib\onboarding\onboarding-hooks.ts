import { useMutation } from '@tanstack/react-query';
import { axiosInstance } from '../http-client.js';

// Define query keys for onboarding
export const onboardingQueryKeys = {
  all: ['onboarding'] as const,
};

// API function to complete networking stage
export const completeNetworkingStage = async (): Promise<string> => {
  const response = await axiosInstance.post(
    '/onboarding/complete-networking-stage'
  );
  return response.data;
};

// Hook to complete networking stage
export const useCompleteNetworkingStage = () => {
  return useMutation({
    mutationFn: completeNetworkingStage,
  });
};
