import { Box, Typography } from '@mui/material';
import RoomIcon from '@mui/icons-material/Room'; // Pin icon

export const ContactCard = () => {
  return (
    <Box mt="20px" display="flex" p={{ xs: '16px', sm: '0px' }}>
      <Box
        maxWidth="976px"
        width="100%"
        bgcolor="white"
        p="20px"
        borderRadius="8px"
      >
        {/* Title */}
        <Typography fontSize="20px" fontWeight={600} mb="20px">
          Get in Touch!
        </Typography>

        {/* Description 1 */}
        <Typography fontSize="16px" fontWeight={400} mb="20px">
          To book a consultation today, send us a message{' '}
          <Box
            component="span"
            fontWeight={600}
            color="#A24295"
            display="inline"
          >
            right here
          </Box>{' '}
          on MiniCardiac!
        </Typography>

        {/* Description 2 */}
        <Typography fontSize="16px" fontWeight={400} mb="20px">
          Alternatively, here’s how you can find us:
        </Typography>

        {/* Contact Info Boxes Side by Side */}
        <Box
          display="flex"
          flexDirection={{ xs: 'column', sm: 'row' }}
          gap="20px"
          flexWrap={'wrap'}
        >
          {/* Contact Details Box */}
          <Box
            p="20px"
            bgcolor="#F8F9FA"
            borderRadius="8px"
            flex="1"
            display="flex"
            flexDirection="column"
            gap="12px"
          >
            <Box display="flex" gap="8px">
              <Typography fontSize="16px" fontWeight={700} minWidth="100px">
                Front Desk:
              </Typography>
              <Typography fontSize="16px" fontWeight={400}>
                457239014317
              </Typography>
            </Box>

            <Box display="flex" gap="8px">
              <Typography fontSize="16px" fontWeight={700} minWidth="100px">
                Emergency:
              </Typography>
              <Typography fontSize="16px" fontWeight={400}>
                240570843170
              </Typography>
            </Box>

            <Box display="flex" gap="8px">
              <Typography fontSize="16px" fontWeight={700} minWidth="100px">
                E-mail:
              </Typography>
              <Typography fontSize="16px" fontWeight={400}>
                <EMAIL>
              </Typography>
            </Box>
          </Box>

          {/* Address Box */}
          <Box
            p="20px"
            bgcolor="#F8F9FA"
            borderRadius="8px"
            flex="1"
            display="flex"
            flexDirection="column"
            gap="12px"
          >
            <Box display="flex" alignItems="center" gap="8px">
              <RoomIcon sx={{ color: '#A24295' }} />
              <Typography fontWeight={700} fontSize="16px">
                Our Address:
              </Typography>
            </Box>
            <Typography fontWeight={400} fontSize="16px">
              St. Andrews Hospital
            </Typography>
            <Typography fontWeight={400} fontSize="16px">
              #252, Springwood Avenue,
            </Typography>
            <Typography fontWeight={400} fontSize="16px">
              Copenhagen, Denmark
            </Typography>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};
