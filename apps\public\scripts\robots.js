const fs = require('fs');
const path = require('path');

// This function can be used in next.config.js or as a standalone script
module.exports.generateRobotsTxt = function () {
  const environment =
    process.env.NEXT_PUBLIC_ENVIRONMENT || process.env.NODE_ENV;
  let content = '';

  switch (environment) {
    case 'production':
      content = `# Allow crawling only on production
User-agent: *
Allow: /`;
      break;

    default:
      content = `# Block all crawlers on non-production environments
User-agent: *
Disallow: /`;
      break;
  }

  // Write to public folder
  const robotsPath = path.join(process.cwd(), 'public', 'robots.txt');
  fs.writeFileSync(robotsPath, content);
};
