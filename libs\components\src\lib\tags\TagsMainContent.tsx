'use client';

import { useSearchParams } from 'next/navigation';
import { Box, Skeleton } from '@mui/material';
import TagSection from './TagSection';
import FullTagSectionView from './FullTagSectionView';
import { useTranslations } from 'next-intl';
import { useTagsWithFollowState } from '@minicardiac-client/apis';

export default function TagsMainContent() {
  const searchParams = useSearchParams();
  const tagType = searchParams?.get('tagType');
  const t = useTranslations('tagsPage');

  // Custom hook to fetch suggested tags
  const { isLoading, suggestedTags, followingTags } = useTagsWithFollowState({
    fetchSuggested: true,
  });

  if (tagType === 'your-tags' || tagType === 'suggested') {
    return (
      <Box
        sx={{
          maxWidth: '976px',
          width: '100%',
          px: { xs: '16px', smd: '0px' },
        }}
      >
        <FullTagSectionView tagType={tagType} />
      </Box>
    );
  }

  return (
    <Box
      sx={{
        maxWidth: '976px',
        width: '100%',
        px: { xs: '16px', smd: '0px' },
        pt: '20px',
      }}
    >
      {/* Following tags section */}
      {followingTags.length > 0 && (
        <TagSection
          title={t('yourTags')}
          tags={followingTags.slice(0, 2)}
          tagType="your-tags"
          showAll={false}
          hideSeeAll={false}
        />
      )}

      {/* Suggested tags section */}
      {isLoading ? (
        <Skeleton
          variant="rectangular"
          width="100%"
          height={80}
          sx={{ borderRadius: '8px', mt: 2 }}
        />
      ) : (
        <TagSection
          title={t('suggestedTags')}
          tags={suggestedTags.slice(0, 2)}
          tagType="suggested"
          showAll={false}
          hideSeeAll={false}
        />
      )}
    </Box>
  );
}
