import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '../networking/api-client.js';
import type { FeedPostType } from '@minicardiac-client/types';

interface ScheduledPostsResponse {
  status: number;
  message: string;
  data: FeedPostType[];
  stack: string | null;
}

export function useScheduledPosts() {
  return useQuery<FeedPostType[]>({
    queryKey: ['scheduled-posts'],
    queryFn: async () => {
      const response = await apiClient.get<ScheduledPostsResponse>(
        '/posts/scheduled',
        {
          params: {
            postTypes: ['text', 'media', 'article'],
            limit: 100, // Set a reasonable limit for scheduled posts
            offset: 0,
          },
        }
      );
      return response.data.data || [];
    },
  });
}

// Update scheduled post hook
export function useUpdateScheduledPost() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      postId,
      postType,
      payload,
    }: {
      postId: string;
      postType: string;
      payload: any;
    }) => {
      console.log(`>>>> Updating ${postType} post with ID: ${postId}`, payload);
      const endpoint = `/posts/${postType}/${postId}`;
      const response = await apiClient.patch(endpoint, payload);
      console.log(`>>>> Update response:`, response.data);
      return response.data;
    },
    onSuccess: () => {
      // Invalidate and refetch scheduled posts
      queryClient.invalidateQueries({ queryKey: ['scheduled-posts'] });
    },
  });
}

// Delete scheduled post hook
export function useDeleteScheduledPost() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (postId: string) => {
      console.log(`>>>> Deleting post with ID: ${postId}`);
      const response = await apiClient.delete(`/posts/${postId}`);
      console.log(`>>>> Delete response:`, response.data);
      return response.data;
    },
    onSuccess: () => {
      // Invalidate and refetch scheduled posts
      queryClient.invalidateQueries({ queryKey: ['scheduled-posts'] });
    },
  });
}
