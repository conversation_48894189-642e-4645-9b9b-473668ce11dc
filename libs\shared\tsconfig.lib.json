{"extends": "../../tsconfig.base.json", "compilerOptions": {"baseUrl": ".", "outDir": "dist", "types": ["node", "@nx/react/typings/cssmodule.d.ts", "@nx/react/typings/image.d.ts", "vite/client"], "jsxImportSource": "@emotion/react", "rootDir": "src", "jsx": "react-jsx", "module": "esnext", "moduleResolution": "bundler", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "tsBuildInfoFile": "dist/tsconfig.lib.tsbuildinfo"}, "exclude": ["out-tsc", "dist", "**/*.spec.ts", "**/*.test.ts", "**/*.spec.tsx", "**/*.test.tsx", "**/*.spec.js", "**/*.test.js", "**/*.spec.jsx", "**/*.test.jsx", "jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts", "eslint.config.js", "eslint.config.cjs", "eslint.config.mjs"], "include": ["src/**/*.js", "src/**/*.jsx", "src/**/*.ts", "src/**/*.tsx"]}