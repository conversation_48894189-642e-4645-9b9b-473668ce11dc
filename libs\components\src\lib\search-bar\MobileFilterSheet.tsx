'use client';

import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  IconButton,
  Button,
  Checkbox,
  useTheme,
} from '@mui/material';
import { useTranslations } from 'next-intl';
import CloseIcon from '@mui/icons-material/Close';
import { useFeedSearchStore } from '../store/useFeedSearchStore';

type MobileFilterSheetProps = {
  open: boolean;
  onClose: () => void;
  onApply: () => void;
};

export default function MobileFilterSheet({
  open,
  onClose,
  onApply,
}: MobileFilterSheetProps) {
  const theme = useTheme();
  const t = useTranslations('feedNavigation');

  // Filter options with translations
  const filterOptions = [
    { label: t('postTypes.text'), value: 'text' },
    { label: t('postTypes.poll'), value: 'poll' },
    { label: t('postTypes.media'), value: 'media' },
    { label: t('postTypes.article'), value: 'article' },
    { label: t('postTypes.question'), value: 'question' },
  ];

  // Get current post types from the store
  const postTypes = useFeedSearchStore((state) => state.postTypes);
  const setPostTypes = useFeedSearchStore((state) => state.setPostTypes);

  // Initialize with empty array and only update when the modal opens
  const [selectedTypes, setSelectedTypes] = useState<string[]>([]);

  // Sync with the store when the modal opens or postTypes changes
  useEffect(() => {
    if (open) {
      setSelectedTypes([...postTypes]);
    }
  }, [open, postTypes]);

  if (!open) return null;

  const handleToggle = (type: string) => {
    setSelectedTypes((prev) =>
      prev.includes(type) ? prev.filter((t) => t !== type) : [...prev, type]
    );
  };

  const handleApply = () => {
    setPostTypes(selectedTypes);
    onApply();
  };

  const handleClose = () => {
    if (open) {
      setSelectedTypes([...postTypes]);
    }
    onClose();
  };

  return (
    <>
      {/* Backdrop */}
      <Box
        sx={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent black for backdrop
          zIndex: 1299,
          pointerEvents: 'auto',
        }}
        onClick={handleClose}
      />

      <Box
        sx={{
          position: 'fixed',
          bottom: 0,
          left: 0,
          right: 0,
          height: '422px',
          backgroundColor: 'background.paper',
          borderRadius: '40px 40px 0px 0px',
          boxShadow: theme.shadows[4],
          zIndex: 1300,
          display: 'flex',
          flexDirection: 'column',
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Modal Handle - Mobile UX pattern */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            pt: '12px',
            pb: '8px',
          }}
        >
          <Box
            sx={{
              width: '36px',
              height: '4px',
              backgroundColor: 'neutral.300',
              borderRadius: '2px',
            }}
          />
        </Box>

        {/* Header */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '8px 24px 16px 24px',
            position: 'relative',
          }}
        >
          <Typography
            sx={{
              ...theme.typography.h5,
            }}
          >
            Filter by:
          </Typography>
          <IconButton
            onClick={handleClose}
            sx={{
              position: 'absolute',
              right: '24px',
              width: 28,
              height: 28,
              p: 0,
              color: 'neutral.600',
              '&:hover': {
                backgroundColor: 'action.hover',
              },
            }}
          >
            <CloseIcon sx={{ fontSize: 20 }} />
          </IconButton>
        </Box>

        {/* Filter Options - 2 Column Grid with reduced top margin */}
        <Box
          sx={{
            padding: '0 30px',
            marginTop: '32px',
            mb: 3,
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
          }}
        >
          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              rowGap: '20px',
              columnGap: '24px',
            }}
          >
            {filterOptions.map((option) => (
              <Box
                key={option.value}
                role="checkbox"
                aria-checked={selectedTypes.includes(option.value)}
                aria-label={`Filter by ${option.label}`}
                onClick={() => handleToggle(option.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    handleToggle(option.value);
                  }
                }}
                tabIndex={0}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px',
                  padding: '10px 0',
                  cursor: 'pointer',
                  '&:focus-visible': {
                    outline: `2px solid ${theme.palette.primary.main}`,
                    outlineOffset: '2px',
                    borderRadius: '4px',
                  },
                }}
              >
                <Checkbox
                  checked={selectedTypes.includes(option.value)}
                  onChange={(e) => {
                    e.stopPropagation();
                    handleToggle(option.value);
                  }}
                  sx={{
                    padding: 0,
                    color: 'action.disabled',
                    '&.Mui-checked': {
                      color: theme.palette.secondary.main,
                    },
                  }}
                />
                <Typography
                  sx={{
                    fontSize: '14px',
                    fontWeight: 400,
                    color: 'text.primary',
                    fontFeatureSettings: "'liga' off",
                    ...theme.typography.body1,
                    whiteSpace: 'nowrap',
                  }}
                >
                  {option.label}
                </Typography>
              </Box>
            ))}
          </Box>
        </Box>

        {/* Bottom Actions - Now positioned at the bottom */}
        <Box
          sx={{
            padding: '16px 24px 32px 24px',
            display: 'flex',
            flexDirection: 'column',
            gap: '12px',
            marginTop: 'auto',
          }}
        >
          {/* Apply Button*/}
          <Button
            variant="contained"
            onClick={handleApply}
            sx={{
              width: '100%',
              height: '44px',
              borderRadius: '8px',
              backgroundColor: 'neutral.400',
              color: 'common.white',
              fontFamily: theme.typography.fontFamily,
              fontSize: '16px',
              fontWeight: 700,
              lineHeight: '24px',
              textTransform: 'none',
              boxShadow: 'none',
              '&:hover': {
                backgroundColor: 'neutral.500',
                boxShadow: 'none',
              },
              '&:active': {
                backgroundColor: 'neutral.600',
                boxShadow: 'none',
              },
            }}
          >
            Apply
          </Button>

          {/* Cancel Button */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
            }}
          >
            <Button
              variant="text"
              onClick={handleClose}
              sx={{
                minWidth: 'auto',
                height: '36px',
                padding: '8px 16px',
                borderRadius: '6px',
                color: theme.palette.secondary.main,
                ...theme.typography.body1,
                fontWeight: 600,
                textTransform: 'none',
                '&:hover': {
                  backgroundColor: 'action.hover',
                  color: theme.palette.secondary.dark,
                },
              }}
            >
              Cancel
            </Button>
          </Box>
        </Box>
      </Box>
    </>
  );
}
