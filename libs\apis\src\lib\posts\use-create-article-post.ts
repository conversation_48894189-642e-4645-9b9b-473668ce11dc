import { useMutation } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { postApi } from './post-api.js';
import type { CreateArticlePostRequest } from './types.js';

export function useCreateArticlePost(options?: {
  onSuccess?: (data: unknown) => void;
  onError?: (error: AxiosError | Error) => void;
}) {
  return useMutation({
    mutationFn: async (data: CreateArticlePostRequest) => {
      return postApi.createArticlePost(data);
    },
    onSuccess: options?.onSuccess,
    onError: options?.onError,
  });
}
