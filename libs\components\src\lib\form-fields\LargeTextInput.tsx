import { TextField } from '@mui/material';
import React from 'react';

interface TextInputProps {
  label: string;
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  sx?: object;
}

const LargeTextInput: React.FC<TextInputProps> = ({
  label,
  placeholder,
  value,
  onChange,
  sx,
}) => (
  <TextField
    label={label}
    placeholder={placeholder}
    value={value}
    onChange={(e) => onChange(e.target.value)}
    fullWidth
    InputLabelProps={{ shrink: true }}
    sx={{
      '& .MuiOutlinedInput-root': {
        height: { xs: 181, sm: 215 },
        alignItems: 'flex-start',
        '& textarea': {
          height: '100%',
          boxSizing: 'border-box',
          resize: 'none',
          paddingTop: '10px',
          '&::placeholder': {
            lineHeight: 1.2,
          },
        },
        ...sx,
      },
    }}
  />
);

export default LargeTextInput;
