'use client';

import { Box, Typography, useTheme } from '@mui/material';
import { forwardRef } from 'react';

export interface PostCreationNotificationProps {
  /** Custom width for the notification */
  width?: string | number;
  /** Whether to show the notification */
  show?: boolean;
  /** Callback when upgrade plan link is clicked */
  onUpgradeClick?: () => void;
  /** Additional custom styles */
  sx?: object;
}

export const PostCreationNotification = forwardRef<HTMLDivElement, PostCreationNotificationProps>(
  function PostCreationNotification(
    {
      width = '754px', // Changed from '749px' to add 5px
      show = true,
      onUpgradeClick,
      sx = {},
      ...props
    },
    ref
  ) {
    const theme = useTheme();

    if (!show) return null;

    return (
      <Box
        ref={ref}
        sx={{
          width,
          minHeight: '40px',
          backgroundColor: '#F5F5F5', 
          padding: `${theme.spacing(1)} ${theme.spacing(2)}`,
          borderRadius: '4px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          boxSizing: 'border-box',
          ...sx,
        }}
        {...props}
      >
        <Typography
          variant="body2"
          sx={{
            color: theme.palette.text?.secondary || '#333',
            fontFamily: theme.typography.fontFamily || '"Plus Jakarta Sans", sans-serif',
            fontSize: theme.typography.fontSize || '14px',
            fontWeight: theme.typography.fontWeightRegular || 400,
            lineHeight: '18px', // Reduced line height for better vertical fit
            textAlign: 'center',
            '& .highlight': {
              backgroundColor: '#FFD700',
              padding: `${theme.spacing(0.25)} ${theme.spacing(0.5)}`,
              borderRadius: theme.shape?.borderRadius || '4px',
              fontWeight: theme.typography.fontWeightBold || 600,
            },
            '& .upgrade-link': {
              color: 'var(--Rouge--Secondary, #A249A4)',
              fontWeight: theme.typography.fontWeightBold || 600,
              textDecoration: 'underline',
              cursor: 'pointer',
              '&:hover': {
                opacity: 0.8,
              },
            },
          }}
        >
          This post will be posted to{' '}
          <strong>#PublicForum</strong>. This means that it will only be visible to other users within this tag and not on their main{' '}
          <span className="highlight">feed</span>. Please{' '}
          <span 
            className="upgrade-link"
            onClick={onUpgradeClick}
            role="button"
            tabIndex={0}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                onUpgradeClick?.();
              }
            }}
          >
            upgrade your plan
          </span>{' '}
          if you would like to change this.
        </Typography>
      </Box>
    );
  }
);

export default PostCreationNotification;
