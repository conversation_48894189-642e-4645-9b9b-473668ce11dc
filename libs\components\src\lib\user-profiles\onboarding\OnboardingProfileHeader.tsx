import { Avatar, Box, Typography } from '@mui/material';
import ProfileHeader from '../common/ProfileHeader';
import { Profile } from '@minicardiac-client/types';
import CurvedMuiRating from '../common/CurvedRatingStars';
import { formatFollowers } from '@minicardiac-client/utilities';
import ActionButtons from '../common/ActionButtons';

export const OnboardingProfileHeader = ({ profile }: { profile: Profile }) => {
  return (
    <ProfileHeader
      profile={profile}
      ConnectUserComponent={ConnectUser}
      ProfileConnectionsComponent={ProfileConnections}
    />
  );
};

const ConnectUser = ({ profile }: { profile: Profile }) => {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: { xs: 'row', lg: 'column' },
        alignItems: { xs: 'center', lg: 'flex-start' },
        justifyContent: { xs: 'space-between', lg: 'end' },
        gap: 2,
        flexWrap: 'wrap',
        px: { xs: '20px', lg: '0px' },
      }}
    >
      {/* Connections + Followers */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          color: { lg: 'white' },
          fontSize: { xs: 18, lg: 22 },
          fontWeight: 400,
        }}
      >
        <Typography>
          {profile.connections > 500
            ? '500+ Connections'
            : `${profile.connections} Connections`}
        </Typography>
        <Typography sx={{ px: 1 }}>|</Typography>
        <Typography>{`${formatFollowers(
          profile.followers
        )} Followers`}</Typography>
      </Box>

      {/* Buttons */}
      <ActionButtons />
    </Box>
  );
};

const ProfileConnections = ({ profile }: { profile: Profile }) => {
  return (
    <Box
      sx={{
        p: { xs: '16px', lg: '20px' },
        display: 'flex',
        justifyContent: 'space-between',
        flexDirection: { xs: 'column', smd: 'row' },
        position: 'absolute',
        bottom: { xs: 10, lg: 0 },
        left: 0,
        right: 0,
        background: 'transparent',
        flexWrap: 'wrap',
        gap: { xs: '10px' },
      }}
    >
      {/* Left: Profile + Stars */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: '20px',
        }}
      >
        <Box sx={{ position: 'relative', width: 124, height: 124 }}>
          <Avatar
            src={profile.profilePic}
            sx={{ width: 124, height: 124, borderRadius: '50%' }}
          />

          {/* 5 Stars */}
          <CurvedMuiRating rating={profile.rating} radius={60} size="small" />
        </Box>

        {/* Right side: Name, Degrees, Designation, Workplace */}
        <Box sx={{ color: 'white' }}>
          <Typography fontWeight={700} fontSize={{ xs: 20, lg: 24 }}>
            {profile.name}
          </Typography>

          <Typography
            fontSize={{ xs: 14, lg: 16 }}
            fontWeight={500}
            sx={{ mt: '2px' }}
          >
            {profile.degrees || 'MD, CCT, PhD'}
          </Typography>

          <Box sx={{ height: '20px' }} />

          <Typography
            fontSize={{ xs: 14, lg: 16 }}
            fontWeight={600}
            sx={{ color: '#F8F9FA' }}
          >
            {profile.designation || 'Head of Cardiology'}
          </Typography>

          <Box
            sx={{
              mt: 1,
              fontSize: 12,
              fontWeight: 600,
              backgroundColor: '#F8F9FA',
              color: '#A24295',
              borderRadius: '20px',
              p: 1,
              minHeight: '30px',
              height: 'auto',
              whiteSpace: 'normal',
              wordBreak: 'break-word',
              lineHeight: '16px',
              textAlign: 'left',
            }}
          >
            {profile.workplace || 'St. Andrews Hospital, Copenhagen'}
          </Box>
        </Box>
      </Box>

      {/* Right: Connect Buttons on smd+ */}
      <Box
        sx={{
          display: { xs: 'none', lg: 'flex' },
        }}
      >
        <ConnectUser profile={profile} />
      </Box>
    </Box>
  );
};
