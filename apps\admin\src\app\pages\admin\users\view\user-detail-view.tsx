import {
  Box,
  Container,
  <PERSON>ItemText,
  Tab,
  Tabs,
  Grid,
  Toolt<PERSON>,
} from '@mui/material';

import {
  CustomBreadCrumbs,
  DocumentPreviewSection,
  Iconify,
} from '@minicardiac-client/components';
import { useNavigate, useParams } from 'react-router-dom';
import { useEffect, useState } from 'react';
import ProfileCardSkeleton from '../list/profile-card-skeleton';
import ProfileCard from '../list/profile-card';
import ProfileCardDetails from '../list/profile-card-details';
import type { User } from '@minicardiac-client/types';
import { useGetUserDetails } from '@minicardiac-client/apis';

export const USER_DETAILS_TAB = {
  user_details: '0',
  documents: '1',
};

export default function UserDetailView() {
  const params = useParams();
  const { userId } = params;

  const [user, setUser] = useState<User | null>(null);
  const [currentTab, setCurrentTab] = useState(USER_DETAILS_TAB.user_details);
  const navigate = useNavigate();

  const data = useGetUserDetails({ userId });
  console.log(data);

  useEffect(() => {
    // Fetch user details (mocked for now)
    const fetchUser = async () => {
      const mockUserResponse = {
        success: true,
        data: {
          id: '100',
          full_name: 'Mayur Hanwate',
          email: '<EMAIL>',
          qualification: 'MBBS, MD',
          employer: 'Apollo Hospitals',
          designation: 'Senior Cardiologist',
          user_type: 'Organization',
          status: 'Pending',
          about:
            'Experienced cardiologist with over 10 years of practice in clinical and surgical environments.',
          profile_picture:
            'https://d2ao.cloudfront.net/public/users/mayur_profile_pic.jpg',
          documents: {
            medical_degree: {
              file_name: 'mayur_mbbs_certificate.pdf',
              file_type: 'application/pdf',
              url: 'https://www.wbdg.org/FFC/VA/VADEGUID/dg_small_house_model.pdf',
            },
            professional_qualification: {
              file_name: 'mayur_md_cardio.jpg',
              file_type: 'image/jpeg',
              url: 'https://images.unsplash.com/photo-1744877478622-a78c7a3336f6?q=80&w=1974&auto=format&fit=crop',
            },
            medical_qualification: {
              file_name: 'mayur_medical_qualification.pdf',
              file_type: '',
              url: '',
            },
          },
        },
      };

      if (mockUserResponse.success) {
        const mockUser = mockUserResponse.data;
        const user: User = {
          id: mockUser.id,
          name: mockUser.full_name,
          email: mockUser.email,
          qualification: mockUser.qualification,
          user_type: 'Organization',
          employer: mockUser.employer,
          designation: mockUser.designation,
          verification_status: mockUser.status,
          about: mockUser.about,
          profile_picture: mockUser.profile_picture,
          documents: mockUser.documents,
          registration_date: '',
        };
        setUser(user);
      }
    };

    fetchUser();
  }, [userId]);

  if (!user) return null;

  const userDetails = [
    { label: 'Email', value: user?.email || '----' },
    { label: 'Employee Category', value: user?.user_type || '----' },
    { label: 'Qualification', value: 'MD, Cardiology' },
    { label: 'Location', value: 'New York, USA' },
    {
      label: 'Verification Status',
      value: user?.verification_status || 'Pending',
    },
  ];

  const handleTabChange = (_: unknown, newValue: string) => {
    if (newValue === USER_DETAILS_TAB.user_details) {
      navigate('.', { replace: true });
    } else {
      navigate(`?activeTab=${newValue}`);
    }
    setCurrentTab(newValue);
  };

  return (
    <Container maxWidth="xl">
      <CustomBreadCrumbs
        heading="User Details"
        links={[
          { name: 'Admin', href: '#' },
          {
            name: 'Pending Verifications',
            href: '/admin/pending-verifications',
          },

          { name: user.name },
        ]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems={{ xs: 'flex-start' }}
        flexDirection={{ xs: 'column', sm: 'row' }}
        mb={2}
      >
        <Tabs
          value={currentTab}
          onChange={handleTabChange}
          // textColor={'#A24295'} // Works but gives error
          sx={{
            marginBottom: '1rem',
            '& .MuiTab-root': {
              textTransform: 'none',
              color: '#A24295',
            },
            '& .Mui-selected': {
              fontWeight: 'bold',
            },
            '& .MuiTabs-indicator': {
              backgroundColor: '#A24295',
            },
          }}
        >
          <Tab
            label={'Profile'}
            value={USER_DETAILS_TAB.user_details}
            sx={{ textTransform: 'none' }}
          />
          <Tab
            label={'Documents'}
            value={USER_DETAILS_TAB.documents}
            sx={{ textTransform: 'none' }}
          />
        </Tabs>
        <Box
          width={{ xs: '100%', sm: 'auto' }}
          display="flex"
          flexDirection={{ xs: 'column', sm: 'row' }}
          justifyContent={{ xs: 'flex-end' }}
          alignItems={{ xs: 'flex-end', sm: 'center' }}
          gap={2}
        >
          <Box display="flex" alignItems={{ xs: 'flex-end' }} gap={2}>
            <Tooltip title={`Edit Users`} placement="top" arrow>
              <Iconify icon="solar:pen-bold" />
            </Tooltip>
          </Box>
        </Box>
      </Box>

      {currentTab === USER_DETAILS_TAB.user_details && (
        <Grid container spacing={2}>
          <Grid size={{ xs: 12 }}>
            <Box
              gap={3}
              mb={3}
              sx={{
                display: 'flex',
                flexWrap: 'wrap',
                flexDirection: {
                  xs: 'column',
                  sm: 'column',
                  md: 'row',
                },
                alignItems: 'stretch', // ensures ProfileCardDetails stretches
                width: '100%',
              }}
            >
              <Box>
                {!user ? (
                  <ProfileCardSkeleton sx={{ width: 120, height: 120 }} />
                ) : (
                  <ProfileCard user={user} />
                )}
              </Box>
              <Box sx={{ flexGrow: 1, minWidth: 0, display: 'flex' }}>
                <ProfileCardDetails user={user} />
              </Box>
            </Box>
          </Grid>

          <Box display="flex" flexWrap={'wrap'} margin={2} gap={2}>
            {userDetails.map((detail, index) => (
              <Box padding={1}>
                <ListItemText
                  primary={detail.label}
                  secondary={detail.value}
                  primaryTypographyProps={{
                    sx: { color: '#6B7280', fontWeight: '500', fontSize: 14 },
                  }} // gray-500
                  secondaryTypographyProps={{ sx: { color: '#111827' } }} // gray-900
                />
              </Box>
            ))}
          </Box>
        </Grid>
      )}
      {currentTab === USER_DETAILS_TAB.documents && (
        <DocumentPreviewSection user={user} />
      )}
    </Container>
  );
}
