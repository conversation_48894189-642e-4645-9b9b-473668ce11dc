import { createTheme as createMuiTheme } from '@mui/material';
import './theme-declarations';

// declare module '@mui/material/styles' {
//   interface Palette {
//     neutral: Palette['primary'];
//     gradient: {
//       main: string;
//     };
//   }
//   interface PaletteOptions {
//     neutral?: PaletteOptions['primary'];
//     gradient?: {
//       main: string;
//     };
//   }
// }

declare module '@mui/material/styles' {
  interface BreakpointOverrides {
    xxl: true;
    smd: true;
  }
}
export const theme = createMuiTheme({
  palette: {
    // Primary color - Torch Red (used for primary buttons, active tabs, etc.)
    primary: {
      light: '#FEE9EC',
      main: '#F92243', // Torch Red - Primary
      dark: '#7D1122',
    },
    // Secondary color - Rouge (used for links, secondary buttons, etc.)
    secondary: {
      light: '#F6ECF4',
      main: '#A24295', // Rouge - Secondary (rgba(162, 66, 149, 1))
      dark: '#51214B',
    },
    // Neutral colors for text, backgrounds, borders
    neutral: {
      100: '#F8F9FA', // Light Grey - Neutral
      200: '#F3F4F6',
      300: '#E1E3E6',
      400: '#C5C7CA',
      500: '#A3A3A3', // Mid Grey - Neutral (rgba(163, 163, 163, 1))
      600: '#737678',
      700: '#525456',
      800: '#333537', // Dark Charcoal - Neutral Black
      900: '#1E1E1E', // Darkest Charcoal for Welcome text
      main: '#A3A3A3',
      lowEmphasis: '#333537', // Dark Charcoal Low Emphasis- Tint
    } as const,
    lowEmphasis: '#333537',
    // Status colors for feedback
    error: {
      main: '#FF5C5C', // Error Soft Red - Status
    },
    success: {
      main: '#00C96B', // Success Emerald - Status
    },
    warning: {
      main: '#FFC800', // Caution Yellow - Status
    },
    status: {
      error: '#FF5C5C', // Error Soft Red - Status
      success: '#00C96B', // Success Emerald - Status
      warning: '#FFC800', // Caution Yellow - Status
    },
    tint: {
      hover: '#B24E9F', // Rouge Hover - Tint
      lowEmphasis: '#4D4D4D', // Dark Charcoal Low Emphasis - Tint
      lowerEmphasis: '#666666', // Dark Charcoal Lower Emphasis - Tint
    },
    text: {
      primary: '#1E1E1E',
      secondary: '#525456',
    },
    background: {
      default: '#FFFFFF',
      paper: '#F8F9FA',
    },
  },
  typography: {
    fontFamily: 'var(--font-plus-jakarta-sans), sans-serif',
    h1: {
      fontFamily: 'var(--font-plus-jakarta-sans), sans-serif',
      fontSize: '40px',
      fontWeight: 800,
      lineHeight: 1.2,
    },
    h2: {
      fontFamily: 'var(--font-plus-jakarta-sans), sans-serif',
      fontSize: '32px',
      fontWeight: 300,
      lineHeight: 1.2,
    },
    h3: {
      fontFamily: 'var(--font-plus-jakarta-sans), sans-serif',
      fontSize: '28px',
      fontWeight: 500,
      lineHeight: 1.2,
    },
    h4: {
      fontFamily: "'Plus Jakarta Sans', sans-serif",
      fontSize: '20px',
      fontWeight: 300,
      lineHeight: 1.2,
    },
    h5: {
      fontFamily: "'Plus Jakarta Sans', sans-serif",
      fontSize: '20px',
      fontWeight: 500,
      lineHeight: 1.5,
    },
    subtitle1: {
      fontFamily: "'Plus Jakarta Sans', sans-serif",
      fontSize: '24px',
      fontWeight: 400,
      lineHeight: 1.5,
    },
    subtitle2: {
      fontFamily: "'Plus Jakarta Sans', sans-serif",
      fontSize: '24px',
      fontWeight: 500,
      lineHeight: 1.5,
    },
    body1: {
      fontFamily: "'Plus Jakarta Sans', sans-serif",
      fontSize: '16px',
      fontWeight: 400,
      lineHeight: 1.5,
    },
    body2: {
      fontFamily: "'Plus Jakarta Sans', sans-serif",
      fontSize: '14px',
      fontWeight: 300,
      lineHeight: 1.5,
    },
    button: {
      fontFamily: "'Plus Jakarta Sans', sans-serif",
      textTransform: 'none',
      fontWeight: 500,
    },
    caption: {
      fontFamily: "'Plus Jakarta Sans', sans-serif",
      fontSize: '12px',
      fontWeight: 300,
      lineHeight: 1.5,
    },
    signUpText: {
      fontFamily: "'Plus Jakarta Sans', sans-serif",
      fontWeight: 400,
      fontSize: '24px',
      lineHeight: '32px',
      letterSpacing: '0px',
      textAlign: 'center',
    },
    // COMMENTED OUT - Moved to component sx prop (MC-219)
    // signUpAsText: {
    //   fontFamily: "'Plus Jakarta Sans', sans-serif",
    //   fontWeight: 400,
    //   fontSize: '17px',
    //   lineHeight: '24px',
    //   letterSpacing: '0px',
    //   color: '#737678', // neutral.600
    // },
    // Moved to component sx prop
    // subscriptionCardTitle: {
    //   fontFamily: "'Plus Jakarta Sans', sans-serif",
    //   fontWeight: 400,
    //   fontSize: '24px',
    //   lineHeight: '100%',
    //   letterSpacing: '0px',
    //   color: '#1E1E1E', // neutral.600
    // },
    // subscriptionCardSubtitle: {
    //   fontFamily: "'Plus Jakarta Sans', sans-serif",
    //   fontWeight: 500,
    //   fontSize: '12px',
    //   lineHeight: '100%',
    //   letterSpacing: '0px',
    //   color: '#1E1E1E', // neutral.600
    // },

    prestigeSubscriptionTitle: {
      fontFamily: "'Plus Jakarta Sans', sans-serif",
      fontWeight: 600,
      fontSize: '12px',
      lineHeight: '100%',
      letterSpacing: '0%',
      color: '#1E1E1E',
    },
    prestigeSubscriptionText: {
      fontFamily: "'Plus Jakarta Sans', sans-serif",
      fontWeight: 300,
      fontSize: '12px',
      lineHeight: '100%',
      letterSpacing: '0%',
      color: '#333537',
    },
    heading1: {
      fontFamily: "'Plus Jakarta Sans', sans-serif",
      fontSize: '32px',
      fontWeight: 300,
      lineHeight: '100%',
      letterSpacing: '0%',
      verticalAlign: 'middle',
      display: 'inline',
      color: '#1E1E1E',
    },
    heading2: {
      fontFamily: "'Plus Jakarta Sans', sans-serif",
      fontSize: '32px',
      fontWeight: 500,
      lineHeight: '100%',
      display: 'inline',
      backgroundImage:
        'linear-gradient(180deg, rgba(249, 34, 67, 1) 0%, rgba(162, 66, 149, 1) 100%)',
      WebkitBackgroundClip: 'text',
      WebkitTextFillColor: 'transparent',
      backgroundClip: 'text',
      color: 'transparent',
    },
    heading3: {
      fontFamily: "'Plus Jakarta Sans', sans-serif",
      fontSize: '24px',
      fontWeight: 600,
      lineHeight: '120%',
    },
    heading4: {
      fontFamily: "'Plus Jakarta Sans', sans-serif",
      fontSize: '20px',
      fontWeight: 600,
      lineHeight: '120%',
    },
    textButton: {
      fontFamily: "'Plus Jakarta Sans', sans-serif",
      fontSize: '16px',
      fontWeight: 400,
      lineHeight: 1.5,
    },
    subtitle3: {
      fontFamily: "'Plus Jakarta Sans', sans-serif",
      fontWeight: 300,
      fontSize: '16px',
      lineHeight: 1.5,
      color: '#1E1E1E',
    },
    welcomeText: {
      fontFamily: "'Plus Jakarta Sans', sans-serif",
      fontWeight: 300,
      fontSize: '32px',
      lineHeight: '100%',
      letterSpacing: '0%',
      verticalAlign: 'middle',
    },
    brandText: {
      fontFamily: "'Plus Jakarta Sans', sans-serif",
      fontWeight: 500,
      fontSize: '32px',
      lineHeight: '100%',
      letterSpacing: '0%',
      verticalAlign: 'middle',
    },
    doctorName: {
      fontFamily: "'Plus Jakarta Sans', sans-serif",
      fontWeight: 600,
      fontSize: '24px',
      lineHeight: '100%',
      letterSpacing: '0%',
      verticalAlign: 'middle',
    },
    otpDescription: {
      fontFamily: "'Plus Jakarta Sans', sans-serif",
      fontWeight: 300,
      fontSize: '16px',
      lineHeight: '100%',
      letterSpacing: '0%',
      verticalAlign: 'middle',
      color: '#333537',
    },
    // COMMENTED OUT - Moved to component sx prop (MC-219)
    // signupOptionTitle: {
    //   fontFamily: "'Plus Jakarta Sans', sans-serif",
    //   fontWeight: 400,
    //   fontSize: '20px',
    //   lineHeight: 1.2,
    //   letterSpacing: 0,
    //   color: '#1E1E1E',
    // },
    // signupOptionDescription: {
    //   fontFamily: "'Plus Jakarta Sans', sans-serif",
    //   fontWeight: 300,
    //   fontSize: '12px',
    //   lineHeight: '16px',
    //   letterSpacing: 0,
    //   color: '#737678',
    // },
    professionalType: {
      fontFamily: "'Plus Jakarta Sans', sans-serif",
      fontWeight: 700,
      fontSize: '16px',
      lineHeight: '100%',
      letterSpacing: '0%',
      textAlign: 'center',
      color: '#1E1E1E',
    },
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: `
        @font-face {
          font-family: 'Plus Jakarta Sans';
          font-style: normal;
          font-display: swap;
          font-weight: 300;
         
        }
        @font-face {
          font-family: 'Plus Jakarta Sans';
          font-style: normal;
          font-display: swap;
          font-weight: 400;
          
        }
        @font-face {
          font-family: 'Plus Jakarta Sans';
          font-style: normal;
          font-display: swap;
          font-weight: 500;
        }
        @font-face {
          font-family: 'Plus Jakarta Sans';
          font-style: normal;
          font-display: swap;
          font-weight: 700;
          
        }
        @font-face {
          font-family: 'Plus Jakarta Sans';
          font-style: normal;
          font-display: swap;
          font-weight: 800;
          
        }
      /* Move Introductory Statement below Title */
      .MuiGrid-container > .MuiGrid-root:last-child {
        order: 5 !important;
        margin-top: 24px !important;
        width: 100% !important;
      }

      /* Ensure the Introductory Statement has proper width */
      .MuiGrid-container > .MuiGrid-root:last-child > div {
        width: 100% !important;
      }
      `,
    },
    // Ensure input fields use the correct font
    MuiInputBase: {
      styleOverrides: {
        root: {
          fontFamily: '"Plus Jakarta Sans", sans-serif',
        },
      },
    },
    // MenuItem (each option)
    MuiAutocomplete: {
      styleOverrides: {
        root: {
          '.MuiAutocomplete-popper': {
            backgroundColor: '#fff',
          },
        },
        paper: {
          marginTop: '5px',
          backgroundColor: '#fff',
        },
        option: {
          '&[aria-selected="true"]': {
            backgroundColor: 'rgba(162, 66, 149, 0.1) !important',
          },
          '&[aria-selected="true"].Mui-focused': {
            backgroundColor: 'rgba(162, 66, 149, 0.1)',
          },
          '&:hover': {
            backgroundColor: 'rgba(162, 66, 149, 0.1)',
          },
        },
      },
    },
    // Button styling
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: 'none',
          fontWeight: 500,
          fontSize: '14px',
          fontFamily: "'Plus Jakarta Sans', sans-serif",
          padding: '8px 16px',
          boxShadow: 'none',
          height: '40px',
        },
        // Primary button (Continue button)
        containedPrimary: {
          backgroundColor: '#A24295', // Purple for Continue button
          color: '#FFFFFF',
          '&:hover': {
            backgroundColor: '#7D1122', // Darker red for hover
            boxShadow: 'none',
          },
          '&.Mui-disabled': {
            backgroundColor: '#E1E3E6',
            color: '#A3A3A3',
          },
        },
        // Secondary button
        containedSecondary: {
          backgroundColor: '#A24295', // Rouge - Secondary
          color: '#FFFFFF',
          '&:hover': {
            backgroundColor: '#B24E9F', // Rouge Hover - Tint
            boxShadow: 'none',
          },
        },
        // Text button (Forgot password?)
        text: {
          padding: 0,
          color: '#A24295', // Rouge - Secondary
          '&:hover': {
            backgroundColor: 'transparent',
            color: '#B24E9F', // Rouge Hover - Tint
            textDecoration: 'underline',
          },
        },
        // Outlined buttons (Social login buttons)
        outlined: {
          root: {
            borderColor: '#E1E3E6',
            borderWidth: '1px',
            color: '#1E1E1E',
            minWidth: '282px',
            paddingRight: '40px',
            paddingLeft: '40px',
            fontFamily: "'Plus Jakarta Sans', sans-serif",
            fontWeight: (theme: {
              customValues: {
                typography: { button: { medium: { fontWeight: any } } };
              };
            }) => theme.customValues.typography.button.medium.fontWeight,
            fontSize: (theme: {
              customValues: {
                typography: { button: { medium: { fontSize: any } } };
              };
            }) => theme.customValues.typography.button.medium.fontSize,
            lineHeight: (theme: {
              customValues: {
                typography: { button: { medium: { lineHeight: any } } };
              };
            }) => theme.customValues.typography.button.medium.lineHeight,
            letterSpacing: (theme: {
              customValues: {
                typography: { button: { medium: { letterSpacing: any } } };
              };
            }) => theme.customValues.typography.button.medium.letterSpacing,
            textAlign: 'center',
            verticalAlign: 'middle',
            '&:hover': {
              backgroundColor: '#F8F9FA',
              borderColor: '#E1E3E6',
            },
          },
        },
      },
    },
    // Text field styling (Email, Password inputs)
    MuiTextField: {
      defaultProps: {
        InputLabelProps: {
          shrink: true,
        },
      },
      styleOverrides: {
        root: {
          width: '100%',
          '& .MuiOutlinedInput-root': {
            height: 45,
            borderRadius: 8,
            backgroundColor: '#FFFFFF',
            '& fieldset': {
              borderColor: '#E1E3E6',
            },
            '&:hover fieldset': {
              borderColor: '#A3A3A3',
            },
            '&.Mui-focused fieldset': {
              borderColor: '#A24295',
            },
          },

          '& .MuiFormLabel-root.MuiInputLabel-root': {
            color: '#1E1E1E', // Dark Charcoal-Neutral Black
            fontWeight: 500,
            backgroundColor: '#FFFFFF',
            padding: '0 0 0 0px',
            '&.Mui-focused': {
              color: '#A24295',
            },
          },
        },
      },
    },
    // Tab styling (Sign In, Sign Up tabs)
    MuiTab: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          fontWeight: 500,
          fontSize: 16,
          minWidth: 'auto',
          padding: '12px 24px',
          color: '#525456',
          '&.Mui-selected': {
            color: '#A24295', // Rouge - Secondary color
            fontWeight: 600,
          },
        },
      },
    },
    MuiTabs: {
      styleOverrides: {
        root: {
          minHeight: 48,
        },
        indicator: {
          height: 3,
          backgroundColor: '#A24295', // Rouge - Secondary color
        },
        flexContainer: {
          justifyContent: 'center',
          gap: '40px',
        },
      },
    },
    // Divider styling (OR divider)
    MuiDivider: {
      styleOverrides: {
        root: {
          '&.MuiDivider-withChildren': {
            '&::before, &::after': {
              borderColor: '#E1E3E6',
            },
          },
        },
      },
    },
    // Typography for various text elements
    MuiTypography: {
      styleOverrides: {
        root: {
          '&.forgot-password': {
            color: '#A24295',
            cursor: 'pointer',
            fontWeight: 500,
            fontSize: '14px',
            '&:hover': {
              textDecoration: 'underline',
            },
          },
          '&.brand-gradient': {
            backgroundImage:
              'linear-gradient(90deg, rgba(249, 34, 67, 1) 0%, rgba(162, 66, 149, 1) 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            color: 'transparent',
            fontWeight: 500,
          },
          '&.subtitle-text': {
            fontWeight: 300,
            color: '#1E1E1E',
            fontSize: '14px',
          },
          '&.signup-as-text': {
            fontWeight: 400,
            fontSize: '24px', // Updated to match Figma
            lineHeight: '32px', // Updated to match Figma
            letterSpacing: '0px',
            textAlign: 'center',
            verticalAlign: 'middle',
            color: '#737678', // neutral.600
            display: 'inline-block',
          },
        },
      },
    },
    MuiMenu: {
      styleOverrides: {
        paper: {
          filter: `
        drop-shadow(0px 12px 24px rgba(0, 0, 0, 0.12)) 
        drop-shadow(0px 0px 8px rgba(0, 0, 0, 0.06))
      `,
          '& .MuiMenuItem-root': {
            borderRadius: 0,
            overflow: 'hidden',
            '&:hover': {
              backgroundColor: 'rgba(162, 66, 149, 0.1)',
              overflow: 'hidden',
            },
            '&.Mui-focusVisible': {
              backgroundColor: 'rgba(162, 66, 149, 0.1)',
            },
            '&.Mui-selected': {
              backgroundColor: 'rgba(162, 66, 149, 0.1) !important',
            },
            '&.Mui-selected:hover': {
              backgroundColor: 'rgba(162, 66, 149, 0.15) !important',
            },
          },
          '& .MuiMenu-list': {
            padding: 0,
          },
          '& .MuiDivider-root': {
            margin: 0,
          },
        },
      },
    },
    MuiMenuItem: {
      styleOverrides: {
        root: {
          borderRadius: '8px',
          fontSize: '14px',
        },
      },
    },
  },
  spacing: 8, // Base spacing unit
  shape: {
    borderRadius: 8, // Default border radius
  },
  customValues: {
    gradient: {
      primary: 'linear-gradient(90deg, #F92243 0%, #A24295 100%)',
      neutral:
        'linear-gradient(175deg, #A3A3A3 3.99%, rgba(163, 163, 163, 0) 84.34%)',
      prestige:
        'linear-gradient(90deg, rgba(162, 66, 149, 0.2) 0%, rgba(220, 183, 215, 0.2) 60.08%, rgba(255, 255, 255, 0.2) 100%)',
    },
    paper: {
      boxShadow: {
        light: '0px 0px 2px 0px #A3A3A333',
        medium: '0px 12px 24px 0px #A3A3A31F',
      },
      borderRadius: 8,
    },
    button: {
      minWidth: '141px',
      width: 320,
      height: 40,
      radius: 8,
      spacing: 8,
      smHeight: 20,
      textVariantSmPx: 8,
      padding: {
        x: 40,
      },
      border: {
        width: 1,
      },
    },
    proceedButton: {
      width: 147, // Wider button to match Figma
      height: 40,

      borderRadius: 1, // Rounded corners but less than default
      borderWidth: 1,
      paddingX: 40,
      gap: 8,
    },
    tabs: {
      width: 176,
      height: 48,
      gap: 40,
    },
    backButton: {
      width: 21,
      height: 23,
      top: 30,
      left: 30,
      angle: 0,
    },
    typography: {
      button: {
        small: {
          lineHeight: '22px',
          fontSize: '16px',
          fontWeight: 600,
          letterSpacing: '0px',
        },
        medium: {
          lineHeight: '40px',
          fontSize: '16px',
          fontWeight: 700,
          letterSpacing: '0px',
        },
      },
    },
    divider: {
      spacing: {
        y: 24, // 24px vertical spacing for dividers
      },
    },
    iconButton: {
      size: 36, // Size for icon buttons
    },
    spacing: {
      welcomeToCarousel: 91,
      doctorNameToCarousel: 193,
      carouselToCards: -60,
      doctorNameToOtpDescription: 80,
    },
    signupCard: {
      width: 360,
      height: 146,
      borderRadius: 12,
      borderWidth: 0.5,
      borderColor: '#A24295',
      shape: {
        width: 171.53,
        height: 161.53,
        top: 0 /* Adjusted to position behind characters */,
        left: 100,
        angle: 40,
        opacity: 0.15,
        borderRadius: 'radius-3',
      },
      content: {
        width: 198,
        height: 106,
        top: 20,
        left: 24,
        gap: 12,
        description: {
          width: 198,
          height: 64,
        },
      },
    },
    doctorCharacter: {
      width: 47.92,
      height: 90.15,
      top: 55,
      left: 300,
      color: '#737678', // Mid Grey- Neutral
    },
    gentlemanCharacter: {
      width: 71,
      height: 104,
      top: 40,
      left: 290,
    },
    girlManCharacter: {
      width: 106,
      height: 83,
      top: 63,
      left: 251,
    },
    resendOtp: {
      width: 94,
      height: 22,
    },
    subscriptionPlanCard: {
      width: 280,
      height: 360,
      borderRadius: 12,
      borderWidth: 0.5,
      p: 20,
      gap: 20,
      borderColor: '#A24295',
      icon: {
        width: 160,
        height: 160,
      },
    },
    opLights: {
      width: 85.56,
      height: 74.*************,
      top: 10.02,
      left: 234,
    },
    alliedCardiacMaleCharacter: {
      width: 71.96,
      height: 140.21,
    },
    alliedCardiacFemaleCharacter: {
      width: 84.71,
      height: 248.17,
      top: 0,
    },
    prestigeSubscriptionSection: {
      width: 413,
      height: 120,
      top: 587, // Adjusted to align with View Plan button
      left: 740, // Adjusted to be next to View Plan button with gap
      borderRadius: 12,
      borderWidth: 1,
      borderColor: '#A24295',
      borderStyle: 'solid',
      colors: {
        primary: '#A24295',
        secondary: '#DCB7D7',
        tertiary: '#FFFFFF',
      },
    },
  },
  breakpoints: {
    values: {
      xs: 0,
      sm: 600,
      smd: 750,
      md: 900,
      lg: 1280,
      xl: 1536,
      xxl: 1924,
    },
  },
});
