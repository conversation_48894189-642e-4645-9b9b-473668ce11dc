// src/components/dialogs/ScheduledPostsFolderDialog.tsx
'use client';

import { useState, useMemo } from 'react';
import {
  Dialog,
  Box,
  Typography,
  CircularProgress,
  IconButton,
} from '@mui/material';
import { ConfirmationDialog } from './ConfirmationDialog';
import {
  useScheduledPosts,
  useUpdateScheduledPost,
  useDeleteScheduledPost,
} from '@minicardiac-client/apis';
import { ScheduledPostCard } from './ScheduledPostsFolderCards';
import { getCdnUrl } from '@minicardiac-client/utilities';
import { Iconify } from '../iconify';
import { toast } from 'react-toastify';

interface ScheduledPostsFolderDialogProps {
  open: boolean;
  onClose: () => void;
}

export default function ScheduledPostsFolderDialog({
  open,
  onClose,
}: ScheduledPostsFolderDialogProps) {
  const {
    data: scheduledPosts = [],
    isLoading,
    error,
    refetch,
  } = useScheduledPosts();
  const { mutateAsync: updatePost } = useUpdateScheduledPost();
  const { mutate: deletePost } = useDeleteScheduledPost();
  const [editingPostId, setEditingPostId] = useState<string | null>(null);

  const handleStartEdit = (postId: string) => {
    setEditingPostId(postId);
  };

  const handleSaveEdit = async (postId: string, newContent: string) => {
    try {
      const post = scheduledPosts.find((p) => p.id === postId);
      if (!post) return;

      // No debug logs in production

      // Frontend validation for media posts
      if (post.postType === 'media' && post.postMedias?.length) {
        // Check if any media is missing valid alt text
        const mediaWithMissingAltText = post.postMedias.find(
          media => !media.altText || media.altText.trim().length < 3
        );

        if (mediaWithMissingAltText) {
          toast.error('Please provide descriptive alt text (minimum 3 characters) for all media files');
          return; // Prevent save if any media is missing valid alt text
        }
      }

      // Prepare payload based on post type
      const basePayload = {
        content: newContent,
        postStatus: 'scheduled',
        community: 'PROFESSIONAL',
        audience: 'BOTH',
        tags: [],
        postScheduleDate: post.postScheduleDate,
      };

      let payload;
      switch (post.postType) {
        case 'text':
        case 'question':
          payload = basePayload;
          break;
        case 'poll':
          payload = {
            ...basePayload,
            question: post.title || '',
            durationDays: 1,
            allowCustomAnswer: true,
            options: [],
          };
          break;
        case 'link':
          payload = {
            ...basePayload,
            link: post.linkUrl || '',
          };
          break;
        case 'article':
          payload = {
            ...basePayload,
            title: post.title || '',
            body: post.content || '',
            coverImagePath: post.coverImagePath || '',
          };
          break;
        case 'media':
          // For media posts, construct the medias array with proper alt text handling
          payload = {
            ...basePayload,
            medias: post.postMedias?.map((media) => {
              // Create a base media payload with required fields
              const mediaPayload: any = {
                mediaPath: media.mediaPath,
                mediaType: media.mediaType,
              };
              // Only include altText if it meets the minimum length requirement
              if (media.altText && media.altText.trim().length >= 3) {
                mediaPayload.altText = media.altText.trim();
              }
              return mediaPayload;
            }) || [],
          };
          break;
        default:
          payload = basePayload;
      }

      await updatePost({
        postId,
        postType: post.postType || 'text',
        payload,
      });

      setEditingPostId(null);
      await refetch();
      toast.success('Post updated successfully');
    } catch (error) {
      console.error('Error updating post:', error);
      toast.error('Failed to update post');
      throw error;
    }
  };

  const handleCancelEdit = () => {
    setEditingPostId(null);
  };

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [postToDelete, setPostToDelete] = useState<string | null>(null);

  const handleDeleteClick = (postId: string) => {
    setPostToDelete(postId);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = () => {
    if (!postToDelete) return;

    deletePost(postToDelete, {
      onSuccess: () => {
        setDeleteDialogOpen(false);
        setPostToDelete(null);
        // The onSuccess callback in the mutation will handle the success toast
      },
      onError: (error) => {
        console.error('Error deleting post:', error);
        toast.error('Failed to delete post');
      }
    });
  };

  const handleCancelDelete = () => {
    setDeleteDialogOpen(false);
    setPostToDelete(null);
  };

  const handleReschedule = (postId: string) => {
    // TODO: Implement reschedule functionality
    toast.info('Reschedule functionality coming soon');
  };

  // Transform the scheduled posts data to match the expected format
  const formattedScheduledPosts = useMemo(() => {
    return scheduledPosts.map((post) => {
      // Get the media URL using getCdnUrl if media exists
      const mediaPath = post.postMedias?.[0]?.mediaPath;
      const mediaUrl = mediaPath ? getCdnUrl(mediaPath) : undefined;
      const postDate = post.postedAt || '';
      const scheduleDate = post.postScheduleDate || postDate;
      const expiresDate = post.expiresAt || scheduleDate;

      return {
        id: post.id,
        title: post.title || 'Untitled Post',
        content: post.content || '',
        date: postDate,
        type: post.postType || 'text',
        media: mediaUrl,
        mediaType: post.postMedias?.[0]?.mediaType,
        scheduledDate: scheduleDate,
        postMedias: post.postMedias?.map((media) => ({
          ...media,
          mediaPath: getCdnUrl(media.mediaPath),
        })) || [],
        postType: post.postType,
        question: post.content,
        options: [],
        allowCustomAnswer: false,
        totalVotes: 0,
        expiresAt: expiresDate,
        postScheduleDate: scheduleDate,
      };
    });
  }, [scheduledPosts]);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="sm"
      PaperProps={{
        sx: {
          borderRadius: '12px',
          backgroundColor: 'white',
          p: 0,
        },
      }}
    >
      {/* Header */}
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        p={3}
        borderBottom="1px solid #eee"
        sx={{
          maxHeight: '70vh',
          overflowY: 'auto',
          '&::-webkit-scrollbar': { display: 'none' },
          scrollbarWidth: 'none',
          msOverflowStyle: 'none',
        }}
      >
        <Typography
          variant="h6"
          sx={{
            fontFamily: 'Plus Jakarta Sans',
            fontWeight: 600,
            fontSize: '1.5rem',
            color: '#1E1E1E',
          }}
        >
          Scheduled
        </Typography>
        <IconButton onClick={onClose}>
          <Iconify
            icon="mingcute:close-line"
            width={36}
            height={36}
            color="#A24295"
          />
        </IconButton>
      </Box>
      <Box p={3}>
        {isLoading ? (
          <Box display="flex" justifyContent="center" p={4}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Typography color="error" textAlign="center" p={2}>
            Error loading scheduled posts. Please try again.
          </Typography>
        ) : formattedScheduledPosts.length === 0 ? (
          <Typography textAlign="center" color="text.secondary" p={2}>
            No scheduled posts found
          </Typography>
        ) : (
          formattedScheduledPosts.map((post) => (
            <ScheduledPostCard
              key={post.id}
              post={post}
              onEdit={handleSaveEdit}
              onDelete={handleDeleteClick}
              onReschedule={handleReschedule}
              isEditing={editingPostId === post.id}
              onStartEdit={handleStartEdit}
              onCancelEdit={handleCancelEdit}
            />
          ))
        )}
      </Box>
      <ConfirmationDialog
        open={deleteDialogOpen}
        title="Delete Scheduled Post"
        message="Are you sure you want to delete this scheduled post? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
      />
    </Dialog>
  );
}
