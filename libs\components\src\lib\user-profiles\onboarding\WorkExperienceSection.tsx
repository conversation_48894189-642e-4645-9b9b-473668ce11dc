import { Box, Typography } from '@mui/material';
import CustomForm from './CustomForm';

export const WorkExperienceSection = () => {
  const formData = [
    {
      // title: 'Academic Degree',
      rows: [
        {
          columns: 1,
          fields: [
            {
              label: 'Workplace',
              placeholder: 'MD',
              sx: { maxWidth: '396px', width: '100%' },
            },
          ],
        },
        {
          columns: 3,
          fields: [
            {
              label: 'Role',
              placeholder: 'Your designation at this workplace',
              sx: { maxWidth: '360px', width: '100%' },
            },
            {
              label: 'From',
              placeholder: '2015',
              sx: { maxWidth: { xs: '100%', sm: '190px' }, width: '100%' },
              type: 'year',
            },
            {
              label: 'To',
              placeholder: '2018',
              sx: { maxWidth: { xs: '100%', sm: '190px' }, width: '100%' },
              type: 'year',
            },
          ],
        },
      ],
      addButtonLabel: 'Workplace',
    },
  ];

  return (
    <Box
      maxWidth="976px"
      width="100%"
      mx="auto"
      p={{ xs: '16px', sm: '20px' }}
      borderRadius="8px"
      bgcolor="#fff"
    >
      <Typography fontSize="24px" fontWeight={600} mb="20px">
        Work Experience
      </Typography>

      {formData.map((form, idx) => (
        <CustomForm
          key={idx}
          rows={form.rows}
          addButtonLabel={`Add ${form.addButtonLabel}`}
        />
      ))}
    </Box>
  );
};
