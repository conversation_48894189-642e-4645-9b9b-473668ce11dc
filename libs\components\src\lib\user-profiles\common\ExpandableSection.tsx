// components/ClinicalInterests.tsx
'use client';

import { Box, Typography } from '@mui/material';
import ExpandableCard from './ExpandableCard';

const ExpandableSection = ({ title }: { title: string }) => {
  const cardTitle = 'Cardiac Screening & Preventive Care';
  const cardContent =
    'A comprehensive heart health evaluation tailored to your medical history, symptoms, and risk factors. During your consultation, I assess cardiovascular risks such as hypertension, high cholesterol, and family history of heart disease. If needed, diagnostic tests are recommended to develop a personalized prevention or treatment plan.';

  return (
    <Box
      maxWidth="976px"
      width="100%"
      bgcolor="white"
      p={{ xs: '16px', sm: '20px' }}
      borderRadius="8px"
    >
      <Typography
        fontSize={{ xs: '20px', smd: '24px' }}
        fontWeight={600}
        mb="20px"
      >
        {title}
      </Typography>

      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: '20px',
        }}
      >
        {[0, 1, 2, 3].map((_, index) => (
          <ExpandableCard key={index} title={cardTitle} content={cardContent} />
        ))}
      </Box>
    </Box>
  );
};

export default ExpandableSection;
