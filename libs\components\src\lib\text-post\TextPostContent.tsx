import { Box } from '@mui/material';

// Keeping it as reference
// const content = `
// <p style="font-size:16px; font-weight:500; margin-bottom:16px;">
//   Rethinking Our Approach to HFpEF
// </p>

// <p style="font-size:12px; font-weight:400; margin-bottom:16px;">
//   Lately, I’ve been reflecting on the evolving landscape of heart failure with preserved ejection fraction (HFpEF). Despite increasing recognition and clinical focus, we still lack robust, targeted therapies that consistently improve long-term outcomes.
// </p>

// <p style="font-size:12px; font-weight:400; margin-bottom:16px;">
//   With more patients—especially older adults and women—being diagnosed, are we doing enough to stratify risk early and personalize management strategies? The recent guidelines emphasize comorbidity optimization, but we need more large-scale trials to refine treatment algorithms.
// </p>

// <p style="font-size:12px; font-weight:400; margin-bottom:16px;">
//   Curious to hear how others are managing these cases in real-world practice. Are you integrating newer agents like SGLT2 inhibitors routinely? How do you handle the diagnostic gray zones?
// </p>

// <p style="font-size:12px; font-weight:700;">
//   Let’s keep the conversation going—we learn the most from each other. #Cardiology #HeartFailure #HFpEF #ClinicalPractice #CardioCommunity
// </p>`;

const TextPostContent = ({ content }: { content: string }) => {
  return (
    <Box
      mt={{ xs: '0px', sm: '20px' }}
      lineHeight="22px"
      sx={{
        fontSize: '16px',
        color: '#1E1E1E',
        '& p': { marginBottom: '16px' },
      }}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
};

export default TextPostContent;
