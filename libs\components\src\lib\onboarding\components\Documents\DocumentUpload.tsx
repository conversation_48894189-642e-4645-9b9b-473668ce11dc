import { Box, Stack, alpha } from '@mui/material';
import { useState } from 'react';

import CustomizedSteppers from '../Stepper/Stepper';
import { DOCUMENT_REQUIREMENTS } from '../../constants/onboarding.constants';
import { Subtitle } from '../../../auth/components/Subtitle';
import { UploadStatusesState } from '@minicardiac-client/types';
import { DocumentUploadField } from './DocumentUploadField';

const fileTypeError = 'Incorrect file type.';
const networkError = 'Network error.';

// Mock function for file upload
const uploadFile = (fieldId: string) => {
  return new Promise((resolve, reject) => {
    // Simulate network request
    setTimeout(() => {
      // Random success or failure
      if (Math.random() > 0.2) {
        resolve({ success: true, fieldId });
      } else {
        reject({
          success: false,
          fieldId,
          error: Math.random() > 0.5 ? fileTypeError : networkError,
        });
      }
    }, 2000);
  });
};

const useGetDocumentDetails = () => {
  return DOCUMENT_REQUIREMENTS;
};

const DocumentUploadForm = ({
  hideSteppers = false,
}: { hideSteppers?: boolean } = {}) => {
  const [uploadStatuses, setUploadStatuses] = useState<UploadStatusesState>({});

  const documentFields = useGetDocumentDetails();

  // Generate a unique ID for each upload
  const generateUniqueId = () =>
    `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  const handleFileUpload = (file: File, fieldId: string) => {
    // Find the field config to check maxCount
    const fieldConfig = documentFields.find((field) => field.id === fieldId);
    if (!fieldConfig) return;

    // Check if max limit is reached
    const currentUploads = uploadStatuses[fieldId] || [];
    if (currentUploads.length >= fieldConfig.maxCount) {
      console.warn(
        `Maximum number of files (${fieldConfig.maxCount}) reached for ${fieldConfig.documentType.name}`
      );
      return;
    }

    const uploadId = generateUniqueId();

    // Set initial uploading state
    setUploadStatuses((prev) => ({
      ...prev,
      [fieldId]: [
        ...(prev[fieldId] || []),
        {
          id: uploadId,
          file,
          status: 'uploading',
          progress: 0,
        },
      ],
    }));

    // Simulate progress
    const progressInterval = setInterval(() => {
      setUploadStatuses((prev) => {
        const currentUploads = prev[fieldId] || [];
        const uploadIndex = currentUploads.findIndex(
          (upload) => upload.id === uploadId
        );

        if (uploadIndex === -1) return prev;

        const current = currentUploads[uploadIndex];
        if (current.status === 'uploading' && current.progress < 80) {
          const updatedUploads = [...currentUploads];
          updatedUploads[uploadIndex] = {
            ...current,
            progress: current.progress + 20,
          };

          return {
            ...prev,
            [fieldId]: updatedUploads,
          };
        }
        return prev;
      });
    }, 500);

    // Upload the file
    uploadFile(fieldId)
      .then(() => {
        clearInterval(progressInterval);
        setUploadStatuses((prev) => {
          const currentUploads = prev[fieldId] || [];
          const uploadIndex = currentUploads.findIndex(
            (upload) => upload.id === uploadId
          );

          if (uploadIndex === -1) return prev;

          const updatedUploads = [...currentUploads];
          updatedUploads[uploadIndex] = {
            ...updatedUploads[uploadIndex],
            status: 'success',
            progress: 100,
          };

          return {
            ...prev,
            [fieldId]: updatedUploads,
          };
        });
      })
      .catch((error) => {
        clearInterval(progressInterval);
        setUploadStatuses((prev) => {
          const currentUploads = prev[fieldId] || [];
          const uploadIndex = currentUploads.findIndex(
            (upload) => upload.id === uploadId
          );

          if (uploadIndex === -1) return prev;

          const updatedUploads = [...currentUploads];
          updatedUploads[uploadIndex] = {
            ...updatedUploads[uploadIndex],
            status: 'error',
            progress: 100,
            errorMessage: error.error,
          };

          return {
            ...prev,
            [fieldId]: updatedUploads,
          };
        });
      });
  };

  const handleFileRemove = (fieldId: string, uploadId: string) => {
    setUploadStatuses((prev) => {
      const currentUploads = prev[fieldId] || [];
      const updatedUploads = currentUploads.filter(
        (upload) => upload.id !== uploadId
      );

      if (updatedUploads.length === 0) {
        const { [fieldId]: _, ...rest } = prev;
        return rest;
      }

      return {
        ...prev,
        [fieldId]: updatedUploads,
      };
    });
  };

  const handleFileRetry = (fieldId: string, uploadId: string) => {
    const currentUploads = uploadStatuses[fieldId] || [];
    const uploadIndex = currentUploads.findIndex(
      (upload) => upload.id === uploadId
    );

    if (uploadIndex === -1) return;

    const { file } = currentUploads[uploadIndex];
    if (!file) return;

    // Remove the failed upload
    handleFileRemove(fieldId, uploadId);

    // Create a new upload with the same file
    handleFileUpload(file, fieldId);
  };

  return (
    <Stack alignItems={'center'} mb={{ xs: '150px', sm: '10px' }}>
      {!hideSteppers && (
        <CustomizedSteppers
          activeStep={1} // Set to 1 for Document Upload (0-based index)
          steps={['Profile Setup', 'Document Upload', 'Adding Network']}
        />
      )}

      <Subtitle
        text={
          'Please upload your documents for verification. This is strictly confidential and will not be publicly accessible'
        }
        sx={{
          fontSize: {
            xs: '12px',
            sm: '16px',
          },
          mt: '40px',
        }}
      />
      <Box
        sx={{
          width: 1,
          borderRadius: 1,
          boxShadow: (theme) =>
            `0px 12px 24px 0px ${alpha(
              (theme.palette as any).neutral[500],
              0.25
            )}`,
        }}
      >
        {documentFields.map((field) => (
          <DocumentUploadField
            key={field.id}
            fieldId={field.id}
            label={field.documentType.name}
            optional={!field.isRequired}
            maxCount={field.maxCount}
            instructions={field.instructions}
            onFileUpload={handleFileUpload}
            onFileRemove={handleFileRemove}
            onFileRetry={handleFileRetry}
            uploadStatuses={uploadStatuses[field.id] || []}
          />
        ))}
      </Box>
    </Stack>
  );
};

export default DocumentUploadForm;
