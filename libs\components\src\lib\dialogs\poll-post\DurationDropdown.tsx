import {
  Box,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  SelectChangeEvent,
} from '@mui/material';

const DurationDropdown = ({
  duration,
  setDuration,
}: {
  duration: number;
  setDuration: (value: number) => void;
}) => {
  const handleChange = (event: SelectChangeEvent) => {
    setDuration(Number(event.target.value));
  };

  return (
    <FormControl
      sx={{
        width: { xs: '100%', sm: '300px' },
        height: '45px',
      }}
    >
      <InputLabel
        shrink
        id="duration-label"
        sx={{
          fontSize: '16px',
          fontWeight: 500,
          color: '#1E1E1E',
          '&.Mui-focused': {
            color: '#A24295',
          },
          backgroundColor: 'white',
          px: '4px',
        }}
      >
        Duration
      </InputLabel>
      <Select
        labelId="duration-label"
        value={duration.toString()}
        onChange={handleChange}
        displayEmpty
        sx={{
          width: { xs: '100%', sm: '300px' },
          height: '45px',
          fontSize: '16px',
          color: duration ? '#1E1E1E' : '#A3A3A3',
          '& .MuiSelect-select': {
            display: 'flex',
            alignItems: 'center',
          },
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderColor: '#A24295',
          },
          '& .MuiOutlinedInput-notchedOutline': {
            borderColor: '#E1E3E6',
          },
          '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: '#A3A3A3',
          },
        }}
        renderValue={(selected) => {
          if (!selected || selected === '0') {
            return (
              <Box sx={{ color: '#A3A3A3', fontSize: '16px', fontWeight: 400 }}>
                Set a duration for your poll
              </Box>
            );
          }
          
          const durationMap: Record<string, string> = {
            '1': '24 hours',
            '3': '3 days', 
            '7': '1 week',
          };
          
          return durationMap[selected] || selected;
        }}
      >
        <MenuItem value={1}>24 hours</MenuItem>
        <MenuItem value={3}>3 days</MenuItem>
        <MenuItem value={7}>1 week</MenuItem>
      </Select>
    </FormControl>
  );
};

export default DurationDropdown;
