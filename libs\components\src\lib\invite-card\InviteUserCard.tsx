import { Avatar, Box, IconButton, Typography } from '@mui/material';
import TextInput from '../form-fields/TextInput';
import DeleteIcon from '../Icons/DeleteIcon';

export const InvitedUserCard = ({
  name,
  email,
  profilePic,
  role,
  onDelete,
}: {
  name?: string;
  email: string;
  profilePic?: string;
  role?: string;
  onDelete: () => void;
}) => {
  return (
    <Box
      width="153px"
      height="221px"
      p="16px"
      border="1px solid #A24295"
      borderRadius="12px"
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="space-between"
      minWidth={153}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: '16px',
        }}
      >
        <Avatar src={profilePic} sx={{ width: 64, height: 64 }} />

        {/* Name */}
        {name ? (
          <Typography fontSize="16px" fontWeight={400} mt="8px">
            {name}
          </Typography>
        ) : (
          <TextInput
            placeholder={email}
            label="Name"
            value=""
            onChange={() => {
              console.log('Add name'); // Replace with actual logic
            }}
            sx={{
              '& .MuiInputBase-root': {
                height: '28px',
                padding: '0 8px',
                fontSize: '12px',
              },
            }}
          />
        )}

        {/* Role */}
        {role ? (
          <Typography fontSize="12px" fontWeight={400} color="#6B7280">
            {role}
          </Typography>
        ) : (
          <TextInput
            placeholder={'Employee role'}
            label="Role"
            value=""
            onChange={() => {
              console.log('Add role'); // Replace with actual logic when api integration
            }}
            sx={{
              '& .MuiInputBase-root': {
                height: '28px',
                padding: '0 8px',
                fontSize: '12px',
              },
            }}
          />
        )}
      </Box>

      <IconButton onClick={onDelete}>
        <DeleteIcon fill="#A24295" />
      </IconButton>
    </Box>
  );
};
