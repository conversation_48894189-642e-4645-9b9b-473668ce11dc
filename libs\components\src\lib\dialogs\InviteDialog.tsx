'use client';

import {
  Di<PERSON>Title,
  <PERSON>alog<PERSON>ontent,
  <PERSON>ton,
  Box,
  Typography,
  Checkbox,
  FormControlLabel,
  useMediaQuery,
  useTheme,
  IconButton,
} from '@mui/material';
import React, { useState, useCallback } from 'react';

import CustomDialog from './CustomDialog';
import { Iconify } from '../iconify';
import CloseIcon from '@mui/icons-material/Close';

const EmailChip = React.memo(
  ({
    email,
    onDelete,
  }: {
    email: string;
    onDelete: (email: string) => void;
  }) => (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        borderRadius: '8px',
        backgroundColor: '#F3F4F6',
        px: '8px',
        py: '4px',
        mr: '12px',
        mt: '4px',
      }}
    >
      <Typography sx={{ fontSize: '12px', fontWeight: 400 }}>
        {email}
      </Typography>
      <Iconify
        icon="mdi:close"
        onClick={() => onDelete(email)}
        style={{
          marginLeft: '8px',
          cursor: 'pointer',
          color: '#A24295',
          fontSize: '16px',
        }}
      />
    </Box>
  )
);

const InviteDialog = ({
  open,
  onClose,
  onInvite,
}: {
  open: boolean;
  onClose: () => void;
  onInvite: (emails: string[]) => void;
}) => {
  const [emailInput, setEmailInput] = useState('');
  const [emailList, setEmailList] = useState<string[]>([]);
  const [addAsConnection, setAddAsConnection] = useState(true);

  const canSend = emailInput.trim() !== '' || emailList.length > 0;
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      if ((e.key === 'Enter' || e.key === ',') && emailInput.trim()) {
        e.preventDefault();
        const newEmails = emailInput
          .split(',')
          .map((email) => email.trim())
          .filter((email) => email && !emailList.includes(email));
        setEmailList((prev) => [...prev, ...newEmails]);
        setEmailInput('');
      }
    },
    [emailInput, emailList]
  );

  const handleDelete = useCallback((emailToDelete: string) => {
    setEmailList((prev) => prev.filter((email) => email !== emailToDelete));
  }, []);

  const handleSend = useCallback(() => {
    onInvite(emailList);
    setEmailList([]);
    setEmailInput('');
    setAddAsConnection(true);
    onClose();
  }, [emailList, onInvite, onClose]);

  const content = (
    <>
      <Typography mb={'24px'}>
        Would you like to invite someone to join MiniCardiac? Add their email
        below and we’ll take care of the rest.
      </Typography>

      {/* Input Field */}
      <Box
        sx={{
          border: '1px solid #ccc',
          borderRadius: '4px',
          padding: '6px 8px',
          minHeight: '56px',
          display: 'flex',
          flexWrap: 'wrap',
          alignItems: 'center',
          '&:focus-within': {
            borderColor: '#A24295',
          },
          position: 'relative',
        }}
      >
        <Typography
          sx={{
            position: 'absolute',
            top: '-15px',
            left: '20px',
            backgroundColor: '#fff',
            fontSize: '16px',
            fontWeight: 500,
          }}
        >
          Send request to
        </Typography>
        {emailList.map((email) => (
          <EmailChip key={email} email={email} onDelete={handleDelete} />
        ))}
        <input
          value={emailInput}
          onChange={(e) => setEmailInput(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={
            emailList.length === 0 ? 'Type emails separated by comma' : ''
          }
          style={{
            flex: 1,
            border: 'none',
            outline: 'none',
            fontSize: '16px',
            minWidth: '120px',
            marginTop: '4px',
            backgroundColor: 'white',
          }}
        />
      </Box>

      <FormControlLabel
        control={
          <Checkbox
            checked={addAsConnection}
            onChange={(e) => setAddAsConnection(e.target.checked)}
            sx={{
              color: '#A24295',
              '&.Mui-checked': {
                color: '#A24295',
              },
            }}
          />
        }
        label="Add as connection when they join"
        sx={{ mt: '24px' }}
      />

      <Typography fontWeight={600} mt={2}>
        You can track the invites you send through My Network.
      </Typography>
    </>
  );

  // ------------------------------
  // Mobile View
  // ------------------------------
  if (isMobile) {
    if (!open) return null;

    return (
      <>
        {/* Backdrop */}
        <Box
          onClick={onClose}
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 1299,
          }}
        />

        {/* Bottom Sheet */}
        <Box
          onClick={(e) => e.stopPropagation()}
          sx={{
            position: 'fixed',
            bottom: 0,
            left: 0,
            right: 0,
            backgroundColor: 'background.paper',
            borderRadius: '24px 24px 0 0',
            padding: '24px',
            zIndex: 1300,
            maxHeight: '90vh',
            overflowY: 'auto',
          }}
        >
          {/* Handle + Close */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              pb: 1,
            }}
          >
            <Box
              sx={{
                width: 36,
                height: 4,
                backgroundColor: 'grey.400',
                borderRadius: 2,
              }}
            />
          </Box>

          <Box
            display="flex"
            justifyContent="space-between"
            alignItems="center"
            mb={2}
          >
            <Typography variant="h6" fontWeight={600}>
              Invite Employees
            </Typography>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>

          {content}

          {/* Bottom Actions */}
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: 2,
              mt: 4,
            }}
          >
            <Button
              variant="contained"
              disabled={!canSend}
              onClick={handleSend}
              sx={{
                backgroundColor: canSend ? '#A24295' : '#A3A3A3',
                color: '#fff',
                fontWeight: 600,
                textTransform: 'none',
                '&:hover': {
                  backgroundColor: canSend ? '#932080' : '#A3A3A3',
                },
              }}
            >
              Send
            </Button>
            <Button
              variant="text"
              onClick={onClose}
              sx={{
                color: '#A24295',
                fontWeight: 600,
              }}
            >
              Cancel
            </Button>
          </Box>
        </Box>
      </>
    );
  }

  // ------------------------------
  // Desktop View
  // ------------------------------
  return (
    <CustomDialog
      open={open}
      onClose={onClose}
      title=""
      sx={{
        p: 0,
        px: { xs: 0, sm: '80px' },
        pt: { xs: 0, sm: '50px' },
        alignItems: { xs: 'stretch', sm: 'start' },
        '.MuiDialog-paper': {
          maxHeight: { xs: '100%', sm: 'calc(100% - 64px)' },
          maxWidth: { xs: '100%', sm: '798px' },
        },
      }}
    >
      <Box
        sx={{
          p: '40px',
          display: 'flex',
          flexDirection: 'column',
          gap: '40px',
        }}
      >
        <DialogTitle sx={{ p: 0, fontWeight: 500, fontSize: '28px' }}>
          Invite Employees
        </DialogTitle>
        <DialogContent sx={{ p: 0 }}>{content}</DialogContent>
      </Box>
      <Box
        display="flex"
        justifyContent="center"
        position="sticky"
        bottom={0}
        gap="20px"
        sx={{
          backgroundColor: '#fff',
          py: '24px',
          boxShadow: '0px -4px 20px 0px #A3A3A31F',
        }}
      >
        <Button
          variant="outlined"
          onClick={onClose}
          sx={{
            width: '156px',
            height: '40px',
            fontWeight: 700,
            borderColor: '#A24295',
            color: '#A24295',
            textTransform: 'none',
            '&:hover': {
              backgroundColor: '#f9f0f5',
            },
          }}
        >
          Cancel
        </Button>
        <Button
          variant="contained"
          onClick={handleSend}
          disabled={!canSend}
          sx={{
            width: '156px',
            height: '40px',
            fontWeight: 700,
            backgroundColor: canSend ? '#A24295' : '#A3A3A3',
            color: '#fff',
            textTransform: 'none',
            '&:hover': {
              backgroundColor: canSend ? '#932080' : '#A3A3A3',
            },
          }}
        >
          Send
        </Button>
      </Box>
    </CustomDialog>
  );
};

export default InviteDialog;
