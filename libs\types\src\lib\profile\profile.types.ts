export interface OrganisationProfileFormData {
  introductoryStatement?: string;
  profileImageUrl?: string;
  profileImageUrlThumbnail?: string;
  category?: string;
  location?: string;
  mainProfession?: string;
  mapLink?: string;
}

export interface ProfessionalProfileFormData {
  introductoryStatement?: string;
  profileImageUrl?: string | null;
  profileImageUrlThumbnail?: string | null;
  title?: string;
  qualifications?: string;
  jobTitle?: string;
  employerId?: string;
  mainProfession?: string;
  category?: string;
  professionalType: 'ALLIED_CARDIAC' | 'CARDIAC_SURGEON' | 'CARDIOLOGIST';
}
