import { auth } from '../firebase/firebase-client.js';

import { sessionLogin } from './auth-api.js';
import { resetAuthMode } from '../http-client.js';

export const establishSession = async (): Promise<boolean> => {
  try {
    if (!auth) return false;

    const currentUser = auth.currentUser;
    if (!currentUser) return false;

    const idToken = await currentUser.getIdToken();
    await sessionLogin({ idToken });
    resetAuthMode();

    return true;
  } catch (error) {
    return false;
  }
};

export const handleSessionExpiration = async (): Promise<boolean> => {
  try {
    if (!auth) return false;

    const currentUser = auth.currentUser;
    if (!currentUser) return false;

    const freshIdToken = await currentUser.getIdToken(true);
    await sessionLogin({ idToken: freshIdToken });
    resetAuthMode();

    return true;
  } catch (error) {
    return false;
  }
};

export const isSessionValid = async (): Promise<boolean> => {
  try {
    if (!auth) return false;

    const currentUser = auth.currentUser;
    if (!currentUser) return false;

    const tokenResult = await currentUser.getIdTokenResult();
    const authTime = new Date(tokenResult.authTime).getTime();
    const now = Date.now();
    const fiveDaysInMs = 5 * 24 * 60 * 60 * 1000;

    return now - authTime < fiveDaysInMs;
  } catch (error) {
    return false;
  }
};
