export interface Award {
  title: string;
  description: string;
  recipient: string;
  icon: string;
}

export interface Profile {
  name: string;
  college: string;
  profilePic: string;
  backgroundImage: string;
  connections: number;
  intro: string;
  video: string;
  awards: Award[];
  about: string;
  degrees?: string;
  designation?: string;
  workplace?: string;
  rating?: number;
  followers: number;
}

export interface BannerOptions {
  blurBackground: boolean;
  addBackgroundTint: boolean;
  tintColor: string;
  textColor: string;
}

export enum LayoutType {
  Default = 0,
  Left = 1,
  Right = 2,
}

export const FIXED_SECTION = 'About';

export const INITIAL_SECTIONS = [
  FIXED_SECTION,
  'Qualifications',
  'Work Experience',
  'Clinical Interests',
  'Areas of Expertise',
  'FAQ',
];
