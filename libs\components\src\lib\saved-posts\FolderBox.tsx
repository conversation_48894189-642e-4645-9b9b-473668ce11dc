'use client';

import {
  Box,
  SxProps,
  Typography,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import PostCard from '../content-posts/PostCard';
import { useRouter } from 'next/navigation';
import { Iconify } from '../iconify';
import Image from 'next/image';
import { MEDIA_POSTS } from '../auth';

interface ActionButtonProps {
  label: string;
  onClick: () => void;
}

interface FolderBoxProps {
  name: string;
  sx?: SxProps;
  actionButtonProps?: ActionButtonProps;
}

export default function FolderBox({
  name,
  sx,
  actionButtonProps,
}: FolderBoxProps) {
  const theme = useTheme();
  const router = useRouter();
  const isSmallOrMedium = useMediaQuery(theme.breakpoints.down('lg'));

  const handleNavigate = () => {
    router.push(`/feed?folder=${encodeURIComponent(name)}`);
  };

  const renderImageGrid = (images: string[]) => {
    const count = images.length;

    if (count === 1) {
      return (
        <Image
          src={images[0]}
          alt="Post image"
          width={320}
          height={148}
          style={{
            borderRadius: 8,
            objectFit: 'cover',
            width: '100%',
            height: '100%',
          }}
        />
      );
    }

    return (
      <Box
        sx={{
          width: '100%',
          height: '148px',
          display: 'grid',
          borderRadius: '8px',
          overflow: 'hidden',
          gridTemplateColumns: count === 2 ? '1fr 1fr' : '1fr 1fr',
          gridTemplateRows: count >= 3 ? '1fr 1fr' : '1fr',
        }}
      >
        {images.slice(0, 4).map((img, index) => (
          <Box
            key={index}
            sx={{
              position: 'relative',
              width: '100%',
              height: '100%',
              gridColumn: count === 3 && index === 2 ? '1 / span 2' : 'auto',
            }}
          >
            <Image
              src={img}
              alt={`post-${index}`}
              fill
              style={{
                objectFit: 'cover',
              }}
            />
          </Box>
        ))}
      </Box>
    );
  };

  return (
    <Box
      onClick={handleNavigate}
      sx={{
        borderRadius: '8px',
        padding: '20px',
        transition: 'background-color 0.2s ease',
        '&:hover': {
          backgroundColor: '#F6ECF4',
        },
        width: '100%',
        cursor: 'pointer',
        maxHeight: { xs: '232px', sm: '251px' },
        ...sx,
      }}
    >
      {/* Folder name and button */}
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={2}
      >
        <Typography
          fontWeight={700}
          fontSize={{ xs: '16px', sm: '20px' }}
          color="#A24295"
        >
          {name}
        </Typography>

        <Iconify
          icon={'mdi:dots-horizontal'}
          color="#A24295"
          width={30}
          height={30}
        />
      </Box>

      {/* Post previews - Only show in desktop */}
      {!isSmallOrMedium ? (
        <Box
          sx={{
            display: 'flex',
            gap: '16px',
            justifyContent: { md: 'space-between' },
          }}
        >
          {[...Array(2)].map((_, i) => (
            <PostCard
              key={i}
              post={{
                postId: 'post123',
                content: 'Check out this amazing sunset over the mountains!',
                postType: 'image',
                workspaceId: 'workspace456',
                publisherName: 'John Doe',
                username: 'johndoe',
                profileImageUrlThumbnail:
                  'https://randomuser.me/api/portraits/men/32.jpg',
                medias: [
                  {
                    mediaPath:
                      'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=700&q=80',
                    mediaType: 'image',
                    altText: 'Sunset over mountains',
                  },
                ],
              }}
            />
          ))}
        </Box>
      ) : (
        // Mobile image preview box
        renderImageGrid(MEDIA_POSTS)
      )}
    </Box>
  );
}
