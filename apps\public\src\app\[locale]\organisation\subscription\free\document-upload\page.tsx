'use client';

import React from 'react';
import { Box } from '@mui/material';
import {
  useSnackbar,
  DocumentUploadPageTemplate,
  ActionButtonsTemplate,
} from '@minicardiac-client/components';
import DocumentUploadForm from '@/libs/components/src/lib/onboarding/components/Documents/DocumentUpload';
import {
  useAuth,
  uploadDocuments,
  refreshSession,
} from '@minicardiac-client/apis';
import { useRouter } from '@/apps/public/src/i18n/navigation';

export default function OrganisationFreeDocumentUploadPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const { showSuccess, showError } = useSnackbar();

  const { authState } = useAuth();

  const handleDoThisLater = () => {
    // Navigate to landing page
    router.push('/feed?fromSignup=true');
  };

  const handleContinue = async () => {
    setIsSubmitting(true);
    setError(null);

    try {
      // Ensure session cookie is fresh before upload
      await refreshSession();

      // Upload documents
      await uploadDocuments();

      showSuccess('Documents saved successfully!');

      router.push('/feed?fromSignup=true');
    } catch (err) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : 'Failed to upload documents. Please try again.';
      showError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <DocumentUploadPageTemplate
      userName={authState.user?.displayName || ''}
      subtitleText="Let's set up your Organisation Account!"
      showBackButton={true}
      onBack={() => router.back()}
      currentStep={1}
      steps={['Profile Setup', 'Document Upload', 'Adding Network']}
    >
      <DocumentUploadForm hideSteppers={true} />

      {/* Action Buttons using shared template */}
      <ActionButtonsTemplate
        onSave={handleContinue}
        onSkip={handleDoThisLater}
        isSubmitting={isSubmitting}
        saveButtonText="Save and Continue"
        skipButtonText="Do this later"
        variant="organization"
      />

      {/* Error Display */}
      {error && (
        <Box sx={{ color: 'error.main', mt: 2, textAlign: 'center' }}>
          {error}
        </Box>
      )}
    </DocumentUploadPageTemplate>
  );
}
