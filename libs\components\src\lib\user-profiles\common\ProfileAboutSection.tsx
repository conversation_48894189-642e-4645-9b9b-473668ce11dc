'use client';

import { Box } from '@mui/material';
import NavigatorTabs from '../common/NavigatorTabs';
import VideoAndAwardsSection from '../common/VideoAndAwardsSection';
import AboutSection from '../common/AboutSection';
import EditButton from '../../buttons/EditButton';
import React from 'react';
import { Profile } from '@minicardiac-client/types';

interface Props {
  profile: Profile;
  showEdit?: boolean; // If you want to optionally show/hide edit
}

function ProfileAboutSection({ profile, showEdit = true }: Props) {
  return (
    <Box
      mt="20px"
      display="flex"
      p={{ xs: '16px', sm: '0px' }}
      sx={{
        '&:hover .edit-about-button': {
          pointerEvents: 'auto',
          display: { xs: 'none', smd: 'flex' },
        },
        position: 'relative',
      }}
    >
      <Box
        maxWidth="976px"
        width="100%"
        bgcolor="white"
        p={'20px'}
        borderRadius={'8px'}
      >
        <NavigatorTabs activeTab="About" />

        <Box mt="16px">
          {(profile.video || profile.awards?.length > 0) && (
            <VideoAndAwardsSection
              video={profile.video}
              awards={profile.awards}
            />
          )}

          {profile.about && <AboutSection about={profile.about} />}
        </Box>
      </Box>

      {showEdit && (
        <EditButton
          className="edit-about-button"
          sx={{
            height: '38px',
            color: '#A24295',
            backgroundColor: 'transparent',
          }}
          width={20}
          height={20}
          label="Edit Page"
        />
      )}
    </Box>
  );
}

export default React.memo(ProfileAboutSection);
