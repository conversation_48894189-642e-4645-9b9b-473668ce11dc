'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@minicardiac-client/apis';

import DashboardLayout from '@/libs/components/src/lib/layout/DashboardLayout';
import { ProtectedRoute } from '../../../components/protected-route';
import {
  FullPageLoader,
  GhostPostPreview,
} from '@minicardiac-client/components';

import {
  DndContext,
  DragStartEvent,
  DragEndEvent,
  DragOverlay,
  useSensors,
  PointerSensor,
  useSensor,
  KeyboardSensor,
  MeasuringStrategy,
} from '@dnd-kit/core';

import DragAndDropDialog from '@/libs/components/src/lib/dialogs/DragAndDrop';
// import { useHandleDragAction } from '@minicardiac-client/utilities';
import ErrorBoundary from '@/libs/components/src/lib/error-boundary/ErrorBoundary';
// import { useRouter } from '../../../i18n/navigation';

type DragActionState = {
  postId: string;
  folderName: string;
} | null;

export default function FeedPageWrapper() {
  const { authState } = useAuth();
  // const router = useRouter();

  useEffect(() => {
    document.title = `Feed | Minicardiac`;
  }, []);

  // Redirect to login if not authenticated
  // useEffect(() => {
  //   if (!authState.isLoading && !authState.isAuthenticated) {
  //     router.push('/signin');
  //   }
  // }, [authState.isLoading, authState.isAuthenticated, router]);

  const [draggedPostInfo, setDraggedPostInfo] = useState<{
    id: string;
    type: 'question' | 'poll' | 'article' | 'media' | 'text';
  } | null>(null);

  const [dragAction, setDragAction] = useState<DragActionState>(null);
  const [showDragDialog, setShowDragDialog] = useState(false);

  // useHandleDragAction(
  //   dragAction?.postId ?? null,
  //   dragAction?.folderName ?? null
  // );

  const handleDragStart = (event: DragStartEvent) => {
    const fullId = String(event.active?.id);
    const [id, type] = fullId.split('--');

    if (id && type) {
      setDraggedPostInfo({
        id,
        type: type as 'question' | 'poll' | 'article' | 'media' | 'text',
      });
    }
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    setDraggedPostInfo(null);

    const activeId = String(active?.id);
    const overId = String(over?.id);

    if (activeId && overId.startsWith('folder-')) {
      const folderName = overId.replace('folder-', '');
      setDragAction({ postId: activeId, folderName });
      setShowDragDialog(true);
    }
  };

  const closeDialog = () => setShowDragDialog(false);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor)
  );

  if (authState.isLoading) return <FullPageLoader open={true} />;

  // if (authState.isAuthenticated) {
  return (
    <ProtectedRoute>
      <ErrorBoundary>
        <DndContext
          sensors={sensors}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
          measuring={{ droppable: { strategy: MeasuringStrategy.Always } }}
        >
          <DashboardLayout />

          <DragOverlay
            dropAnimation={{
              duration: 150,
              easing: 'ease-in-out',
            }}
          >
            {draggedPostInfo && (
              <GhostPostPreview
                postId={draggedPostInfo.id}
                type={draggedPostInfo.type}
              />
            )}
          </DragOverlay>

          {showDragDialog && dragAction?.folderName && (
            <DragAndDropDialog
              open={true}
              onClose={closeDialog}
              folderName={dragAction.folderName}
              onMove={() => {
                closeDialog();
              }}
              onCopy={() => {
                closeDialog();
              }}
            />
          )}
        </DndContext>
      </ErrorBoundary>
    </ProtectedRoute>
  );
  // }

  // Return null while redirecting
  // return null;
}
