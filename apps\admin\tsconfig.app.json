{"extends": "../../tsconfig.base.json", "compilerOptions": {"baseUrl": ".", "outDir": "dist", "tsBuildInfoFile": "dist/tsconfig.app.tsbuildinfo", "jsx": "react-jsx", "lib": ["dom"], "types": ["node", "@nx/react/typings/cssmodule.d.ts", "@nx/react/typings/image.d.ts", "vite/client"], "rootDir": "src", "module": "esnext", "moduleResolution": "bundler", "paths": {"@minicardiac-client/shared": ["../../libs/shared/src/index.ts"], "@minicardiac-client/shared/*": ["../../libs/shared/src/*"]}}, "exclude": ["out-tsc", "dist", "src/**/*.spec.ts", "src/**/*.test.ts", "src/**/*.spec.tsx", "src/**/*.test.tsx", "src/**/*.spec.js", "src/**/*.test.js", "src/**/*.spec.jsx", "src/**/*.test.jsx", "jest.config.ts", "eslint.config.js", "eslint.config.cjs", "eslint.config.mjs"], "include": ["src/**/*.js", "src/**/*.jsx", "src/**/*.ts", "src/**/*.tsx"], "references": [{"path": "../../libs/shared/tsconfig.lib.json"}, {"path": "../../libs/types/tsconfig.lib.json"}, {"path": "../../libs/apis/tsconfig.lib.json"}, {"path": "../../libs/components/tsconfig.lib.json"}]}