import { test, expect } from '@playwright/test';

test.describe('Link Posts E2E', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/en/feed');
    await expect(page.locator('.MuiCircularProgress-root')).toHaveCount(0, {
      timeout: 15000,
    });
  });

  test('opens link post dialog', async ({ page }) => {
    await page.locator('[data-testid="link-post-icon"]').click();
    await expect(page.locator('text="New Link Post"')).toBeVisible();
    await expect(page.locator('input[type="url"]')).toBeVisible();
    await expect(page.locator('textarea')).toBeVisible();
  });

  test('creates a link post', async ({ page }) => {
    await page.locator('[data-testid="link-post-icon"]').click();
    await expect(page.locator('text="New Link Post"')).toBeVisible();

    await page.locator('input[type="url"]').fill('https://facebook.com');
    await expect(page.locator('[data-testid="link-preview"]')).toBeVisible({
      timeout: 5000,
    });

    await page.locator('textarea').fill('Test link post');
    const postBtn = page.locator('button:has-text("Post")');
    await expect(postBtn).toBeEnabled();
    await postBtn.click();

    await expect(page.locator('text="New Link Post"')).toHaveCount(0);
  });

  test('shows link post in feed', async ({ page }) => {
    const post = page.locator('[data-testid="link-post"]').first();
    await expect(post).toBeVisible({ timeout: 5000 });
    await expect(post.locator('[data-testid="link-preview"]')).toBeVisible();
  });

  test('opens link post details', async ({ page }) => {
    const post = page.locator('[data-testid="link-post"]').first();
    await expect(post).toBeVisible({ timeout: 5000 });
    await post.click();

    // Wait for navigation or modal
    await expect(
      page.locator('[role="dialog"], [data-testid="link-preview"]')
    ).toBeVisible({ timeout: 5000 });

    expect(page.url()).toContain('/feed/link/');
  });

  test('likes and comments on link post', async ({ page }) => {
    const post = page.locator('[data-testid="link-post"]').first();
    await expect(post).toBeVisible({ timeout: 5000 });

    const likeBtn = post.locator('button[aria-label*="like"]').first();
    if (await likeBtn.isVisible()) {
      await likeBtn.click();
      await expect(
        post.locator('button[aria-label*="liked"], [data-icon*="heart-fill"]')
      ).toBeVisible({ timeout: 3000 });
    }

    const commentBtn = post.locator('button[aria-label*="comment"]').first();
    if (await commentBtn.isVisible()) {
      await commentBtn.click();
      await expect(
        page.locator(
          '[data-testid="comments"], textarea[placeholder*="comment"]'
        )
      ).toBeVisible({ timeout: 3000 });
    }
  });

  test('validates link post form inputs', async ({ page }) => {
    await page.locator('[data-testid="link-post-icon"]').click();
    await expect(page.locator('text="New Link Post"')).toBeVisible();

    const postBtn = page.locator('button:has-text("Post")');
    await expect(postBtn).toBeDisabled();

    const urlInput = page.locator('input[type="url"]');
    await urlInput.fill('not-a-valid-url');
    await expect(postBtn).toBeDisabled();

    await urlInput.fill('https://example.com');
    await page.locator('textarea').fill('Valid caption');
    await expect(postBtn).toBeEnabled();
  });
});
