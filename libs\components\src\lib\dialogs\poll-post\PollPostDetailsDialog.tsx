'use client';

import { Box, TextField } from '@mui/material';
import CustomToggleButtonGroup from '../../buttons/CustomToggleButtonGroup';
import DurationDropdown from './DurationDropdown';
import { PollPostDetailsDialogProps } from './types';
import { useTranslations } from 'next-intl';
import { PostCreationNotification } from '../../common/PostCreationNotification';
import { PUBLIC_FORUM_TAG } from './constants';

const PollPostDetailsDialog = ({
  caption,
  setCaption,
  tags,
  setTags,
  audience,
  setAudience,
  speciality,
  setSpeciality,
  duration,
  setDuration,
  isPublicUser = false,
}: PollPostDetailsDialogProps) => {
  const t = useTranslations('pollPost');

  const handleTagsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!isPublicUser) {
      setTags(e.target.value);
    }
  };

  return (
    <Box display="flex" flexDirection="column" flex={1}>
      <Box mt={{ xs: '20px', sm: '40px' }} mb="40px">
        <TextField
          placeholder={t('captionPlaceholder')}
          value={caption}
          onChange={(e) => setCaption(e.target.value)}
          fullWidth
          multiline
          minRows={3}
          InputLabelProps={{ shrink: true }}
          label={t('caption')}
          sx={{
            '& .MuiOutlinedInput-root': {
              height: { xs: 181, sm: 136 },
              alignItems: 'start',
              '& textarea': {
                height: '100% !important',
                boxSizing: 'border-box',
                resize: 'none',
                overflowY: 'auto',
                '&::placeholder': {
                  textAlign: 'left',
                  verticalAlign: 'top',
                  lineHeight: 1.2,
                },
              },
            },
          }}
        />
      </Box>

      <Box
        display="flex"
        flexDirection={{ xs: 'column' }}
        gap={{ xs: '20px', lg: '20px' }}
        alignItems={{ xs: 'center', md: 'end' }}
        mb="0px"
      >
        {/* Tags and Duration row */}
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            gap: '20px',
            width: '100%',
          }}
        >
          {/* Tags field */}
          <Box width={{ xs: '100%', sm: '50%' }}>
            <TextField
              placeholder={isPublicUser ? PUBLIC_FORUM_TAG : t('tagsPlaceholder')}
              value={tags}
              onChange={handleTagsChange}
              fullWidth
              label={t('tags')}
              InputLabelProps={{ shrink: true }}
              disabled={isPublicUser}
              sx={isPublicUser ? {
                '& .MuiInputBase-input': {
                  color: '#666',
                  cursor: 'not-allowed',
                },
                '& .MuiOutlinedInput-root': {
                  backgroundColor: '#f5f5f5',
                  height: '45px',
                }
              } : {}}
            />
          </Box>

          {/* Duration dropdown - only show for public users here */}
          {isPublicUser && (
            <Box width={{ xs: '100%', sm: '50%' }}>
              <DurationDropdown duration={duration} setDuration={setDuration} />
            </Box>
          )}
        </Box>

        {/* Show notification banner below tags and duration for public users - hidden on mobile */}
        {isPublicUser && (
          <Box sx={{ width: '100%', display: { xs: 'none', sm: 'block' } }}>
            <PostCreationNotification
              show={true}
              onUpgradeClick={() => {
                // TODO: Implement navigation to the upgrade page or show upgrade modal.
                // For example, use router.push('/upgrade') or open an upgrade dialog.
                console.log('Navigate to upgrade page');
              }}
              width="100%"
              sx={{ height: '45px', padding: '8px 16px', display: 'flex', alignItems: 'center' }}
            />
          </Box>
        )}

        {/* Only show community and audience toggles for non-public users */}
        {!isPublicUser && (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              flexDirection: { xs: 'column', sm: 'row' },
              gap: '20px',
              width: '100%',
            }}
          >
            <Box width={{ xs: '100%', md: '304px' }}>
              <CustomToggleButtonGroup
                label={t('audience')}
                options={['PROFESSIONAL', 'PUBLIC', 'BOTH']}
                selected={audience}
                onChange={setAudience}
                width={{ xs: '100%', md: '304px' }}
              />
            </Box>
            <Box width={{ xs: '100%', md: '304px' }}>
              <CustomToggleButtonGroup
                label={t('community')}
                options={['CARDIAC_SURGEON', 'CARDIOLOGIST', 'BOTH']}
                selected={speciality}
                onChange={setSpeciality}
                width={{ xs: '100%', md: '304px' }}
              />
            </Box>
          </Box>
        )}
      </Box>

      {/* Duration for non-public users (moved outside the above box) */}
      {!isPublicUser && (
        <Box mt="40px">
          <DurationDropdown duration={duration} setDuration={setDuration} />
        </Box>
      )}
    </Box>
  );
};

export default PollPostDetailsDialog;