// components/ExpandableCard.tsx
'use client';

import { Box, Typography, IconButton, Collapse } from '@mui/material';
import { useState } from 'react';
import Iconify from '../../iconify/iconify';

const ExpandableCard = ({
  title,
  content,
}: {
  title: string;
  content: string;
}) => {
  const [expanded, setExpanded] = useState(false);

  return (
    <Box
      sx={{
        maxWidth: '936px',
        width: '100%',
        p: { xs: '16px', sm: '20px' },
        borderLeft: '2px solid',
        borderColor: expanded ? '#A24295' : 'transparent',
        backgroundColor: expanded ? '#F8F9FA' : 'white',
        transition: 'all 0.3s ease',
      }}
    >
      <Box
        display="flex"
        alignItems="center"
        justifyContent="space-between"
        onClick={() => setExpanded(!expanded)}
        sx={{ cursor: 'pointer' }}
      >
        <Typography
          fontSize={{ xs: '16px', smd: '20px' }}
          fontWeight={expanded ? 600 : 400}
          color={expanded ? '#A24295' : 'inherit'}
        >
          {title}
        </Typography>

        <IconButton
          sx={{
            transform: expanded ? 'rotate(180deg)' : 'rotate(0deg)',
            transition: 'transform 0.3s ease',
          }}
        >
          <Iconify
            icon={'formkit:down'}
            color={'#A24295'}
            width={24}
            height={24}
          />
        </IconButton>
      </Box>

      <Collapse in={expanded}>
        <Typography mt="20px" fontSize="16px" fontWeight={400} color="#333">
          {content}
        </Typography>
      </Collapse>
    </Box>
  );
};

export default ExpandableCard;
