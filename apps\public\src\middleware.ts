import createMiddleware from 'next-intl/middleware';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { routing } from './i18n/routing';

const intlMiddleware = createMiddleware(routing);

// Session verification function
async function verifySession(
  sessionCookie: string
): Promise<{ valid: boolean; customToken?: string; userInfo?: any }> {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_SERVER_URL}/api/v1/auth/verify-session`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Cookie: `__session=${sessionCookie}`,
        },
        credentials: 'include',
      }
    );

    if (response.ok) {
      const data = await response.json();
      return {
        valid: true,
        customToken: data.data?.customToken,
        userInfo: data.data?.user,
      };
    }

    return { valid: false };
  } catch (error) {
    console.error('Session verification failed:', error);
    return { valid: false };
  }
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const environment =
    process.env.NEXT_PUBLIC_ENVIRONMENT || process.env.NODE_ENV;

  // Exclude internal paths and assets
  if (
    pathname.startsWith('/_next') ||
    pathname === '/favicon.ico' ||
    pathname.startsWith('/api/') ||
    pathname.includes('.')
  ) {
    return NextResponse.next();
  }

  const firstSegment = pathname.split('/')[1];
  const isPotentialLocale = /^[a-z]{2}$/.test(firstSegment);
  const isInvalidLocale =
    isPotentialLocale &&
    !routing.locales.includes(firstSegment as (typeof routing.locales)[number]);

  if (isInvalidLocale) {
    const url = request.nextUrl.clone();
    url.pathname = '/404';
    return NextResponse.rewrite(url);
  }

  const isLocalized = routing.locales.includes(
    firstSegment as (typeof routing.locales)[number]
  );
  let response: NextResponse;

  if (isLocalized) {
    response = intlMiddleware(request);
  } else {
    const url = request.nextUrl.clone();
    url.pathname = `/${routing.defaultLocale}${pathname}`;
    return NextResponse.redirect(url);
  }

  // Session handling logic
  const sessionCookie = request.cookies.get('__session')?.value;

  // Add session info to response headers (for your existing components to use)
  if (sessionCookie) {
    try {
      const sessionResult = await verifySession(sessionCookie);

      if (sessionResult.valid) {
        response.headers.set('X-User-Authenticated', 'true');
        response.headers.set('X-Session-Valid', 'true');

        if (sessionResult.customToken) {
          response.headers.set('X-Custom-Token', sessionResult.customToken);
        }

        if (sessionResult.userInfo) {
          response.headers.set(
            'X-User-Info',
            JSON.stringify(sessionResult.userInfo)
          );
        }
      } else {
        response.headers.set('X-Session-Valid', 'false');
        response.headers.set('X-User-Authenticated', 'false');
      }
    } catch (error) {
      console.error('Session verification in middleware failed:', error);
      response.headers.set('X-Session-Valid', 'false');
      response.headers.set('X-User-Authenticated', 'false');
    }
  } else {
    response.headers.set('X-Session-Valid', 'false');
    response.headers.set('X-User-Authenticated', 'false');
  }

  if (environment !== 'production') {
    response.headers.set('X-Robots-Tag', 'noindex, nofollow');
  }

  return response;
}

export const config = {
  matcher: ['/((?!_next|favicon.ico|api/|.*\\..*).*)'],
};
