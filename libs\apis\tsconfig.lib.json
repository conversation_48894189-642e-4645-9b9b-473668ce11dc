{"extends": "../../tsconfig.base.json", "compilerOptions": {"baseUrl": ".", "rootDir": "src", "outDir": "dist", "tsBuildInfoFile": "dist/tsconfig.lib.tsbuildinfo", "emitDeclarationOnly": true, "forceConsistentCasingInFileNames": true, "types": ["node", "vite/client"], "jsx": "react-jsx"}, "include": ["src/**/*.ts", "src/**/*.tsx"], "references": [{"path": "../types/tsconfig.lib.json"}], "exclude": ["jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts"]}