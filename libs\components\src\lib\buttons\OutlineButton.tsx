import { useTheme } from '@emotion/react';
import { Button, SxProps, useMediaQuery } from '@mui/material';

export const OutlinedButton = ({
  label,
  onClick,
  sx,
}: {
  label: string;
  onClick?: () => void;
  sx?: SxProps;
}) => {
  const theme: any = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <Button
      variant="outlined"
      fullWidth={isMobile}
      onClick={onClick}
      sx={{
        fontSize: { xs: 14, lg: 16 },
        height: isMobile ? 36 : 40,
        borderColor: { xs: '#A24295', lg: 'white' },
        color: { xs: '#A24295', lg: 'white' },
        textWrap: isMobile ? 'nowrap' : 'initial',
        ...sx,
      }}
    >
      {label}
    </Button>
  );
};
