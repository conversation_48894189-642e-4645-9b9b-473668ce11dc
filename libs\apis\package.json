{"name": "@minicardiac-client/apis", "version": "0.0.1", "private": true, "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"development": "./src/index.ts", "types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.js"}, "./lib/auth/auth-store": {"types": "./dist/lib/auth/auth-store.d.ts", "import": "./dist/lib/auth/auth-store.js", "default": "./dist/lib/auth/auth-store.js"}, "./lib/posts": {"types": "./dist/lib/posts/index.d.ts", "import": "./dist/lib/posts/index.js", "default": "./dist/lib/posts/index.js"}, "./lib/posts/use-create-media-post": {"types": "./dist/lib/posts/use-create-media-post.d.ts", "import": "./dist/lib/posts/use-create-media-post.js", "default": "./dist/lib/posts/use-create-media-post.js"}}, "dependencies": {"@tanstack/react-query": "^5.0.0", "@minicardiac-client/types": "workspace:*", "axios": "^1.6.0", "firebase": "^11.6.0", "jwt-decode": "^4.0.0", "react": "18.2.0", "react-toastify": "11.0.5", "swr": "^2.3.3", "zustand": "^5.0.4"}}