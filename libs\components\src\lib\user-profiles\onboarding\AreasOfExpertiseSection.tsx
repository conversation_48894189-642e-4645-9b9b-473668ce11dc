import { Box, Typography } from '@mui/material';
import CustomForm from './CustomForm';

export const AreasOfExpertiseSection = () => {
  const formData = [
    {
      subtitles:
        'Detail your areas of expertise. These will be displayed on your page as a bulleted list with expandable descriptions for those who wish to know more details.',
      rows: [
        {
          columns: 1,
          fields: [
            {
              label: 'Title',
              placeholder: 'Your area of expertise',
              sx: { maxWidth: '840px', width: '100%' },
            },
          ],
        },
        {
          columns: 1,
          fields: [
            {
              label: 'Description (optional)',
              placeholder: 'A short description of your expertise',
              sx: { maxWidth: '840px', width: '100%' },
              optional: true,
            },
          ],
        },
      ],
      addButtonLabel: 'Area of Expertise',
    },
  ];

  return (
    <Box
      maxWidth="976px"
      width="100%"
      mx="auto"
      p={{ xs: '16px', sm: '20px' }}
      borderRadius="8px"
      bgcolor="#fff"
    >
      <Typography fontSize="24px" fontWeight={600} mb="20px">
        Areas of Expertise
      </Typography>

      {formData.map((form, idx) => (
        <CustomForm
          key={idx}
          subtitles={form.subtitles}
          rows={form.rows}
          addButtonLabel={`Add ${form.addButtonLabel}`}
        />
      ))}
    </Box>
  );
};
