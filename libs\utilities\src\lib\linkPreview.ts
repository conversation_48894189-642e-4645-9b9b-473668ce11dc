export interface LinkPreview {
  title: string;
  description: string;
  image: string;
  domain: string;
  url: string;
}

export const enhanceLinkPreview = async (url: string): Promise<LinkPreview | null> => {
  if (!url.trim()) return null;
  
  try {
    const response = await fetch('/api/link-preview', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ url: url.trim() }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to fetch link preview');
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Link preview error:', error);
    
    // Return fallback preview on error
    const domain = url.replace(/^https?:\/\//, '').replace(/^www\./, '').split('/')[0];
    return {
      title: `Website - ${domain}`,
      description: 'Preview of the shared link content. Click to visit the website.',
      image: 'https://images.unsplash.com/photo-1499951360447-b19be8fe80f5?w=400&h=300&fit=crop',
      domain: domain,
      url: url,
    };
  }
};
