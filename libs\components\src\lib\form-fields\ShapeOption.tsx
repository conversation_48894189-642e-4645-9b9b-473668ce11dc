import React from 'react';
import { Box, Typography } from '@mui/material';

interface ShapeOptionProps {
  label: string;
  value: string;
  checked?: boolean;
  onChange?: (value: string) => void;
}

const CustomRadioButton: React.FC<ShapeOptionProps> = ({
  label,
  value,
  checked,
  onChange,
}) => {
  return (
    <Box
      component="label"
      display="flex"
      alignItems="center"
      gap="8px"
      sx={{ cursor: 'pointer' }}
    >
      {/* Hidden radio input */}
      <input
        type="radio"
        name="shape"
        value={value}
        checked={checked}
        onChange={() => onChange?.(value)}
        style={{ display: 'none' }}
      />

      {/* Visual Circle */}
      <Box
        width="16px"
        height="16px"
        borderRadius="50%"
        border={checked ? '5px solid #A24295' : '2px solid #A3A3A3'}
      />

      <Typography fontSize="16px" fontWeight={400} noWrap>
        {label}
      </Typography>
    </Box>
  );
};

export default CustomRadioButton;
