import React, { useRef, useState } from 'react';
import { Box, Typography, Button } from '@mui/material';
import MediaFileCard from './MediaFileCard';
import { AddMoreMediaButton } from './AddMoreMediaButton';

const MediaPostGallerySelector = ({
  files,
  setFiles,
  onFilesChange,
}: {
  files: File[];
  setFiles: (files: File[]) => void;
  onFilesChange: (files: File[]) => void;
}) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const handleAddMediaClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files ? Array.from(e.target.files) : [];
    const newFiles = [...files, ...selectedFiles];
    setFiles(newFiles);
    onFilesChange(newFiles);
    setActiveIndex(files.length);
  };

  const handleRemove = (index: number) => {
    const updatedFiles = files.filter((_, i) => i !== index);
    setFiles(updatedFiles);
    onFilesChange(updatedFiles);
    setActiveIndex((prev) => (index <= prev ? Math.max(0, prev - 1) : prev));
  };

  return (
    <Box width="100%" mt="20px">
      {/* Hidden File Input */}
      <input
        type="file"
        accept="image/jpeg,image/gif,image/jpg,image/png,image/webp,video/mp4,video/avi,video/webm,video/x-ms-wmv,video/x-flv,video/mpeg,video/quicktime,video/x-m4v,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation,text/plain"
        multiple
        ref={fileInputRef}
        style={{ display: 'none' }}
        onChange={handleFileChange}
      />

      {/* Main Preview Area */}
      {files.length === 0 && (
        <Box
          width="100%"
          height="361px"
          borderRadius="8px"
          position="relative"
          overflow="hidden"
          display="flex"
          justifyContent="center"
          alignItems="center"
          sx={{
            backgroundColor: files.length === 0 ? '#F5F5F5' : 'transparent',
          }}
        >
          {files.length === 0 ? (
            <Box
              display="flex"
              flexDirection="column"
              alignItems="center"
              justifyContent="center"
              gap={2}
            >
              <Typography fontSize="16px" color="#999">
                No media selected
              </Typography>
              <Button
                variant="contained"
                onClick={handleAddMediaClick}
                sx={{
                  backgroundColor: '#A24295',
                  '&:hover': { backgroundColor: '#8A2C80' },
                  textTransform: 'none',
                  fontWeight: 600,
                }}
              >
                Add Media
              </Button>
            </Box>
          ) : (
            <>
              <Box
                component="img"
                src={URL.createObjectURL(files[activeIndex])}
                alt={`Preview ${activeIndex + 1}`}
                sx={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover',
                }}
              />

              {/* Add Media Button */}
              <Box position="absolute" bottom="16px" right="16px">
                <Button
                  onClick={handleAddMediaClick}
                  variant="contained"
                  sx={{
                    backgroundColor: '#A24295',
                    '&:hover': { backgroundColor: '#8A2C80' },
                    textTransform: 'none',
                    fontWeight: 600,
                  }}
                >
                  Add Media
                </Button>
              </Box>
            </>
          )}
        </Box>
      )}

      {/* Scrollable Thumbnails */}
      {files.length > 0 && (
        <>
          <Box
            mt="16px"
            overflow="auto"
            width="100%"
            sx={{
              '&::-webkit-scrollbar': { display: 'none' },
              scrollbarWidth: 'none',
              msOverflowStyle: 'none',
            }}
          >
            <Box display="flex" gap="12px" width="max-content" margin="0 auto">
              {files.map((file, index) => (
                <Box
                  key={index}
                  onClick={() => setActiveIndex(index)}
                  sx={{
                    flexShrink: 0,
                    cursor: 'pointer',
                    borderRadius: '8px',
                  }}
                >
                  <MediaFileCard
                    file={file}
                    index={index}
                    // open={() => {}}
                    onRemove={handleRemove}
                  />
                </Box>
              ))}
            </Box>
          </Box>

          {/* Image Counter */}
          <Typography
            textAlign="center"
            fontSize="14px"
            mt="8px"
            color="#737678"
            fontWeight={500}
          >
            {activeIndex + 1} / {files.length}
          </Typography>
          <Box
            sx={{
              mt: '10px',
              mb: '140px',
            }}
          >
            <AddMoreMediaButton
              open={handleAddMediaClick}
              sx={{
                position: 'static',
              }}
            />
          </Box>
        </>
      )}
    </Box>
  );
};

export default MediaPostGallerySelector;
