'use client';

import { Box, SxProps, Typography } from '@mui/material';
import { Iconify } from '../iconify';

interface Props {
  onClick?: () => void;
  label?: string;
  className: string;
  sx?: SxProps;
  width?: number;
  height?: number;
}

export default function EditButton({
  onClick,
  label,
  className,
  sx,
  width = 24,
  height = 24,
}: Props) {
  return (
    <Box
      className={className}
      sx={{
        position: 'absolute',
        top: '20px',
        right: '20px',
        display: 'none',
        alignItems: 'center',
        gap: '8px',
        px: 2,
        py: 1,
        borderRadius: '8px',
        backgroundColor: '#1E1E1E66',
        color: 'white',
        fontSize: 14,
        fontWeight: 500,
        cursor: 'pointer',
        pointerEvents: 'none',
        transition: 'opacity 0.3s ease',
        ...sx,
      }}
      onClick={onClick}
    >
      <Iconify icon="fluent:edit-20-filled" width={width} height={height} />
      <Typography
        fontSize={14}
        fontWeight={600}
        display={{ xs: 'none', smd: 'block' }}
      >
        {label}
      </Typography>
    </Box>
  );
}
