import { useState, useRef, useEffect, useMemo } from 'react';
import type { FeedPostType } from '@minicardiac-client/types';
import {
  Box,
  Typography,
  IconButton,
  Card,
  CardContent,
  CardMedia,
  TextField,
  Button,
} from '@mui/material';
import { Iconify } from '../iconify';
import { getCdnUrl } from '@minicardiac-client/utilities';

// Make all FeedPostType fields optional since we don't need all of them for scheduled posts
interface ScheduledPostCardProps {
  post: Partial<FeedPostType> & {
    id: string;
    title: string;
    content: string;
    scheduledDate: string;
    postType: string;
    media?: string;
    mediaType?: string;
    link?: string;
    linkUrl?: string;
    postMedias?: Array<{
      mediaPath: string;
      mediaType: string;
      altText?: string;
    }>;
    coverImagePath?: string;
    question?: string;
    options?: Array<{ text: string } | string>;
  };
  onEdit: (id: string, newContent: string) => Promise<void>;
  onDelete: (id: string) => void;
  onReschedule: (id: string) => void;
  isEditing?: boolean;
  onStartEdit?: (id: string) => void;
  onCancelEdit?: () => void;
}

export function ScheduledPostCard({
  post,
  onEdit,
  onDelete,
  onReschedule,
  isEditing = false,
  onStartEdit,
  onCancelEdit,
}: ScheduledPostCardProps) {
  const [editContent, setEditContent] = useState(post.content || '');
  const [isSaving, setIsSaving] = useState(false);
  const textFieldRef = useRef<HTMLTextAreaElement>(null);

  // Truncate content if too long
  const truncatedContent = useMemo(() => {
    const maxLength = 200; // Maximum characters to show before truncation
    if (!post.content) return '';
    return post.content.length > maxLength
      ? `${post.content.substring(0, maxLength)}...`
      : post.content;
  }, [post.content]);

  useEffect(() => {
    if (isEditing && textFieldRef.current) {
      const textarea = textFieldRef.current;
      textarea.focus();
      // Position cursor at end
      const length = editContent.length;
      textarea.setSelectionRange(length, length);
    }
  }, [isEditing, editContent]);

  const handleSave = async () => {
    if (!editContent.trim() || editContent === post.content) {
      onCancelEdit?.();
      return;
    }

    try {
      setIsSaving(true);
      await onEdit(post.id, editContent);
    } catch (error) {
      console.error('Error updating post:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      setEditContent(post.content || '');
      onCancelEdit?.();
    }
  };

  const handleBlur = () => {
    // Only save if content actually changed
    if (editContent !== post.content) {
      handleSave();
    } else {
      onCancelEdit?.();
    }
  };

  const renderContentPreview = () => {
    const contentElement = isEditing ? (
      <TextField
        inputRef={textFieldRef}
        value={editContent}
        onChange={(e) => setEditContent(e.target.value)}
        onKeyDown={handleKeyDown}
        onBlur={handleBlur}
        multiline
        minRows={1}
        maxRows={4}
        variant="standard"
        fullWidth
        disabled={isSaving}
        sx={{
          '& .MuiInput-underline:before': { display: 'none' },
          '& .MuiInput-underline:after': { display: 'none' },
          '& .MuiInputBase-root': {
            overflow: 'hidden',
            '& textarea': {
              overflow: 'hidden !important',
              resize: 'none',
              padding: 0,
              margin: 0,
              lineHeight: '1.4',
            },
          },
        }}
      />
    ) : (
      <Box
        onClick={() => onStartEdit?.(post.id)}
        sx={{
          cursor: 'text',
          '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.02)' },
          padding: '2px 4px',
          borderRadius: '4px',
          transition: 'background-color 0.2s',
          display: '-webkit-box',
          WebkitLineClamp: 3,
          WebkitBoxOrient: 'vertical',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          '& *': {
            margin: 0,
            lineHeight: 1.4,
          }
        }}
      >
        {truncatedContent || 'No content'}
      </Box>
    );

    switch (post.postType) {
      case 'media':
        return (
          <Box display="flex" gap={2} alignItems="flex-start">
            {post.postMedias?.[0] && (
              <CardMedia
                component="img"
                sx={{
                  width: 64,
                  height: 64,
                  borderRadius: 1,
                  objectFit: 'cover',
                }}
                image={post.postMedias[0].mediaPath}
              />
            )}
            <Box sx={{ flex: 1 }}>{contentElement}</Box>
          </Box>
        );
      case 'article':
        return (
          <>
            {post.coverImagePath && (
              <CardMedia
                component="img"
                sx={{
                  width: 64,
                  height: 64,
                  borderRadius: 1,
                  objectFit: 'cover',
                  mb: 1,
                }}
                image={getCdnUrl(post.coverImagePath)}
              />
            )}
            <Typography variant="body2" fontWeight={600}>
              {post.title}
            </Typography>
            {contentElement}
          </>
        );
      case 'link':
        return (
          <Box>
            {contentElement}
            {post.link && (
              <Typography variant="caption" color="primary" noWrap>
                {post.link}
              </Typography>
            )}
          </Box>
        );
      case 'poll':
        return (
          <>
            <Typography variant="body2" fontWeight={600}>
              {post.question || post.content}
            </Typography>
            <Typography variant="caption">
              Options:{' '}
              {post.options?.map((o: any, i: number) => (
                <span key={i}>
                  <strong>{i + 1}.</strong> {o.text || o}{' '}
                </span>
              )) || 'No options'}
            </Typography>
          </>
        );
      case 'question':
        return (
          <Typography variant="body2" fontWeight={600}>
            {post.question || post.content}
          </Typography>
        );
      default:
        return contentElement;
    }
  };

  return (
    <Card variant="outlined" sx={{ borderRadius: 1, my: 2, border: 'none' }}>
      <CardContent>
        <Box display="flex" alignItems="center">
          {/* Content Section */}
          <Box width="75%" pr={2} overflow="hidden">
            <Typography
              variant="caption"
              color="text.secondary"
              textTransform="capitalize"
              sx={{ fontWeight: 500 }}
            >
              {post.postType}
            </Typography>
            <Box sx={{ overflow: 'hidden', textOverflow: 'ellipsis' }}>
              {renderContentPreview()}
            </Box>

            {/* Scheduled time and reschedule */}
            <Box mt={1} display="flex" alignItems="center" gap={1}>
              <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
                {(() => {
                  const dateObj = new Date(post.postScheduleDate as string);
                  const formattedDate = dateObj.toLocaleDateString(undefined, {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                  });
                  const formattedTime = dateObj.toLocaleTimeString(undefined, {
                    hour: '2-digit',
                    minute: '2-digit',
                  });
                  return `Post will go live on ${formattedDate} at ${formattedTime}`;
                })()}
              </Typography>
              <Button
                variant="text"
                size="small"
                onClick={() => onReschedule(post.id)}
                sx={{
                  textTransform: 'none',
                  minWidth: 0,
                  p: 0,
                  color: 'primary.main',
                  fontSize: '0.75rem',
                  '&:hover': {
                    textDecoration: 'underline',
                    backgroundColor: 'transparent',
                  }
                }}
              >
                Reschedule
              </Button>
            </Box>
          </Box>

          {/* Actions Section */}
          <Box
            width="25%"
            display="flex"
            justifyContent="flex-end"
            alignItems="flex-start"
            gap={1}
          >
            <IconButton
              size="small"
              onClick={() => onStartEdit?.(post.id)}
              disabled={isEditing}
            >
              <Iconify
                icon="solar:pen-bold"
                width={20}
                height={20}
                color="#A24295"
              />
            </IconButton>
            <IconButton size="small" onClick={() => onDelete(post.id)}>
              <Iconify
                icon="solar:trash-bin-minimalistic-bold"
                width={20}
                height={20}
                color="#A24295"
              />
            </IconButton>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
}
