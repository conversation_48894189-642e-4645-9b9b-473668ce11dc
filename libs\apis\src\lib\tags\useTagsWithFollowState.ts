import { useState, useMemo } from 'react';
import { formatTags } from './formatTags.js';
import {
  useFollowingTagsWithPosts,
  useFollowTag,
  useSuggestedTagsWithPosts,
  useUnfollowTag,
} from './useTags.js';
import { TagWithPosts } from '@minicardiac-client/types';

// ...other imports

export function useTagsWithFollowState({
  fetchSuggested = false,
  suggestedLimit = 20,
  followingLimit = 20,
} = {}) {
  // Fetch following tags
  const { data: followingData = [], isLoading: isFollowingLoading } =
    useFollowingTagsWithPosts({
      limit: followingLimit,
    }) as { data: TagWithPosts[]; isLoading: boolean };

  // Fetch suggested tags (optional)
  const { data: suggestedData = [], isLoading: isSuggestedLoading } =
    useSuggestedTagsWithPosts({
      limit: suggestedLimit,
      enabled: fetchSuggested,
    }) as { data: TagWithPosts[]; isLoading: boolean };

  // Compute initial followingState from followingData and suggestedData
  const initialFollowingState = useMemo(() => {
    if (!fetchSuggested) return {};

    const followedTagIds = new Set(followingData.map((t) => t.tagId));

    return suggestedData.reduce<Record<string, boolean>>((acc, tag) => {
      acc[tag.tagId] = followedTagIds.has(tag.tagId);
      return acc;
    }, {});
  }, [followingData, suggestedData, fetchSuggested]);

  // Local state for optimistic UI toggling of follow/unfollow
  const [followingState, setFollowingState] = useState<Record<string, boolean>>(
    initialFollowingState
  );

  // Keep local state in sync if the data changes externally
  // (optional) — can add a simple effect or reset logic if needed
  // For example:
  // useEffect(() => {
  //   setFollowingState(initialFollowingState);
  // }, [initialFollowingState]);

  // Mutations
  const followMutation = useFollowTag();
  const unfollowMutation = useUnfollowTag();

  const handleToggleFollow = (tagId: string, currentlyFollowing: boolean) => {
    setFollowingState((prev) => ({ ...prev, [tagId]: !currentlyFollowing }));

    const mutation = currentlyFollowing
      ? unfollowMutation.mutate
      : followMutation.mutate;

    mutation(tagId, {
      onError: (err) => {
        console.error('Error toggling follow:', err);
        setFollowingState((prev) => ({
          ...prev,
          [tagId]: currentlyFollowing,
        }));
      },
    });
  };

  return {
    isLoading: isFollowingLoading || (fetchSuggested && isSuggestedLoading),
    followingTags: formatTags(
      (followingData || []).map((t) => ({
        tagId: t.tagId,
        tagName: t.tagName,
        posts: t.posts,
      }))
    ),
    suggestedTags: fetchSuggested
      ? formatTags(
          (suggestedData || []).map((t) => ({
            tagId: t.tagId,
            tagName: t.tagName,
            posts: t.posts,
          }))
        )
      : [],
    followingState,
    handleToggleFollow,
  };
}
