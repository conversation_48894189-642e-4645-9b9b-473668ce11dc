import { Box, Button, Stack, Typography } from '@mui/material';
import { User } from '@minicardiac-client/types';
import { updateUserStatus } from '@minicardiac-client/apis';

interface ProfileCardDetailsProps {
  user: User;
}

export default function ProfileCardDetails({ user }: ProfileCardDetailsProps) {
  const handleStatusChange = async (
    status: 'accepted' | 'rejected' | 'request-info'
  ) => {
    if (!user.id) return;

    try {
      await updateUserStatus(user.id, status);
    } catch (err) {
      console.log(err);
    }
  };

  return (
    <Box
      display="flex"
      flexDirection="column"
      justifyContent={'space-between'}
      flex={1}
    >
      <Box>
        <Typography variant="subtitle1" fontWeight="bold">
          {user.name}
        </Typography>
        <Box
          sx={{
            flexGrow: 1,
            overflowY: 'auto',
            mt: 1,
            mb: 2,
            pr: 1,
            maxWidth: {
              md: '500px',
              sm: '100%',
            },
          }}
        >
          <Typography variant="body2" color="text.secondary">
            {user.about || 'No description provided.'}
          </Typography>
        </Box>
      </Box>
      <Stack direction="row" spacing={1}>
        <Button
          variant="contained"
          sx={{ backgroundColor: '#5CB85C', textTransform: 'none' }}
          size="small"
          disableElevation
          onClick={() => handleStatusChange('accepted')}
        >
          Approve
        </Button>
        <Button
          variant="contained"
          sx={{ backgroundColor: '#DC3545', textTransform: 'none' }}
          size="small"
          disableElevation
          onClick={() => handleStatusChange('rejected')}
        >
          Reject
        </Button>
        <Button
          variant="contained"
          sx={{ backgroundColor: '#A3A3A3', textTransform: 'none' }}
          size="small"
          disableElevation
          onClick={() => handleStatusChange('request-info')}
        >
          Request Info
        </Button>
      </Stack>
    </Box>
  );
}
