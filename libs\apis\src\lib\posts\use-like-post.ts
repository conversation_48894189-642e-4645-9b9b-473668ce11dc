import { useMutation, useQueryClient } from '@tanstack/react-query';
import { postApi } from './post-api.js';
import type { FeedPost } from './types.js';

interface FeedResponse {
  data: FeedPost[];
  // Add other properties that might be in the response
}

export function useLikePost() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ postId, like }: { postId: string; like: boolean }) =>
      like ? postApi.likePost(postId) : postApi.unlikePost(postId),
    onMutate: async ({ postId, like }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['feed'] });

      // Snapshot the previous value
      const previousFeed = queryClient.getQueryData(['feed']);

      // Optimistically update the cache
      queryClient.setQueryData(['feed'], (old: FeedResponse | undefined) => ({
        ...(old || {}),
        data: (old?.data || []).map((post: FeedPost) =>
          post.id === postId
            ? {
                ...post,
                isLiked: like,
                likesCount: like
                  ? (post.likesCount || 0) + 1
                  : Math.max(0, (post.likesCount || 1) - 1),
              }
            : post
        ),
      }));

      // Return a context object with the snapshotted value
      return { previousFeed };
    },
    onError: (err, variables, context) => {
      // Rollback on error
      if (context?.previousFeed) {
        queryClient.setQueryData(['feed'], context.previousFeed);
      }
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ['feed'], type: 'all' });
    },
  });
}
