import { useMutation, useQuery } from '@tanstack/react-query';
import { postApi, CreateCommentRequest } from './post-api.js';
import { toast } from 'react-toastify';
import { Comment } from '@minicardiac-client/types';

interface UseCommentsOptions {
  enabled?: boolean;
  parentCommentId?: string;
}

export function useComments(postId: string, options?: UseCommentsOptions) {
  const { enabled = true } = options || {};

  const {
    data: comments = [],
    isLoading,
    error,
    refetch,
  } = useQuery<Comment[]>({
    queryKey: ['comments', postId],
    queryFn: async () => {
      const answerComments = await postApi.getComments(postId);

      return answerComments.map(
        ({ id, comment, creator, createdAt, repliesCount }): Comment => ({
          id,
          user: {
            id: creator.id,
            name: creator.displayName || 'Unknown User',
            profilePic: creator.profileImageUrlThumbnail || '',
          },
          postedAgo: createdAt,
          comment,
          replies: [],
          repliesCount,
          createdAt,
        })
      );
    },
    enabled: enabled && !!postId,
  });

  // Create a new comment
  const { mutateAsync: createComment, isPending: isCreating } = useMutation({
    mutationFn: async (data: CreateCommentRequest) => {
      const response = await postApi.createComment(postId, data);
      return response.data;
    },
    onSuccess: () => {
      toast.success('Comment posted successfully!');
      refetch(); // refresh comments
    },
    onError: (error: unknown) => {
      const errorMessage =
        error &&
        typeof error === 'object' &&
        'response' in error &&
        error.response &&
        typeof error.response === 'object' &&
        'data' in error.response &&
        error.response.data &&
        typeof error.response.data === 'object' &&
        'message' in error.response.data &&
        typeof error.response.data.message === 'string'
          ? error.response.data.message
          : 'Failed to post comment';
      toast.error(errorMessage);
      if (error instanceof Error) throw error;
      throw new Error(errorMessage);
    },
  });

  // Delete a comment
  const { mutateAsync: deleteComment } = useMutation({
    mutationFn: async (commentId: string) => {
      await postApi.deleteComment(commentId);
    },
    onSuccess: () => {
      toast.success('Comment deleted');
      refetch();
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to delete comment');
    },
  });

  // Pin a comment
  const { mutateAsync: pinComment, isPending: isPinning } = useMutation({
    mutationFn: async (commentId: string) => {
      const res = await postApi.pinComment(commentId);
      return res.data;
    },
    onSuccess: () => {
      toast.success('Comment pinned successfully!');
      refetch(); // refresh comments
    },
    onError: (error: unknown) => {
      const errorMessage =
        error &&
        typeof error === 'object' &&
        'response' in error &&
        error.response &&
        typeof error.response === 'object' &&
        'data' in error.response &&
        error.response.data &&
        typeof error.response.data === 'object' &&
        'message' in error.response.data &&
        typeof error.response.data.message === 'string'
          ? error.response.data.message
          : 'Failed to pin comment';
      toast.error(errorMessage);
    },
  });

  // Unpin a comment
  const { mutateAsync: unpinComment, isPending: isUnpinning } = useMutation({
    mutationFn: async (postId: string) => {
      const res = await postApi.unpinComment(postId);
      return res.data;
    },
    onSuccess: () => {
      toast.success('Comment unpinned successfully!');
      refetch();
    },
    onError: (error: unknown) => {
      const errorMessage =
        error &&
        typeof error === 'object' &&
        'response' in error &&
        error.response &&
        typeof error.response === 'object' &&
        'data' in error.response &&
        error.response.data &&
        typeof error.response.data === 'object' &&
        'message' in error.response.data &&
        typeof error.response.data.message === 'string'
          ? error.response.data.message
          : 'Failed to unpin comment';
      toast.error(errorMessage);
    },
  });

  return {
    comments,
    isLoading,
    error,
    createComment,
    isCreating,
    deleteComment,
    pinComment,
    isPinning,
    unpinComment,
    isUnpinning,
    refetchComments: refetch,
  };
}
