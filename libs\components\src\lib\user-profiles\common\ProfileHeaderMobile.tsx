'use client';

import { useState } from 'react';
import { Box, Typography, Chip, Menu, MenuItem } from '@mui/material';
import ProfileIntro from './ProfileIntro';
import { BackButton } from '../../buttons/Backbutton';
import { useRouter } from 'next/navigation';
import { Profile } from '@minicardiac-client/types';
import EditButton from '../../buttons/EditButton';
import { usePostDialogStore } from '@minicardiac-client/apis';

interface Props {
  profile: Profile;
  ConnectUserComponent?: (props: { profile: Profile }) => JSX.Element;
  ProfileConnectionsComponent?: (props: { profile: Profile }) => JSX.Element;
}

export default function ProfileHeaderMobile({
  profile,
  ConnectUserComponent,
  ProfileConnectionsComponent,
}: Props) {
  const router = useRouter();
  const { setActiveDialog } = usePostDialogStore();

  // Menu state
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  return (
    <Box sx={{ width: '100%', overflow: 'hidden', bgcolor: '#fff' }}>
      {/* Background Image */}
      <Box
        sx={{
          position: 'relative',
          height: 240,
          backgroundImage: `url(${profile.backgroundImage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          background: profile.backgroundImage ? 'transparent' : '#ffffff',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        {/* Dark overlay */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            bottom: 0,
            left: 0,
            right: 0,
            background: `linear-gradient(to ${
              !profile.backgroundImage ? 'top' : 'bottom'
            }, rgba(255, 255, 255, 0), rgba(136, 136, 136, 0.25),rgba(30, 30, 30, 1))`,
          }}
        />

        {!profile.backgroundImage && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background:
                'linear-gradient(to bottom right, rgba(249, 34, 67, 0.3), rgba(162, 66, 149, 0.3))',
            }}
          />
        )}

        {!profile.backgroundImage && (
          <Box sx={{ display: 'flex', gap: 2, position: 'absolute' }}>
            <Box
              component="img"
              src="/assets/user-profile/lifeline.svg"
              alt="Heart"
              sx={{
                width: {
                  xs: '172px',
                  smd: '100%',
                },
                height: 'auto',
              }}
            />
            <Box
              component="img"
              src="/assets/user-profile/heart.svg"
              alt="Heart"
              sx={{
                width: {
                  xs: '135px',
                  smd: '100%',
                },
                height: 'auto',
              }}
            />
          </Box>
        )}

        {/* Top buttons */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            p: 1.5,
          }}
        >
          <BackButton sx={{ color: 'white' }} onClick={() => router.back()} />

          <Box
            onClick={handleMenuOpen}
            sx={{
              cursor: 'pointer',
            }}
          >
            {/* Edit button with menu */}
            <EditButton
              className="edit-button"
              sx={{
                color: 'white',
                opacity: 1,
                backgroundColor: 'transparent',
                position: 'static',
              }}
            />
          </Box>

          <Menu
            anchorEl={anchorEl}
            open={open}
            onClose={handleMenuClose}
            anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
            transformOrigin={{ vertical: 'top', horizontal: 'right' }}
          >
            <MenuItem onClick={handleMenuClose}>Edit cover details</MenuItem>
            <MenuItem
              onClick={() => {
                setActiveDialog('EditUserProfile');
              }}
            >
              Edit page
            </MenuItem>
          </Menu>
        </Box>

        {/* Profile connections */}
        {ProfileConnectionsComponent ? (
          <ProfileConnectionsComponent profile={profile} />
        ) : (
          <Box sx={{ p: 2, color: 'gray' }}>Connections info not available</Box>
        )}
      </Box>

      {/* Info Section */}
      <Box
        sx={{
          mt: '20px',
          px: 2,
          pt: 7,
          pb: 2,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'flex-start',
          gap: '20px',
        }}
      >
        <Box>
          <Typography fontSize={20} fontWeight={700}>
            {profile.name}
          </Typography>

          {profile.degrees && (
            <Typography fontSize={16} fontWeight={400}>
              {profile.degrees}
            </Typography>
          )}
        </Box>

        <Box>
          {profile.designation && (
            <Typography fontSize={16} fontWeight={500}>
              {profile.designation}
            </Typography>
          )}

          <Chip
            label={profile.workplace || profile.college}
            sx={{
              fontSize: 12,
              fontWeight: 600,
              backgroundColor: '#F3F4F6',
              color: '#A24295',
              borderRadius: '20px',
              px: 1,
              minHeight: '28px',
              maxWidth: '100%',
            }}
          />
        </Box>

        {ConnectUserComponent ? (
          <ConnectUserComponent profile={profile} />
        ) : (
          <Box sx={{ p: 2, color: 'gray' }}>Connect option not available</Box>
        )}
      </Box>

      {/* Intro */}
      <ProfileIntro intro={profile.intro} />
    </Box>
  );
}
