'use client';

import { Box, Divider, Typography } from '@mui/material';
import SchoolIcon from '@mui/icons-material/School';

interface QualificationItem {
  degree: string;
  institution: string;
  duration: string;
}

interface QualificationCategory {
  title: string;
  items: QualificationItem[];
}

const qualificationsData: QualificationCategory[] = [
  {
    title: 'Academic Degree',
    items: [
      {
        degree: 'PhD, Cardiology',
        institution: 'Red Cross Institute of Medicine',
        duration: '2016–2019',
      },
    ],
  },
  {
    title: 'Professional Degree',
    items: [
      {
        degree: 'MD, Internal Medicine',
        institution: 'All India Medical Sciences',
        duration: '2012–2015',
      },
      {
        degree: 'DNB, General Medicine',
        institution: 'Fortis Medical College',
        duration: '2015–2017',
      },
    ],
  },
  {
    title: 'Registration and License',
    items: [
      {
        degree: 'MCI Registered',
        institution: 'Medical Council of India',
        duration: '2014–Present',
      },
    ],
  },
];

export default function QualificationsSection({
  data = qualificationsData,
}: {
  data?: QualificationCategory[];
}) {
  return (
    <Box
      maxWidth="976px"
      padding="20px"
      borderRadius="8px"
      display="flex"
      flexDirection="column"
      gap="20px"
      width="100%"
      bgcolor="white"
    >
      <Typography fontSize={{ xs: '20px', sm: '24px' }} fontWeight={600}>
        Qualifications
      </Typography>

      {data.map((category, catIdx) => (
        <Box key={catIdx}>
          <Typography
            fontSize={{ xs: '16px', sm: '20px' }}
            fontWeight={600}
            mb="20px"
          >
            {category.title}
          </Typography>

          <Box display="flex" flexDirection="column" gap="20px">
            {category.items.map((item, idx) => (
              <Box key={idx} display="flex" alignItems="center">
                {/* Logo */}
                <Box
                  width={{ xs: '60px', sm: '56px' }}
                  height={{ xs: '60px', sm: '56px' }}
                  borderRadius="8px"
                  border="1px solid #A3A3A3"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                >
                  <SchoolIcon sx={{ fontSize: 32, color: '#A24295' }} />
                </Box>

                {/* Right content */}
                <Box
                  ml="16px"
                  gap="8px"
                  display={'flex'}
                  flexDirection={'column'}
                >
                  <Box
                    display="flex"
                    flexDirection={{ xs: 'column', sm: 'row' }}
                    gap="8px"
                    alignItems={{ xs: 'flex-start', sm: 'center' }}
                  >
                    <Typography fontSize="16px" fontWeight={600}>
                      {item.degree}
                    </Typography>
                    <Box
                      display={{ xs: 'none', sm: 'block' }}
                      width="1px"
                      height="20px"
                      bgcolor="#1E1E1E"
                    />
                    <Typography fontSize="16px" fontWeight={500}>
                      {item.duration}
                    </Typography>
                  </Box>
                  <Typography
                    fontSize={{ xs: '12px', sm: '16px' }}
                    fontWeight={400}
                    color="#737678"
                  >
                    {item.institution}
                  </Typography>
                </Box>
              </Box>
            ))}
          </Box>

          {catIdx !== data.length - 1 && (
            <Divider
              sx={{ my: '24px', borderColor: '#A3A3A3', opacity: 0.5 }}
            />
          )}
        </Box>
      ))}
    </Box>
  );
}
