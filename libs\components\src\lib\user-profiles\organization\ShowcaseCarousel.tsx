'use client';

import { Box, Typography, IconButton } from '@mui/material';
import { useState } from 'react';
import { Iconify } from '../../iconify';
import ShowcaseCard from './ShowcaseCard';
import { AnimatePresence, motion } from 'framer-motion';

const showcaseItems = [
  {
    id: 1,
    image: '/assets/user-profile/profile-header.jpg',
    title: 'HeartWise Remote Monitoring Program',
    highlight: 'Advanced cardiac care, right from your home.',
    description:
      'Our HeartWise Remote Monitoring Program empowers patients with chronic or post-operative cardiac conditions to stay connected with their care team—without leaving home. Using wearable technology and real-time data monitoring, HeartWise allows our specialists to track vital signs, detect early warning signs, and adjust care plans proactively.',
  },
  {
    id: 2,
    image: '/assets/user-profile/profile-header.jpg',
    title: 'Remote Cardiac Rehab',
    highlight: 'Recover confidently from your home.',
    description:
      'Our Remote Cardiac Rehab program delivers personalized exercise, dietary guidance, and progress tracking to help patients recover post-surgery or manage chronic heart conditions effectively.',
  },
];

const carouselVariants = {
  enter: (direction: number) => ({
    x: direction > 0 ? 300 : -300,
    opacity: 1,
    position: 'absolute',
  }),
  center: {
    x: 0,
    opacity: 1,
    position: 'relative',
  },
  exit: (direction: number) => ({
    x: direction < 0 ? 300 : -300,
    opacity: 1,
    position: 'absolute',
  }),
};

export default function ShowcaseCarousel() {
  const [[index, direction], setIndex] = useState<[number, number]>([0, 0]);

  const handleNext = () => {
    setIndex(([prev]) => [(prev + 1) % showcaseItems.length, 1]);
  };

  const handlePrev = () => {
    setIndex(([prev]) => [
      (prev - 1 + showcaseItems.length) % showcaseItems.length,
      -1,
    ]);
  };

  const item = showcaseItems[index];

  return (
    <Box mt="20px" display="flex" p={{ xs: '16px', sm: '0px' }}>
      <Box maxWidth="976px" width="100%" bgcolor="white" borderRadius="8px">
        <Typography fontWeight={600} fontSize="24px" mb="20px" px="20px">
          Showcase
        </Typography>

        <Box
          display="flex"
          alignItems="center"
          position="relative"
          overflow="hidden"
          height={{ xs: '735px', sm: '280px' }}
        >
          <IconButton onClick={handlePrev}>
            <Iconify
              icon="flowbite:angle-left-outline"
              width={{ xs: 24, sm: 36 }}
              height={{ xs: 24, sm: 36 }}
              color="#A24295"
            />
          </IconButton>

          <Box
            flex={1}
            position="relative"
            height="100%"
            display="flex"
            overflow="hidden"
          >
            <AnimatePresence custom={direction} mode="wait">
              <motion.div
                key={item.id}
                custom={direction}
                variants={carouselVariants}
                initial="enter"
                animate="center"
                exit="exit"
                transition={{ duration: 0.4 }}
                style={{
                  display: 'flex',
                  width: '100%',
                  gap: '20px',
                }}
              >
                <ShowcaseCard {...item} />
              </motion.div>
            </AnimatePresence>
          </Box>

          <IconButton
            onClick={handleNext}
            sx={{ ml: { xs: '0px', sm: '20px' } }}
          >
            <Iconify
              icon="flowbite:angle-right-outline"
              width={{ xs: 24, sm: 36 }}
              height={{ xs: 24, sm: 36 }}
              color="#A24295"
            />
          </IconButton>
        </Box>
      </Box>
    </Box>
  );
}
