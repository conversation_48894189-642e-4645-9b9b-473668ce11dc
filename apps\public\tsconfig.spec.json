{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "./out-tsc/jest", "jsx": "preserve", "types": ["jest", "node"], "module": "esnext", "moduleResolution": "bundler"}, "include": ["jest.config.ts", "src/**/*.test.ts", "src/**/*.spec.ts", "src/**/*.test.tsx", "src/**/*.spec.tsx", "src/**/*.test.js", "src/**/*.spec.js", "src/**/*.test.jsx", "src/**/*.spec.jsx", "src/**/*.d.ts"], "references": [{"path": "./tsconfig.json"}]}