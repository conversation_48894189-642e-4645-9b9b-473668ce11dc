import { Avatar, Box, Typography } from '@mui/material';
import ProfileHeaderMobile from '../common/ProfileHeaderMobile';
import { Profile } from '@minicardiac-client/types';
import CurvedMuiRating from '../common/CurvedRatingStars';
import { formatFollowers } from '@minicardiac-client/utilities';
import ActionButtons from '../common/ActionButtons';

export const ProfessionalMobileProfileHeader = ({
  profile,
}: {
  profile: Profile;
}) => {
  return (
    <ProfileHeaderMobile
      profile={profile}
      ConnectUserComponent={MobileConnectButtons}
      ProfileConnectionsComponent={MobileProfileInfo}
    />
  );
};

const MobileConnectButtons = () => {
  return <ActionButtons />;
};

const MobileProfileInfo = ({ profile }: { profile: Profile }) => {
  const formattedConnections =
    profile.connections > 500 ? '500+' : profile.connections;
  const formattedFollowers = formatFollowers(profile.followers);

  return (
    <>
      <Box sx={{ position: 'relative' }}>
        <Avatar
          src={profile.profilePic}
          sx={{
            width: 121,
            height: 121,
            border: '4px solid white',
            position: 'absolute',
            bottom: -160,
            left: 16,
          }}
        />

        <CurvedMuiRating
          rating={profile.rating}
          radius={60}
          size="small"
          yAngle={-130}
          xAngle={15}
        />
      </Box>

      <Box
        sx={{
          position: 'absolute',
          left: 150,
          bottom: -50,
          height: '40px',
          display: 'flex',
          alignItems: 'center',
          gap: 2,
        }}
      >
        {/* Connections */}
        <Box sx={{ textAlign: 'center' }}>
          <Typography fontSize={16} fontWeight={600}>
            {formattedConnections}
          </Typography>
          <Typography fontSize={12} fontWeight={400}>
            Connections
          </Typography>
        </Box>

        {/* Divider */}
        <Box
          sx={{
            width: '1px',
            height: '30px',
            backgroundColor: '#ccc',
          }}
        />

        {/* Followers */}
        <Box sx={{ textAlign: 'center' }}>
          <Typography fontSize={16} fontWeight={600}>
            {formattedFollowers}
          </Typography>
          <Typography fontSize={12} fontWeight={400}>
            Followers
          </Typography>
        </Box>
      </Box>
    </>
  );
};
