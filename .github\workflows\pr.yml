name: PR Validation

on:
  workflow_dispatch:
  pull_request:
    branches:
      - '*'
      - '!staging' # do not run when the PR is created to staging
    types: [opened, synchronize, reopened]

# Set the access for individual scopes, or use permissions: write-all
permissions:
  pull-requests: write
  contents: read
  actions: read
  deployments: write

env:
  NODE_VERSION: 20

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - name: Check out Git repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Fetch all history so git can diff against develop

      - name: Fetch base branch
        run: git fetch origin ${{ github.base_ref }}:${{ github.base_ref }}

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          version: latest

      - name: Cache node modules
        uses: actions/cache@v4
        id: cache-npm
        with:
          path: node_modules
          key: ${{ runner.os }}-npm-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-npm-

      - name: Install Node.js dependencies
        if: ${{ steps.cache-npm.outputs.cache-hit != 'true' }}
        run: |
          pnpm install --frozen-lockfile

      - run: pnpm exec nx affected -t lint --base=${{ github.base_ref }} --head=HEAD
