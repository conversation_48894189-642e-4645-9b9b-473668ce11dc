'use client';

import { Box } from '@mui/material';
import TagBox from './TagBox';
import { useTranslations } from 'next-intl';
import { useTagsWithFollowState } from '@minicardiac-client/apis';

interface FullTagSectionViewProps {
  tagType: 'your-tags' | 'suggested';
}

export default function FullTagSectionView({
  tagType,
}: FullTagSectionViewProps) {
  const t = useTranslations('tagsPage');
  const isSuggested = tagType === 'suggested';

  const {
    isLoading,
    followingTags,
    suggestedTags,
    followingState,
    handleToggleFollow,
  } = useTagsWithFollowState({ fetchSuggested: isSuggested });

  if (isLoading) {
    return <div>{t('loading')}</div>;
  }

  // Select tags and render based on tagType
  const tagsToRender = isSuggested ? suggestedTags : followingTags;

  return (
    <Box
      sx={{
        display: 'flex',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
        gap: '20px',
        mt: '20px',
      }}
    >
      {tagsToRender.map((tag) => {
        const isFollowing = isSuggested
          ? followingState[tag.tagId] ?? false
          : true;

        return (
          <Box
            key={tag.tagId}
            sx={{
              flex: '1 1 45%',
              minWidth: '300px',
              maxWidth: '100%',
            }}
          >
            <TagBox
              tagName={tag.tagName}
              posts={tag.posts || []}
              sx={{ backgroundColor: 'white', width: '100%' }}
              followButtonProps={{
                isFollowing,
                onClick: () => handleToggleFollow(tag.tagId, isFollowing),
              }}
              following={isFollowing}
            />
          </Box>
        );
      })}
    </Box>
  );
}
