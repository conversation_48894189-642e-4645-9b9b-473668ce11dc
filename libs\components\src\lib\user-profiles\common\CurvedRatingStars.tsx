import { Box, Rating } from '@mui/material';

interface CurvedRatingProps {
  rating?: number;
  radius?: number;
  size?: 'small' | 'medium' | 'large';
  xAngle?: number;
  yAngle?: number;
}

const CurvedMuiRating = ({
  rating = 0,
  radius = 62,
  size = 'small',
  xAngle = 0,
  yAngle = 110,
}: CurvedRatingProps) => {
  const totalStars = 5;

  const angles = Array.from({ length: totalStars }, (_, i) => {
    const spread = 15;
    const startAngle = 90 - ((totalStars - 1) * spread) / 2;
    return startAngle + i * spread;
  });

  return (
    <Box
      sx={{
        width: radius * 2,
        height: radius + 20,
        position: 'relative',
        mt: 1,
        transform: 'scaleX(-1)',
      }}
    >
      {angles.map((deg, i) => {
        const angle = (deg * Math.PI) / 180;
        const x = radius + radius * Math.cos(angle) - xAngle;
        const y = radius + radius * Math.sin(angle) - yAngle;

        let starValue = 0;
        if (i + 1 <= Math.floor(rating)) {
          starValue = 1;
        } else if (i < rating) {
          starValue = 0.5;
        }

        return (
          <Box
            key={i}
            sx={{
              position: 'absolute',
              top: y,
              left: x,
              transform: 'translate(-50%, -50%) ',
            }}
          >
            <Rating
              value={starValue}
              precision={0.5}
              readOnly
              max={1}
              size={size}
              sx={{
                color: '#FFD700',
                '& .MuiRating-iconEmpty': {
                  color: '#FFD700',
                },
              }}
            />
          </Box>
        );
      })}
    </Box>
  );
};

export default CurvedMuiRating;
