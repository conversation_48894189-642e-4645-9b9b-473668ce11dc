'use client';

import { Box, Button, Typography } from '@mui/material';

export default function EmptyAboutFallback() {
  return (
    <Box
      display="flex"
      gap="20px"
      alignItems="center"
      flexWrap="wrap"
      sx={{
        position: { xs: 'fixed', sm: 'static' },
        bottom: '70px',
        zIndex: 100,
        width: '100%',
      }}
    >
      <Box
        maxWidth={{ xs: '100%', lg: '400px' }}
        width={'100%'}
        bgcolor={{ xs: '#FFFFFF', sm: '#F8F9FA' }}
        borderRadius="8px"
        p="20px"
        display="flex"
        flexDirection="column"
        alignItems={'center'}
        gap="20px"
      >
        <Box display={{ xs: 'none', sm: 'block' }}>
          <Typography>
            This is your About page - a customisable space to showcase your
            work, credentials, achievements, & more!
          </Typography>

          <Typography>
            Use the default templates to create a dynamic landing page and
            document your work history & credentials.
          </Typography>

          <Typography>
            From there, you can add additional sections as you need, and reorder
            them however you like!
          </Typography>
        </Box>
        <Button
          variant="contained"
          color="primary"
          sx={{
            width: { xs: '152px', sm: '180px' },
          }}
        >
          Get Started →
        </Button>
      </Box>
    </Box>
  );
}
