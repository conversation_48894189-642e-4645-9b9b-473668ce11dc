import { AnswerComment } from '../comments/comments.types.js';

export type FeedPostType = {
  id: string;
  username: string;
  title?: string;
  content: string;
  postedAt: string;
  isLiked: boolean;
  likesCount: number;
  commentsCount: number;
  repostCount: number;
  shareCount: number;
  publisherName?: string;
  profileImageUrlThumbnail?: string;
  postType: 'question' | 'poll' | 'article' | 'media' | 'text' | 'link';
  featuredComment?: AnswerComment | null;
  coverImagePath?: string;
  postMedias: Array<{
    id: string;
    mediaPath: string;
    mediaType: string;
    order: number;
    altText?: string;
    thumbnailPath?: string;
    width?: number;
    height?: number;
  }>;
  tags?: string[];
  question: string;
  options: Option[];
  allowCustomAnswer: boolean;
  postScheduleDate?: string;
  totalVotes: number;
  expiresAt: string;
  // Link post specific fields
  linkUrl?: string;
  linkPreview?: {
    title: string;
    description: string;
    image: string;
    domain: string;
  };
};

export interface FeedSearchState {
  searchKeyword: string;
  setSearchKeyword: (keyword: string) => void;
  postTypes: string[];
  setPostTypes: (types: string[]) => void;
}

export interface FeedState {
  feed: FeedPostType[];
  setFeed: (feed: FeedPostType[]) => void;
  updateLike: (postId: string, isLiked: boolean) => void;
}

export interface Option {
  id: string;
  isCreatedByCurrentUser: boolean;
  isCustom: boolean;
  order: number;
  text: string;
  isUserVote: boolean;
  votesCount: number;
  percentage: number;
}

export interface PollData {
  question: string | undefined;
  options: Option[] | undefined;
  totalVotes: number;
  allowCustomAnswer: boolean;
  expiresAt: string;
}
