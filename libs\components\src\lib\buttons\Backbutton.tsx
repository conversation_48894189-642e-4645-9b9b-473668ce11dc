import { IconButton, SxProps } from '@mui/material';
import { Iconify } from '../iconify';

export const BackButton = ({
  onClick,
  sx,
}: {
  onClick?: () => void;
  sx?: SxProps;
}) => {
  return (
    <IconButton
      onClick={onClick}
      sx={{
        ...sx,
      }}
    >
      <Iconify
        icon={'ion:arrow-back-outline'}
        sx={{
          width: 36,
          height: 36,
          color: 'secondary.main',
          ...sx,
        }}
      />
    </IconButton>
  );
};
