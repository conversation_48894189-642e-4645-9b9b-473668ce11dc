export interface LinkPostProps {
  user?: {
    name: string;
    profilePic: string;
    postedAgo: string;
  };
  linkUrl?: string;
  content?: string;
  linkPreview?: {
    title: string;
    description: string;
    image: string;
    domain: string;
  };
  likes?: number;
  comments?: number;
  reposts?: number;
  shares?: number;
  isLiked?: boolean;
  postId?: string;
  tags?: string[];
  onLikeChange?: (isLiked: boolean) => void;
}
