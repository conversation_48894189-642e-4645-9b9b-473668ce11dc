import { ReactNode } from 'react';
import { use } from 'react';
import { getMessages, setRequestLocale } from 'next-intl/server';
import { notFound } from 'next/navigation';
import LocaleLayout from './LocaleLayout';
import { hasLocale } from 'next-intl';
import { routing } from '../../i18n/routing';

export function generateStaticParams() {
  return routing.locales.map((locale) => ({ locale }));
}

export default function ServerLayout({
  children,
  params: paramsPromise,
}: {
  children: ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = use(paramsPromise);

  console.log('ServerLayout locale:', locale);

  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  setRequestLocale(locale);

  const messages = use(getMessages({ locale }));

  return (
    <LocaleLayout locale={locale} messages={messages}>
      {children}
    </LocaleLayout>
  );
}
