'use client';

import { useState } from 'react';
import { Box, Typography, Divider, IconButton } from '@mui/material';
import PollOptionCard from '../poll-post/PollOptionCard';
import CustomAnswerList from '../poll-post/CustomAnswerList';
import CustomAnswerSection from '../poll-post/CustomAnswerSection';
import { Iconify } from '../iconify';
import { useTranslations } from 'next-intl';

import { Option } from '@minicardiac-client/types';
import { useSubmitPollVote } from '@minicardiac-client/apis';

interface MainPollContentProps {
  question?: string;
  options?: Option[];
  allowCustomAnswer: boolean;
  totalVotes: number;
  postId: string;
  expiresAt: string;
}

export default function MainPollContent({
  question,
  options,
  allowCustomAnswer = false,
  totalVotes = 0,
  postId,
  expiresAt,
}: MainPollContentProps) {
  const t = useTranslations('pollPost');

  const [showCustom, setShowCustom] = useState(false);
  const [customAnswer, setCustomAnswer] = useState('');
  const [hasInteracted, setHasInteracted] = useState(false);

  const [pollData, setPollData] = useState({
    question,
    options,
    totalVotes,
    allowCustomAnswer,
    expiresAt,
  });

  const { mutate: vote } = useSubmitPollVote({
    onSuccess: (updatedPollData) => {
      if (updatedPollData) {
        setPollData({
          question: updatedPollData.question,
          options: updatedPollData.options,
          totalVotes: updatedPollData.totalVotes,
          allowCustomAnswer: updatedPollData.allowCustomAnswer,
          expiresAt: updatedPollData.expiresAt,
        });
      }
    },
  });

  const handleSelect = (optionId: string) => {
    setHasInteracted(true);
    vote({ postId: postId, optionId: optionId, customText: null });
  };

  const handleCustomSubmit = () => {
    setHasInteracted(true);
    vote({ postId: postId, optionId: null, customText: customAnswer });
    setCustomAnswer('');
  };

  return (
    <Box
      sx={{
        border: '1px solid #A3A3A3',
        borderRadius: '8px',
        p: '16px',
      }}
    >
      {/* Poll Question */}
      <Typography
        sx={{
          fontWeight: 600,
          fontSize: '16px',
          lineHeight: '18px',
          my: '16px',
        }}
      >
        {pollData.question}
      </Typography>

      {/* Poll Options */}
      <Box display="flex" flexDirection="column" gap="16px">
        {pollData.options?.map(
          (option) =>
            !option?.isCustom && (
              <PollOptionCard
                key={option.id}
                label={option.text}
                votes={option.votesCount}
                totalVotes={pollData.totalVotes}
                percentage={option.percentage}
                selected={option.isUserVote}
                onSelect={() => handleSelect(option.id)}
                isCustom={false}
                expiresAt={pollData.expiresAt}
              />
            )
        )}
      </Box>

      {pollData.allowCustomAnswer && (
        <>
          {/* Custom Answer Toggle */}
          {!hasInteracted && (
            <Box
              mt="16px"
              textAlign="center"
              sx={{ color: '#A24295', cursor: 'pointer' }}
              onClick={() => {
                setShowCustom(!showCustom);
                setHasInteracted(true);
              }}
            >
              <Typography sx={{ fontWeight: 600, fontSize: '16px' }}>
                {t('selectCustomAnswers')}
              </Typography>
              <IconButton size="small">
                <Iconify
                  icon={showCustom ? 'mdi:chevron-up' : 'mdi:chevron-down'}
                  color="#A24295"
                />
              </IconButton>
            </Box>
          )}
          {/* Custom Answers */}
          {(showCustom || hasInteracted) && (
            <CustomAnswerList
              answers={pollData.options}
              totalVotes={pollData.totalVotes}
              postId={postId}
              setPollData={setPollData}
              expiresAt={pollData.expiresAt}
              onSelect={handleSelect}
            />
          )}

          {/* Divider */}
          <Divider sx={{ mb: '16px', borderColor: '#A3A3A3', opacity: 0.5 }} />

          {/* Custom Answer Section */}
          <CustomAnswerSection
            value={customAnswer}
            onChange={(val) => setCustomAnswer(val)}
            onSubmit={handleCustomSubmit}
          />
        </>
      )}
    </Box>
  );
}
