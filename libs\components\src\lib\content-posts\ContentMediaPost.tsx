'use client';

import { forwardRef, useState } from 'react';
import { Box, Divider } from '@mui/material';
import PostFooterActions from './PostFooterActions';
import PostHeader from './PostHeader';
import ContentMediaPostDialog from './ContentMediaPostDialog';
import { ContentMediaImages } from '../media-post/ContentMediaImages';
import { ContentMediaPostCaption } from '../media-post/ContentMediaPostCaption';
import { useFeedStore } from '../store/useFeedStore';
import { getCdnUrl } from '@minicardiac-client/utilities';
import { PostTags } from '../tags/PostTags';

interface MediaPostProps {
  postId: string;
  ghost?: boolean;
}

const MAX_LINES = 3;

const ContentMediaPost = forwardRef(function ContentMediaPost(
  {
    postId,
    ghost = false,
    ...eventHandlers
  }: MediaPostProps & React.HTMLAttributes<HTMLDivElement>,
  ref: React.Ref<HTMLDivElement>
) {
  const [showMore] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [showComments, setShowComments] = useState(false);

  const handleCommentClick = () => {
    setShowComments((prev) => !prev);
  };

  // Get post data from Zustand store
  const post = useFeedStore((state) => state.feed.find((p) => p.id === postId));
  if (!post) return null;

  const user = {
    name: post.publisherName || 'Anonymous',
    profilePic: post.profileImageUrlThumbnail
      ? getCdnUrl(post.profileImageUrlThumbnail)
      : '/placeholder-avatar.png',
    postedAgo: new Date(post.postedAt).toLocaleDateString(),
  };

  const media =
    post.postMedias?.map((m: { mediaPath: string }) =>
      getCdnUrl(m.mediaPath)
    ) || [];

  return (
    <Box
      ref={ref}
      {...eventHandlers}
      sx={{
        width: '100%',
        p: { xs: '16px', sm: '20px' },
        borderRadius: '12px',
        backgroundColor: '#fff',
        boxShadow: '0px 1px 6px rgba(0, 0, 0, 0.05)',
        boxSizing: 'border-box',
        cursor: eventHandlers.onMouseDown ? 'grab' : 'default',
      }}
    >
      <PostHeader user={user} showOptions={true} />

      <ContentMediaPostCaption
        MAX_LINES={MAX_LINES}
        content={post.content}
        showMore={showMore}
        setOpenDialog={setOpenDialog}
        mediaId={post.id}
      />

      <Box sx={{ mt: '20px' }}>
        <ContentMediaImages media={media} setOpenDialog={setOpenDialog} />
      </Box>

      <PostTags tags={post.tags || []} />

      <Divider
        sx={{ mt: '12px', mb: '8px', borderColor: '#A3A3A3', opacity: 0.5 }}
      />

      <PostFooterActions
        likes={post.likesCount}
        isLiked={post.isLiked}
        commentsCount={post.commentsCount}
        reposts={post.repostCount}
        shares={post.shareCount}
        onOpenComments={handleCommentClick}
        showComments={showComments}
        setShowComments={setShowComments}
        postId={postId}
      />

      <ContentMediaPostDialog
        open={openDialog}
        onClose={() => setOpenDialog(false)}
        media={media}
        user={user}
        content={post.content}
        likes={post.likesCount}
        reposts={post.repostCount}
        shares={post.shareCount}
        postId={post.id}
        isLiked={post.isLiked}
      />
    </Box>
  );
});

export default ContentMediaPost;