'use client';

import { useState } from 'react';
import { Box, useMediaQuery } from '@mui/material';

import QualificationsSection from '../common/QualificationsSection';
import WorkExperienceSection from '../common/WorkExperienceSection';
import { useTheme } from '@emotion/react';
import { Profile } from '@minicardiac-client/types';
import { StudentProfileHeader } from './StudentProfileHeader';
import { StudentMobileProfileHeader } from './StudentMobileProfileHeader';
import ProfileSkeletonUI from '../../skeleton-ui/ProfileSkeletonUI';
import ProfileAboutSection from '../common/ProfileAboutSection';

// use or modify this constant data till api integration
const PROFILE_DATA = {
  name: '<PERSON><PERSON>',
  college: 'St. Andrews Medical School, Copenhagen',
  profilePic: '/assets/user-profile/profile-image.jpg',
  backgroundImage: '/assets/user-profile/profile-header.jpg',
  connections: 576,

  rating: 4,
  followers: 3565,
  intro:
    'Dedicated and driven medical student with a passion for patient care, research, and advancing medical knowledge.',
  video: '/assets/user-profile/sample-video.mp4',
  awards: [
    {
      title: 'HSJ Award',
      description: 'For Excellence in Patient Care | 2016',
      recipient: 'Recipient of the',
      icon: '/assets/user-profile/award-1.svg',
    },
    {
      title: 'National Scholar Award',
      description: 'For Academic Excellence | 2015',
      recipient: 'Recipient of the',
      icon: '/assets/user-profile/award-2.svg',
    },
  ],
  about: `I’m Dr. Simmons, a board-certified cardiologist with a passion for preventing heart disease before it starts. While treating cardiovascular conditions is a vital part of my work, my true focus is helping patients take proactive steps to protect their heart health and overall well-being.

With 15 years of experience in cardiology, I’ve seen firsthand how lifestyle choices, early detection, and personalized care can dramatically reduce the risk of heart disease. My approach is built on evidence-based medicine, cutting-edge diagnostics, and a deep commitment to patient education. Whether it’s optimizing nutrition, designing heart-healthy exercise plans, or managing risk factors like high blood pressure and cholesterol, I work closely with my patients to create sustainable, effective strategies for lifelong health.`,
};

export default function StudentProfile({ username }: { username: string }) {
  const theme: any = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const [profile] = useState<Profile>(PROFILE_DATA);

  if (!profile) return <ProfileSkeletonUI />;

  return (
    <Box
      sx={{
        mt: { xs: '0px', sm: '40px' },
      }}
    >
      {isMobile ? (
        <StudentMobileProfileHeader profile={profile} />
      ) : (
        <StudentProfileHeader profile={profile} />
      )}

      {/* About Section */}
      <ProfileAboutSection profile={profile} showEdit={true} />

      <Box mt="20px" display="flex" p={{ xs: '16px', sm: '0px' }}>
        <QualificationsSection />
      </Box>

      <Box mt="20px" display="flex" p={{ xs: '16px', sm: '0px' }}>
        <WorkExperienceSection />
      </Box>
    </Box>
  );
}
