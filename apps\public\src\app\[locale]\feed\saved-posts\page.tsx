'use client';

import DashboardLayout from '@/libs/components/src/lib/layout/DashboardLayout';
import { ProtectedRoute } from '../../../../components/protected-route';
import { useEffect } from 'react';
import { useAuth } from '@minicardiac-client/apis';
import { useRouter } from '@/apps/public/src/i18n/navigation';
import { FullPageLoader } from '@minicardiac-client/components';
import Head from '@/libs/components/src/lib/head/Head';

export default function SavedPostsPageWrapper() {
  const { authState } = useAuth();
  const router = useRouter();

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authState.isLoading && !authState.isAuthenticated) {
      router.push('/signin');
    }
  }, [authState.isLoading, authState.isAuthenticated, router]);

  // Show loading state while checking authentication
  if (authState.isLoading) {
    return <FullPageLoader open={true} />;
  }

  if (authState.isAuthenticated) {
    return (
      <>
        <Head title="Saved Posts" />
        <ProtectedRoute>
          <DashboardLayout />
        </ProtectedRoute>
      </>
    );
  }

  // Return null while redirecting
  return null;
}
