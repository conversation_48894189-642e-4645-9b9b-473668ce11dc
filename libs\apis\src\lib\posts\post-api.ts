import { AnswerComment, TagWithPosts } from '@minicardiac-client/types';
import { apiClient } from '../networking/api-client.js';
import { GetFeedParams, FeedResponse, GetTagsParams } from './types.js';

export interface Comment {
  id: string;
  content: string;
  userId: string;
  userFullName: string;
  userProfilePic?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateCommentRequest {
  comment: string;
}

export interface CreateTextPostRequest {
  community: 'PROFESSIONAL' | 'PUBLIC' | 'BOTH';
  audience: 'CARDIAC_SURGEON' | 'CARDIOLOGIST' | 'BOTH';
  tags: string[];
  postStatus: 'draft' | 'published' | 'scheduled';
  postScheduleDate?: string; // ISO date string
  content: string;
}

export interface CreateQuestionPostRequest {
  community: 'PROFESSIONAL' | 'PUBLIC' | 'BOTH';
  audience: 'CARDIAC_SURGEON' | 'CARDIOLOGIST' | 'BOTH';
  tags: string[];
  postStatus: 'draft' | 'published' | 'scheduled';
  postScheduleDate?: string;
  content: string;
}

export interface CreatePollPostRequest {
  community: 'PROFESSIONAL' | 'PUBLIC' | 'BOTH';
  audience: 'CARDIAC_SURGEON' | 'CARDIOLOGIST' | 'BOTH';
  tags: string[];
  postStatus: 'draft' | 'published' | 'scheduled';
  postScheduleDate?: string;
  content: string;
  options: string[];
  question?: string;
  allowCustomAnswer: boolean;
  durationDays: number;
}

export interface CreateLinkPostRequest {
  link: string;
  content: string;
  tags: string[];
  community: 'PROFESSIONAL' | 'PUBLIC' | 'BOTH';
  audience: 'CARDIAC_SURGEON' | 'CARDIOLOGIST' | 'BOTH';
  postStatus: 'draft' | 'published' | 'scheduled';
  postScheduleDate?: string;
}

export const postApi = {
  createTextPost: async (data: CreateTextPostRequest) => {
    const response = await apiClient.post('/posts/text', data);
    return response.data;
  },

  createQuestionPost: async (data: CreateQuestionPostRequest) => {
    const response = await apiClient.post('/posts/question', data);
    return response.data;
  },

  createPollPost: async (data: CreatePollPostRequest) => {
    const response = await apiClient.post('/posts/poll', data);
    return response.data;
  },

  createLinkPost: async (data: CreateLinkPostRequest) => {
    const response = await apiClient.post('/posts/link', data);
    return response.data;
  },

  getFeed: async (params: GetFeedParams) => {
    console.log('🔄 Fetching feeds with params:', params);
    const response = await apiClient.get<FeedResponse>('/posts/feed', {
      params,
    });
    console.log('✅ Feed API Response:', response.data);
    return response.data;
  },

  getPostById: async (postId: string) => {
    const response = await apiClient.get(`/posts/feed/${postId}`);
    return response.data.data;
  },

  likePost: async (postId: string) => {
    const response = await apiClient.post(`/posts/likes/${postId}`);
    return response.data;
  },

  unlikePost: async (postId: string) => {
    const response = await apiClient.delete(`/posts/likes/${postId}`);
    return response.data;
  },

  createComment: async (postId: string, data: CreateCommentRequest) => {
    const response = await apiClient.post(`/posts/comments/${postId}`, data);
    return response.data;
  },

  deleteComment: async (commentId: string) => {
    const response = await apiClient.delete(`/posts/comments/${commentId}`);
    return response.data;
  },

  createArticlePost: async (
    data: import('./types.js').CreateArticlePostRequest
  ) => {
    const response = await apiClient.post('/posts/article', data);
    return response.data;
  },

  getComments: async (
    postId: string,
    query?: { parentCommentId?: string; limit?: number; offset?: number }
  ): Promise<AnswerComment[]> => {
    const params = new URLSearchParams();
    if (query?.parentCommentId)
      params.append('parentCommentId', query.parentCommentId);
    if (query?.limit !== undefined)
      params.append('limit', query.limit.toString());
    if (query?.offset !== undefined)
      params.append('offset', query.offset.toString());

    const url = `/posts/comments/${postId}${
      params.toString() ? '?' + params.toString() : ''
    }`;

    const response = await apiClient.get<{ data: AnswerComment[] }>(url);
    return response.data.data;
  },

  pinComment: async (commentId: string) => {
    const response = await apiClient.patch(
      `/posts/question/pin-comment/${commentId}`
    );
    return response;
  },

  unpinComment: async (postId: string) => {
    const response = await apiClient.delete(
      `/posts/question/pin-comment/${postId}`
    );
    return response;
  },

  // Submit vote
  submitPollVote: async (
    postId: string,
    optionId: string | null,
    customText: string | null
  ) => {
    const response = await apiClient.post(`/posts/polls/${postId}/vote`, {
      optionId,
      customText,
    });
    return response.data;
  },

  // Delete (retract) vote
  deletePollVote: async (postId: string) => {
    const response = await apiClient.delete(`/posts/polls/${postId}/vote`);
    return response.data;
  },

  getTags: async (params: GetTagsParams = {}) => {
    const response = await apiClient.get<{ data: TagWithPosts[] }>(
      '/tags/search',
      {
        params,
      }
    );
    return response.data;
  },

  getFollowingTagsWithPosts: async (
    params: {
      limit?: number;
      offset?: number;
      searchKeyword?: string;
    } = {}
  ) => {
    const response = await apiClient.get('/tag-follow/following', { params });
    return response.data;
  },

  getSuggestedTagsWithPosts: async (
    params: {
      limit?: number;
      offset?: number;
      searchKeyword?: string;
    } = {}
  ) => {
    const response = await apiClient.get('/tag-follow/suggestions', {
      params,
    });
    return response.data;
  },

  followTag: async (tagId: string) => {
    const response = await apiClient.post(`/tag-follow/${tagId}`);
    return response.data;
  },

  unfollowTag: async (tagId: string) => {
    const response = await apiClient.delete(`/tag-follow/${tagId}`);
    return response.data;
  },
  createOrganisationProfile: async (payload: Record<string, string>) => {
    const response = await apiClient.post(
      '/onboarding/profile-setup/organisation',
      payload
    );
    return response.data;
  },

  createProfessionalProfile: async (
    professionalType: 'ALLIED_CARDIAC' | 'CARDIAC_SURGEON' | 'CARDIOLOGIST',
    payload: Record<string, any>
  ) => {
    const endpoint =
      professionalType === 'ALLIED_CARDIAC'
        ? '/onboarding/profile-setup/allied-cardiac'
        : '/onboarding/profile-setup/specialist';

    const response = await apiClient.post(endpoint, payload);
    return response.data;
  },
};
