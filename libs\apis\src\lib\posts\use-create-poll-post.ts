import { useMutation } from '@tanstack/react-query';
import { postApi, CreatePollPostRequest } from './post-api.js';
import { toast } from 'react-toastify';
import { FeedPostType } from '@minicardiac-client/types';

interface UseCreatePollPostOptions {
  onSuccess?: (data: FeedPostType) => void;
  onError?: (error: Error) => void;
}

export function useCreatePollPost(options?: UseCreatePollPostOptions) {
  return useMutation({
    mutationFn: async (data: CreatePollPostRequest) => {
      const response = await postApi.createPollPost(data);
      return response;
    },
    onSuccess: (data: FeedPostType) => {
      toast.success('Poll post created successfully!');
      options?.onSuccess?.(data);
    },
    onError: (error: unknown) => {
      console.error('Error creating poll post:', error);
      const errorMessage = extractErrorMessage(
        error,
        'Failed to create poll post'
      );
      toast.error(errorMessage);
      const errObj = error instanceof Error ? error : new Error(errorMessage);
      options?.onError?.(errObj);
    },
  });
}

export function useSubmitPollVote(options?: UseCreatePollPostOptions) {
  return useMutation({
    mutationFn: async ({
      postId,
      optionId,
      customText,
    }: {
      postId: string;
      optionId: string | null;
      customText: string | null;
    }) => {
      const response = await postApi.submitPollVote(
        postId,
        optionId,
        customText
      );
      return response?.data.pollData;
    },
    onSuccess: (data) => {
      toast.success('Vote submitted!');
      options?.onSuccess?.(data);
    },
    onError: (error: unknown) => {
      console.error('Error submitting vote:', error);
      const errorMessage = extractErrorMessage(error, 'Failed to submit vote');
      toast.error(errorMessage);
      const errObj = error instanceof Error ? error : new Error(errorMessage);
      options?.onError?.(errObj);
    },
  });
}

export function useDeletePollVote(options?: UseCreatePollPostOptions) {
  return useMutation({
    mutationFn: async (postId: string) => {
      const response = await postApi.deletePollVote(postId);
      return response;
    },
    onSuccess: (data) => {
      toast.success('Vote removed!');
      options?.onSuccess?.(data);
    },
    onError: (error: unknown) => {
      console.error('Error deleting vote:', error);
      const errorMessage = extractErrorMessage(error, 'Failed to delete vote');
      toast.error(errorMessage);
      const errObj = error instanceof Error ? error : new Error(errorMessage);
      options?.onError?.(errObj);
    },
  });
}

function extractErrorMessage(
  error: unknown,
  fallback = 'Something went wrong'
) {
  if (
    error &&
    typeof error === 'object' &&
    'response' in error &&
    error.response &&
    typeof error.response === 'object' &&
    'data' in error.response &&
    error.response.data &&
    typeof error.response.data === 'object' &&
    'message' in error.response.data &&
    typeof error.response.data.message === 'string'
  ) {
    return error.response.data.message;
  }
  return fallback;
}
