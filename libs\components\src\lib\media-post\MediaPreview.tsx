import React from 'react';
import { Box, Typography } from '@mui/material';

interface MediaPreviewProps {
  file: File;
}

const MediaPreview: React.FC<MediaPreviewProps> = ({ file }) => {
  const fileURL = URL.createObjectURL(file);
  const fileType = file.type;

  if (fileType.startsWith('image/')) {
    return (
      <img
        src={fileURL}
        alt={file.name}
        style={{ width: '100%', height: '100%', objectFit: 'cover' }}
      />
    );
  } else if (fileType.startsWith('video/')) {
    return (
      <video
        src={fileURL}
        controls
        style={{ width: '100%', height: '100%', objectFit: 'cover' }}
      />
    );
  } else if (fileType.startsWith('audio/')) {
    return <audio src={fileURL} controls style={{ width: '100%' }} />;
  } else if (fileType === 'application/pdf') {
    return (
      <Box
        display="flex"
        alignItems="center"
        justifyContent="center"
        height="100%"
        color="#A24295"
      >
        <Typography variant="caption">PDF</Typography>
      </Box>
    );
  }

  return null;
};

export default MediaPreview;
