import { Checkbox, CheckboxProps, FormControlLabel } from '@mui/material';

interface CustomCheckboxProps extends CheckboxProps {
  label: string;
}

const CustomCheckbox = ({ label, ...props }: CustomCheckboxProps) => {
  return (
    <FormControlLabel
      control={
        <Checkbox
          {...props}
          sx={{
            color: '#A3A3A3',
            borderRadius: '8px',
            '&.Mui-checked': {
              color: '#A24295',
            },
            '& .MuiSvgIcon-root': {
              borderRadius: '8px',
            },
          }}
        />
      }
      label={label}
    />
  );
};

export default CustomCheckbox;
