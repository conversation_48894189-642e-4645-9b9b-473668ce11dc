import React from 'react';
import parse, {
  domToReact,
  HTMLReactParserOptions,
  Element,
  Text,
} from 'html-react-parser';

interface HighlightHtmlProps {
  html: string;
  keyword: string;
  className?: string;
}

function highlightText(text: string, keyword: string, className?: string) {
  if (!keyword) return text;
  const regex = new RegExp(
    `(${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`,
    'gi'
  );
  const parts = text.split(regex);
  return parts.map((part, i) =>
    regex.test(part) ? (
      <mark
        key={i}
        className={className}
        style={{ fontWeight: 'bold', background: 'none', color: 'inherit' }}
      >
        {part}
      </mark>
    ) : (
      part
    )
  );
}

export const HighlightHtml: React.FC<HighlightHtmlProps> = ({
  html,
  keyword,
  className,
}) => {
  if (!keyword) return <>{parse(html)}</>;

  const options: HTMLReactParserOptions = {
    replace: (domNode) => {
      if (domNode.type === 'text') {
        const textNode = domNode as Text;
        return <>{highlightText(textNode.data, keyword, className)}</>;
      }

      if (domNode.type === 'tag') {
        const el = domNode as Element;

        const attributes: Record<string, any> = { ...el.attribs };

        if (attributes.style && typeof attributes.style === 'string') {
          const styleObj: React.CSSProperties = {};
          // Get style once instead of in every iteration
          const style = typeof document !== 'undefined' ? document.documentElement.style as any : null;
          
          attributes.style.split(';').forEach((declaration) => {
            const [property, value] = declaration
              .split(':')
              .map((s) => s.trim());
            if (property && value) {
              const reactProperty = property.replace(/-([a-z])/g, (g) =>
                g[1].toUpperCase()
              );
              const cssProp = reactProperty as keyof React.CSSProperties;
              // Check if the property is a valid CSS property
              const isValidCssProp = (prop: string): boolean => {
                if (!style) return false; // Handle SSR
                return prop in style || 
                       prop.startsWith('--') || // CSS variables
                       ['-webkit-appearance', '-moz-appearance', '-ms-appearance'].includes(prop);
              };

              if (isValidCssProp(cssProp)) {
                // Convert string values to appropriate types
                let typedValue: any = value;

                // Handle numeric values (e.g., '10px' -> '10px' is fine as string)
                if (/^\d+$/.test(value)) {
                  typedValue = parseFloat(value);
                }
                // Handle numeric values with units (e.g., '10px', '2rem')
                else if (/^\d+[a-z%]+$/i.test(value)) {
                  // Keep as string since CSS properties accept values like '10px', '2rem', etc.
                }
                // Handle color values
                else if (/^(#|rgb|hsl)/.test(value)) {
                  // Keep as string since it's a valid color value
                }
                // Handle other string values that need to be preserved as-is
                else if (['fontFamily', 'fontWeight', 'fontStyle', 'textDecoration'].includes(cssProp)) {
                  // Keep as string for these specific properties
                }

                styleObj[cssProp] = typedValue as any;
              }
            }
          });
          // Using a new object to avoid mutating the original attributes
          attributes.style = styleObj;
        }

        if (el.children && el.children.length > 0) {
          return React.createElement(
            el.name,
            attributes,
            domToReact(el.children as any, options)
          );
        }
      }
      return undefined;
    },
  };

  return <>{parse(html, options)}</>;
};
