'use client';

import { Box, Typography, useMediaQuery, IconButton } from '@mui/material';
import { useRef } from 'react';
import { Iconify } from '../../iconify';
import { Profile } from '@minicardiac-client/types';

const MOCK_PROFILES = Array.from({ length: 10 }).map((_, idx) => ({
  id: idx,
  name: '<PERSON>',
  designation: 'Head of Cardiology',
  profilePic: '/assets/user-profile/profile-image.jpg',
}));

export default function CardiacTeam({ profile }: { profile: Profile }) {
  const isMobile = useMediaQuery((theme: any) => theme.breakpoints.down('sm'));
  const scrollRef = useRef<HTMLDivElement>(null);

  const handleNext = () => {
    if (scrollRef.current) {
      scrollRef.current.scrollBy({ left: 200, behavior: 'smooth' });
    }
  };

  return (
    <Box mt="20px" display="flex" p={{ xs: '16px', sm: '0px' }}>
      <Box
        maxWidth="976px"
        width="100%"
        bgcolor="white"
        p={'20px'}
        borderRadius={'8px'}
      >
        {/* Title */}
        <Typography fontSize="24px" fontWeight={600} mb="20px">
          Cardiac Team
        </Typography>

        <Box
          sx={{
            maxWidth: '976px',
            display: 'flex',
          }}
        >
          <Box
            ref={scrollRef}
            display="flex"
            overflow="auto"
            gap="56px"
            pb="4px"
            sx={{
              scrollbarWidth: 'none',
              '&::-webkit-scrollbar': { display: 'none' },
            }}
          >
            {MOCK_PROFILES.map((profile) => (
              <Box
                key={profile.id}
                display="flex"
                flexDirection="column"
                alignItems="center"
                minWidth="100px"
              >
                <Box
                  component="img"
                  src={profile.profilePic}
                  alt={profile.name}
                  width="100px"
                  height="100px"
                  borderRadius="50%"
                  sx={{ objectFit: 'cover' }}
                />
                <Typography fontSize="16px" fontWeight={400} mt="8px">
                  {profile.name}
                </Typography>
                <Typography
                  fontSize="12px"
                  fontWeight={400}
                  mt="8px"
                  textAlign="center"
                  noWrap
                  color="#737678"
                >
                  {profile.designation}
                </Typography>
              </Box>
            ))}
          </Box>

          {/* Scroll button on desktop */}
          {!isMobile && (
            <IconButton onClick={handleNext}>
              <Iconify
                icon="flowbite:angle-right-outline"
                width={36}
                height={36}
                color={'#A24295'}
              />
            </IconButton>
          )}
        </Box>
      </Box>
    </Box>
  );
}
