import { ProfessionalProfileFormData } from '@minicardiac-client/types';
import { useMutation } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { postApi } from '../posts/post-api.js';

export function useProfessionalProfileSetup({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: (error: string) => void;
} = {}) {
  return useMutation({
    mutationFn: async (formData: ProfessionalProfileFormData) => {
      const {
        professionalType,
        introductoryStatement,
        profileImageUrl,
        profileImageUrlThumbnail,
        title,
        qualifications,
        jobTitle,
        employerId,
        mainProfession,
        category,
      } = formData;

      const uniqueImageId = Date.now().toString();
      const defaultImageUrl = `default-profile-${uniqueImageId}.jpg`;

      const payload =
        professionalType === 'ALLIED_CARDIAC'
          ? {
              introductoryStatement: introductoryStatement || '',
              profileImageUrl: profileImageUrl || defaultImageUrl,
              profileImageUrlThumbnail:
                profileImageUrlThumbnail || defaultImageUrl,
              title: title || '',
              qualifications: qualifications || '',
              designation: jobTitle || '',
              employerId: employerId || undefined,
              primarySpeciality: mainProfession || '',
              segmentCategoryId: category || '',
            }
          : {
              introductoryStatement: introductoryStatement || '',
              profileImageUrl: profileImageUrl || defaultImageUrl,
              profileImageUrlThumbnail:
                profileImageUrlThumbnail || defaultImageUrl,
              title: title || '',
              qualifications: qualifications || '',
              jobTitle: jobTitle || '',
              employerId: employerId || undefined,
              speciality: professionalType || '',
            };

      return await postApi.createProfessionalProfile(professionalType, payload);
    },
    onSuccess: () => onSuccess?.(),
    onError: (err: AxiosError | Error) => {
      const errorMsg =
        err instanceof AxiosError &&
        err.response?.data?.message?.includes(
          'unique constraint "users_profile_image_url_unique"'
        )
          ? 'This profile image is already in use. Please choose a different image.'
          : err.message || 'Failed to save profile data';

      onError?.(errorMsg);
    },
  });
}
