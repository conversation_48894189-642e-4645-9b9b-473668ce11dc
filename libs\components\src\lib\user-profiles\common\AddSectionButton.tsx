import { Button } from '@mui/material';

interface Props {
  onClick: () => void;
  label: string;
}

const AddSectionButton = ({ label, onClick }: Props) => {
  return (
    <Button
      onClick={onClick}
      sx={{
        color: '#A24295',
        fontWeight: 600,
        fontSize: '16px',
        textTransform: 'none',
        p: 0,
        background: 'transparent',
        '&:hover': {
          backgroundColor: 'transparent',
          textDecoration: 'underline',
        },
      }}
    >
      + Add {label}
    </Button>
  );
};

export default AddSectionButton;
