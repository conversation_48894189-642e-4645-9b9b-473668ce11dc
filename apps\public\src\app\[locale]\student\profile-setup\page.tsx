'use client';

import React from 'react';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import { useRouter } from '@/apps/public/src/i18n/navigation';
import {
  CustomizedSteppers,
  WelcomeHeader,
  useSnackbar,
  FullPageLoader,
} from '@minicardiac-client/components';
import { axiosInstance } from '@minicardiac-client/apis';
import { StudentProfileSetupForm } from '@minicardiac-client/components';

// Define the student profile form data interface
interface StudentProfileFormData {
  institutionName: string;
  introductoryStatement: string;
  profileImageUrl?: string;
  profileImageUrlThumbnail?: string;
}

// Add constants for better maintainability
const PROFILE_SETUP_STEPS = [
  'Profile Setup',
  'Document Upload',
  'Adding Network',
];
const CURRENT_STEP = 0;

// API response interface for better type safety
interface ApiErrorResponse {
  message?: string;
  errors?: Record<string, string[]>;
}

export default function StudentProfileSetupPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [isSessionRefreshing, setIsSessionRefreshing] = React.useState(true);
  const { showSuccess, showError } = useSnackbar();
  const [error, setError] = React.useState<string | null>(null);

  // Add state to track current form data
  const [formData, setFormData] = React.useState<StudentProfileFormData>({
    institutionName: '',
    introductoryStatement: '',
    profileImageUrl: '',
    profileImageUrlThumbnail: '',
  });

  // Refresh the session when the component mounts
  React.useEffect(() => {
    const refreshUserSession = async () => {
      try {
        setIsSessionRefreshing(true);
      } catch (error) {
        console.error('Failed to refresh session:', error);
        // Optionally redirect to login if session refresh fails
        // router.push('/login');
      } finally {
        setIsSessionRefreshing(false);
      }
    };

    refreshUserSession();
  }, []);

  // Handle skip button click
  const handleSkip = () => {
    // Redirect to feed with fromSignup=true parameter
    router.push('/feed?fromSignup=true');
  };

  // Handle form changes - now properly updating state
  const handleFormChange = React.useCallback(
    (data: StudentProfileFormData) => {
      console.log('Form data changed:', data); // Debug log
      setFormData(data);

      // Clear any existing errors when form changes
      if (error) {
        setError(null);
      }
    },
    [error]
  );

  // Handle form submission - use current form data or passed data
  const handleSubmit = async (data?: StudentProfileFormData) => {
    setIsSubmitting(true);
    setError(null);

    // Use passed data or current form state
    const submitData = data || formData;

    console.log('Submitting data:', submitData); // Debug log

    try {
      // Validate required fields before submission with null/undefined checks
      if (!submitData.institutionName || !submitData.institutionName.trim()) {
        throw new Error('Institution name is required');
      }
      if (
        !submitData.introductoryStatement ||
        !submitData.introductoryStatement.trim()
      ) {
        throw new Error('Introductory statement is required');
      }

      // Prepare the request data according to the API requirements
      const requestData = {
        institutionName: submitData.institutionName?.trim() || '',
        introductoryStatement: submitData.introductoryStatement?.trim() || '',
        profileImageUrl: submitData.profileImageUrl || '',
        profileImageUrlThumbnail: submitData.profileImageUrlThumbnail || '',
      };

      console.log('Request data:', requestData); // Debug log

      // Call the API to save the student profile
      const response = await axiosInstance.post(
        '/onboarding/profile-setup/student',
        requestData
      );

      if (response.status === 200 || response.status === 201) {
        showSuccess('Profile saved successfully!');
        // Redirect to document upload after successful submission
        router.push('/student/document-upload');
      }
    } catch (err: any) {
      console.error('Error updating profile:', err);

      let errorMessage = 'Failed to update profile';

      // Handle different types of errors
      if (err.response?.data) {
        const apiError = err.response.data as ApiErrorResponse;
        if (apiError.message) {
          errorMessage = apiError.message;
        } else if (apiError.errors) {
          // Handle validation errors
          const firstError = Object.values(apiError.errors)[0];
          if (firstError && firstError.length > 0) {
            errorMessage = firstError[0];
          }
        }
      } else if (err.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);
      showError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show loading state while session is refreshing
  if (isSessionRefreshing) {
    return <FullPageLoader open={true} />;
  }

  return (
    <Container maxWidth="lg">
      <Box py={4}>
        <WelcomeHeader
          name="Complete Your Student Profile"
          subtitle="Tell us more about yourself to get started"
        />

        <Box mt={4}>
          <CustomizedSteppers
            activeStep={CURRENT_STEP}
            steps={PROFILE_SETUP_STEPS}
          />
        </Box>

        <Box mt={6}>
          <StudentProfileSetupForm
            onChange={handleFormChange}
            onSave={handleSubmit}
            onSkip={handleSkip}
            isSubmitting={isSubmitting}
            disabled={isSubmitting}
            initialData={formData}
          />
          {error && (
            <Box
              sx={{
                color: 'error.main',
                mt: 2,
                textAlign: 'center',
                p: 2,
                border: '1px solid',
                borderColor: 'error.main',
                borderRadius: 1,
                bgcolor: 'error.light',
                opacity: 0.1,
              }}
            >
              {error}
            </Box>
          )}
        </Box>
      </Box>
    </Container>
  );
}
