import {
  RegisterUserRequest,
  RegisterUserResponse,
  VerifyOtpRequest,
  VerifyOtpResponse,
  RegenerateOtpRequest,
  RegenerateOtpResponse,
  SessionLoginRequest,
  VerifySessionResponse,
  UserProfileData,
} from './types.js';
import { axiosInstance } from '../http-client.js';

export const registerUser = async (
  data: RegisterUserRequest
): Promise<RegisterUserResponse> => {
  const response = await axiosInstance.post('/auth/register', data);
  return {
    userId: response.data.data.userId || '',
    email: response.data.data.email || '',
    requiresOTP: response.data.data.requiresOTP || false,
  };
};

export const verifyOtp = async (
  data: VerifyOtpRequest
): Promise<VerifyOtpResponse> => {
  const response = await axiosInstance.post('/auth/verify-email', data);
  return {
    customToken: response.data.data.customToken,
  };
};

export const regenerateOtp = async (
  data: RegenerateOtpRequest
): Promise<RegenerateOtpResponse> => {
  const response = await axiosInstance.post('/auth/regenerate-otp', data);
  return {
    success: !!response.data.data,
  };
};

export const sessionLogin = async (
  data: SessionLoginRequest
): Promise<void> => {
  await axiosInstance.post('/auth/session-login', data);
};

export const getOwnProfile = async (): Promise<UserProfileData> => {
  const response = await axiosInstance.get('/users/profile');
  return response.data.data as UserProfileData;
};

export const verifySession = async (): Promise<VerifySessionResponse> => {
  const response = await axiosInstance.post('/auth/verify-session', {});
  return {
    customToken: response.data.data.customToken,
  };
};

export const logout = async (): Promise<void> => {
  await axiosInstance.post('/auth/logout', {});
};
