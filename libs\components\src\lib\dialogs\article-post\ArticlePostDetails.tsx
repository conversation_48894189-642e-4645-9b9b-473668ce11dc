'use client';

import {
  Box,
  TextField,
  Typography,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import { useRef } from 'react';
import CustomToggleButtonGroup from '../../buttons/CustomToggleButtonGroup';
import MediaUploadIcon from '../../Icons/FeedIcons/MediaUploadIcon';
import { useTranslations } from 'next-intl';
import { PostCreationNotification } from '../../common/PostCreationNotification';

interface ArticlePostDetailsProps {
  tags: string;
  setTags: (tags: string) => void;
  handleTagsChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  audience: string;
  setAudience: (audience: string) => void;
  speciality: string;
  setSpeciality: (speciality: string) => void;
  thumbnail: File | null;
  setThumbnail: (file: File | null) => void;
  title: string;
  setTitle: (value: string) => void;
  summary: string;
  setSummary: (value: string) => void;
  isPublicUser?: boolean;
}

const ArticlePostDetails = ({
  tags,
  setTags,
  handleTagsChange,
  audience,
  setAudience,
  speciality,
  setSpeciality,
  thumbnail,
  setThumbnail,
  title,
  setTitle,
  summary,
  setSummary,
  isPublicUser = false,
}: ArticlePostDetailsProps) => {
  const t = useTranslations('articlePostDetails');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const theme: any = useTheme();
  const screenBelowSM = useMediaQuery(theme.breakpoints.down('sm'));

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setThumbnail(file);
    }
  };

  const handleBoxClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <Box display="flex" flexDirection="column" gap="20px" mt="20px">
      <Box
        display="flex"
        gap="40px"
        flexDirection={{ xs: 'column', sm: 'row' }}
      >
        {/* Thumbnail Upload */}
        <Box
          width={{ xs: '100%', sm: '500px' }}
          height={{ xs: '196px', sm: '335px' }}
          border={`1px solid ${theme.palette.secondary.main}`}
          borderRadius="8px"
          display="flex"
          justifyContent="center"
          alignItems="center"
          sx={{ cursor: 'pointer', overflow: 'hidden', position: 'relative' }}
          onClick={handleBoxClick}
        >
          <input
            type="file"
            accept="image/*"
            ref={fileInputRef}
            onChange={handleFileChange}
            style={{ display: 'none' }}
          />

          {thumbnail ? (
            <Box
              component="img"
              src={URL.createObjectURL(thumbnail)}
              alt="Thumbnail Preview"
              sx={{ width: '100%', height: '100%', objectFit: 'cover' }}
            />
          ) : (
            <Box
              textAlign="center"
              display="flex"
              flexDirection="column"
              alignItems="center"
            >
              <MediaUploadIcon size={52} />
              <Typography
                fontSize={{ xs: '16px', sm: '20px' }}
                mt="12px"
                fontWeight={500}
              >
                {screenBelowSM
                  ? t('tapToSelectThumbnail')
                  : t('clickToAddThumbnail')}
              </Typography>
              <Typography
                fontSize="16px"
                mt="4px"
                color={theme.palette.neutral[500]}
                display={{ xs: 'none', sm: 'block' }}
              >
                {t('dragAndDrop')}
              </Typography>
            </Box>
          )}
        </Box>

        {/* Title & Summary */}
        <Box
          display="flex"
          flexDirection="column"
          gap="40px"
          width={{ xs: '100%', sm: '620px' }}
        >
          <TextField
            label={t('title')}
            placeholder={t('titlePlaceholder')}
            fullWidth
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            InputLabelProps={{ shrink: true }}
          />
          <TextField
            label={t('summary')}
            placeholder={t('summaryPlaceholder')}
            variant="outlined"
            size="small"
            multiline
            minRows={6}
            fullWidth
            value={summary}
            onChange={(e) => setSummary(e.target.value)}
            InputLabelProps={{ shrink: true }}
            sx={{
              '& .MuiOutlinedInput-root': {
                height: { xs: 168, sm: 247 },
                '& textarea': {
                  height: '100%',
                  boxSizing: 'border-box',
                  resize: 'none',
                  paddingTop: { xs: '0px', sm: '10px' },
                  '&::placeholder': {
                    textAlign: 'left',
                    verticalAlign: 'top',
                    paddingTop: 0,
                    lineHeight: 1.2,
                  },
                },
              },
            }}
          />
        </Box>
      </Box>

      {/* Tags & Toggles */}
      <Box
        display="flex"
        flexDirection={{ xs: 'column', md: 'row' }}
        gap={{ xs: '10px', lg: '40px' }}
        alignItems={{ xs: 'center', md: 'end' }}
        mb={{ xs: '140px', sm: '0px' }}
      >
        {/* Tag field with fixed width for public users */}
        <Box width={isPublicUser ? '240px' : '100%'}>
          <TextField
            placeholder={isPublicUser ? "PublicForum" : "#Surgery #Research"}
            value={tags}
            onChange={handleTagsChange || ((e) => setTags(e.target.value))}
            fullWidth
            label={t('tags')}
            InputLabelProps={{ shrink: true }}
            disabled={isPublicUser}
            sx={isPublicUser ? {
              '& .MuiInputBase-input': {
                color: '#666',
                cursor: 'not-allowed',
              },
              '& .MuiOutlinedInput-root': {
                backgroundColor: '#f5f5f5',
                height: '45px',
              }
            } : {}}
          />
        </Box>
        
        {/* Show notification banner side-by-side for public users */}
        {isPublicUser && (
          <Box sx={{ width: '800px', flexShrink: 0 }}>
            <PostCreationNotification
              show={true}
              onUpgradeClick={() => {
                // Handle upgrade click - you can add navigation logic here
                console.log('Navigate to upgrade page');
              }}
              width="100%"
              sx={{ height: '45px', padding: '8px 16px', display: 'flex', alignItems: 'center' }}
            />
          </Box>
        )}
        
        {/* Only show audience and speciality toggles for non-public users */}
        {!isPublicUser && (
          <>
            <Box width={{ xs: '100%', md: '224px' }}>
              <CustomToggleButtonGroup
                label={t('community')}
                options={['PROFESSIONAL', 'PUBLIC', 'BOTH']}
                selected={audience}
                onChange={setAudience}
                width={{ xs: '100%', md: '224px' }}
              />
            </Box>
            <Box width={{ xs: '100%', md: '282px' }}>
              <CustomToggleButtonGroup
                label={t('audience')}
                options={['CARDIAC_SURGERY', 'CARDIOLOGY', 'BOTH']}
                selected={speciality}
                onChange={setSpeciality}
                width={{ xs: '100%', md: '282px' }}
              />
            </Box>
          </>
        )}
      </Box>
    </Box>
  );
};

export default ArticlePostDetails;
