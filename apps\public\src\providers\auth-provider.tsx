import React, { ReactNode } from 'react';
import { AuthProvider as AuthContextProvider } from '@minicardiac-client/apis';
import SessionVerifier from '../components/session-verifier';

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  return (
    <>
      <AuthContextProvider>
        <SessionVerifier />
        {children}
      </AuthContextProvider>
    </>
  );
};
