import { create } from 'zustand';

export type DialogType = 'text' | 'media' | 'article' | 'poll' | 'question' | 'link';

interface PostDialogsState {
  activeDialog: DialogType | null;
  openTextDialog: () => void;
  openMediaDialog: () => void;
  openArticleDialog: () => void;
  openPollDialog: () => void;
  openQuestionDialog: () => void;
  openLinkDialog: () => void;
  closeDialog: () => void;
}

export const usePostDialogsStore = create<PostDialogsState>((set) => ({
  activeDialog: null,
  openTextDialog: () => set({ activeDialog: 'text' }),
  openMediaDialog: () => set({ activeDialog: 'media' }),
  openArticleDialog: () => set({ activeDialog: 'article' }),
  openPollDialog: () => set({ activeDialog: 'poll' }),
  openQuestionDialog: () => set({ activeDialog: 'question' }),
  openLinkDialog: () => set({ activeDialog: 'link' }),
  closeDialog: () => set({ activeDialog: null }),
}));