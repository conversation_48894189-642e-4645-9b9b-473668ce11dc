// scripts/generate-robots.js
const fs = require('fs');
const path = require('path');

function generateRobotsTxt() {
  // Determine environment - for Vite we need to check different env vars
  const environment = process.env.VITE_ENVIRONMENT || process.env.NODE_ENV;

  let content = '';

  switch (environment) {
    case 'production':
      content = `# Allow crawling only on production
User-agent: *
Allow: /`;
      break;

    default:
      content = `# Block all crawlers on non-production environments
User-agent: *
Disallow: /`;
      break;
  }

  // Write to public folder
  const robotsPath = path.join(process.cwd(), 'public', 'robots.txt');
  fs.writeFileSync(robotsPath, content);
  console.log(`robots.txt generated for ${environment} environment`);
}

// Allow running directly or as a module
if (require.main === module) {
  generateRobotsTxt();
} else {
  module.exports = generateRobotsTxt;
}
