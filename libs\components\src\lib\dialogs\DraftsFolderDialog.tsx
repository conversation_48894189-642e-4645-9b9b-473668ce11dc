// src/components/dialogs/DraftsFolderDialog.tsx
'use client';

import { useState, useMemo } from 'react';
import {
  Dialog,
  Box,
  Typography,
  IconButton,
  CircularProgress,
} from '@mui/material';
import { ConfirmationDialog } from './ConfirmationDialog';
import { Iconify } from '../iconify';
import { DraftPostCard } from './DraftsFolderCards';
import {
  useDrafts,
  useUpdateDraft,
  useDeleteDraft,
} from '@minicardiac-client/apis';
import { getCdnUrl } from '@minicardiac-client/utilities';
import { toast } from 'react-toastify';
import { HighlightHtml } from '../common/HighlightHtml';

interface DraftsFolderDialogProps {
  open: boolean;
  onClose: () => void;
}

export default function DraftsFolderDialog({
  open,
  onClose,
}: DraftsFolderDialogProps) {
  const { data: drafts = [], isLoading, error, refetch } = useDrafts();
  const { mutateAsync: updatePost } = useUpdateDraft();
  const { mutate: deletePost } = useDeleteDraft();
  const [editingPostId, setEditingPostId] = useState<string | null>(null);

  const handleStartEdit = (postId: string) => {
    console.log('Starting edit for draft:', postId);
    setEditingPostId(postId);
  };

  const handleSaveEdit = async (postId: string, newContent: string) => {
    try {
      const post = drafts.find((p) => p.id === postId);
      if (!post) return;

      console.log('Saving edit for draft:', postId, 'New content:', newContent);

      // Prepare payload based on post type
      const basePayload = {
        content: newContent,
        postStatus: 'draft',
        community: 'PROFESSIONAL',
        audience: 'BOTH',
        tags: [],
      };

      let payload;
      switch (post.postType) {
        case 'text':
        case 'question':
          payload = basePayload;
          break;
        case 'poll':
          payload = {
            ...basePayload,
            question: post.title || '',
            durationDays: 1,
            allowCustomAnswer: true,
            options: [],
          };
          break;
        case 'link':
          payload = {
            ...basePayload,
            link: post.linkUrl || '',
          };
          break;
        case 'article':
          payload = {
            ...basePayload,
            title: post.title || '',
            body: post.content || '',
            coverImagePath: post.coverImagePath || '',
          };
          break;
        case 'media':
          payload = {
            ...basePayload,
            medias:
              post.postMedias?.map((media) => {
                const mediaPayload: any = {
                  mediaPath: media.mediaPath,
                  mediaType: media.mediaType
                };
                // Only include altText if it exists and meets minimum length requirement
                if (media.altText && media.altText.trim().length >= 3) {
                  mediaPayload.altText = media.altText;
                }
                return mediaPayload;
              }) || [],
          };
          break;
        default:
          payload = basePayload;
      }

      await updatePost({
        postId,
        postType: post.postType || 'text',
        payload,
      });

      setEditingPostId(null);
      await refetch();
      toast.success('Draft updated successfully');
    } catch (error) {
      console.error('Error updating draft:', error);
      toast.error('Failed to update draft');
      throw error;
    }
  };

  const handleCancelEdit = () => {
    setEditingPostId(null);
  };

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [draftToDelete, setDraftToDelete] = useState<string | null>(null);

  const handleDeleteClick = (postId: string) => {
    setDraftToDelete(postId);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = () => {
    if (!draftToDelete) return;

    deletePost(draftToDelete, {
      onSuccess: () => {
        setDeleteDialogOpen(false);
        setDraftToDelete(null);
        // The onSuccess callback in the mutation will handle the success toast
      },
      onError: (error) => {
        console.error('Error deleting draft:', error);
        toast.error('Failed to delete draft');
      }
    });
  };

  const handleCancelDelete = () => {
    setDeleteDialogOpen(false);
    setDraftToDelete(null);
  };

  const handlePublish = (postId: string) => {
    // TODO: Implement publish functionality
    console.log('Publish draft:', postId);
    toast.info('Publish functionality coming soon');
  };
  console.log('<<<< Raw drafts data:', drafts);

  // Transform the draft data to match the expected format
  const formattedDrafts = useMemo(() => {
    return drafts.map((post) => {
      const mediaPath = post.postMedias?.[0]?.mediaPath;
      const mediaUrl = mediaPath ? getCdnUrl(mediaPath) : undefined;
      const currentDate = new Date().toISOString();
      const postDate = post.postedAt || currentDate;
      const scheduleDate = post.postScheduleDate || postDate;
      const expiresDate = post.expiresAt || scheduleDate;

      const formattedPost = {
        id: post.id,
        title: post.title || 'Untitled Draft',
        content: post.content || '',
        date: postDate,
        type: post.postType || 'text',
        media: mediaUrl,
        mediaType: post.postMedias?.[0]?.mediaType,
        scheduledDate: scheduleDate,
        postMedias: post.postMedias?.map((media) => ({
          ...media,
          mediaPath: getCdnUrl(media.mediaPath),
        })) || [],
        postType: post.postType,
        question: post.content,
        options: [],
        allowCustomAnswer: false,
        totalVotes: 0,
        expiresAt: expiresDate,
        postScheduleDate: scheduleDate,
        // Keep original fields for compatibility
        username: '',
        postedAt: postDate,
        isLiked: false,
        likesCount: 0,
        commentsCount: 0,
        repostCount: 0,
        shareCount: 0,
        coverImagePath: post.coverImagePath
          ? getCdnUrl(post.coverImagePath)
          : undefined,
        linkUrl: post.linkUrl,
        contentElement: <HighlightHtml html={post.content || ''} keyword="" />,
      };


      return formattedPost;
    });
  }, [drafts]);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="sm"
      PaperProps={{
        sx: {
          borderRadius: '12px',
          backgroundColor: 'white',
          p: 0,
        },
      }}
    >
      {/* Header */}
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        p={3}
        borderBottom="1px solid #eee"
      >
        <Typography
          variant="h6"
          sx={{
            fontFamily: 'Plus Jakarta Sans',
            fontWeight: 600,
            fontSize: '1.5rem',
            color: '#1E1E1E',
          }}
        >
          Drafts
        </Typography>
        <IconButton onClick={onClose}>
          <Iconify
            icon="mingcute:close-line"
            width={36}
            height={36}
            color="#A24295"
          />
        </IconButton>
      </Box>
      <Box p={3}>
        {isLoading ? (
          <Box display="flex" justifyContent="center" p={4}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Typography color="error" textAlign="center" p={2}>
            Error loading drafts. Please try again.
          </Typography>
        ) : formattedDrafts.length === 0 ? (
          <Typography textAlign="center" color="text.secondary" p={2}>
            No drafts found
          </Typography>
        ) : (
          formattedDrafts.map((post) => (
            <DraftPostCard
              key={post.id}
              post={post}
              onEdit={handleSaveEdit}
              onDelete={handleDeleteClick}
              onReschedule={handlePublish}
              isEditing={editingPostId === post.id}
              onStartEdit={handleStartEdit}
              onCancelEdit={handleCancelEdit}
            />
          ))
        )}
      </Box>
      <ConfirmationDialog
        open={deleteDialogOpen}
        title="Delete Draft"
        message="Are you sure you want to delete this draft? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
      />
    </Dialog>
  );
}
