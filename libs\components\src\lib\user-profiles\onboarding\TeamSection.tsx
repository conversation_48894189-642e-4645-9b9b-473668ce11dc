import { Box, Typography, TextField } from '@mui/material';
import { useState } from 'react';
import InviteDialog from '../../dialogs/InviteDialog';
import { InvitedUserCard } from '../../invite-card/InviteUserCard';
import { InviteCard } from '../../invite-card/InviteCard';
import { FixedSizeList as List } from 'react-window';

const USERS = [
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    profilePic: 'https://randomuser.me/api/portraits/men/1.jpg',
    role: 'Software Engineer',
  },
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    profilePic: 'https://randomuser.me/api/portraits/women/2.jpg',
    role: 'Product Manager',
  },
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    profilePic: 'https://randomuser.me/api/portraits/women/3.jpg',
    role: 'UX Designer',
  },
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    profilePic: 'https://randomuser.me/api/portraits/men/4.jpg',
    role: 'DevOps Engineer',
  },
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    profilePic: 'https://randomuser.me/api/portraits/women/5.jpg',
    role: 'Data Scientist',
  },
];

const ITEM_HEIGHT = 72;

const TeamSection = () => {
  const [inviteDialogOpen, setInviteDialogOpen] = useState(false);
  const [invitedUsers, setInvitedUsers] = useState<typeof USERS>([]);

  const handleInviteClick = (user: (typeof USERS)[number]) => {
    if (!invitedUsers.find((u) => u.name === user.name)) {
      setInvitedUsers((prev) => [...prev, user]);
    }
  };

  const handleDeleteUser = (name: string) => {
    setInvitedUsers((prev) => prev.filter((user) => user.name !== name));
  };

  // Called when user enters email in InviteDialog
  const handleManualInvite = (emails: string[]) => {
    const newUsers = emails.map((email) => ({
      email,
      name: '',
      role: '',
      profilePic: '',
    }));

    setInvitedUsers((prev) => {
      const existingEmails = new Set(prev.map((u) => u.email));
      const filteredNewUsers = newUsers.filter(
        (u) => !existingEmails.has(u.email)
      );
      return [...prev, ...filteredNewUsers];
    });
  };

  const Row = ({
    index,
    style,
  }: {
    index: number;
    style: React.CSSProperties;
  }) => {
    const user = USERS[index];
    return (
      <div style={style}>
        <InviteCard
          name={user.name}
          profilePic={user.profilePic}
          role={user.role}
          onClick={() => handleInviteClick(user)}
        />
      </div>
    );
  };

  return (
    <Box
      maxWidth="976px"
      width="100%"
      mx="auto"
      p={{ xs: '16px', sm: '20px' }}
      borderRadius="8px"
      bgcolor="#fff"
    >
      {/* Section Title */}
      <Typography fontSize="24px" fontWeight={600} mb="20px">
        Team
      </Typography>

      <Typography
        fontSize="16px"
        fontWeight={400}
        mb={{ xs: '20px', sm: '40px' }}
      >
        Highlight the people behind your company. Choose from suggested members,
        search for users, or invite someone new. Then, drag their profile cards
        to arrange them in any order you like.
      </Typography>

      <Box
        display="flex"
        gap={{ xs: '20px', lg: '40px' }}
        flexDirection={{ xs: 'column', md: 'row' }}
      >
        {/* Left Section */}
        <Box
          sx={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          <TextField
            placeholder="Search"
            sx={{
              width: '332px',
              height: '45px',
              mb: '12px',
              '& .MuiInputBase-root': {
                height: '100%',
              },
            }}
          />

          <Box
            width="332px"
            height={{ xs: '237px', sm: '305px' }}
            bgcolor="#F3F4F6"
            borderRadius="12px"
            p="16px"
          >
            <Typography fontSize="16px" fontWeight={400} mb="12px">
              Suggested:
            </Typography>

            {/*  Virtualized User List */}
            <List
              height={200} // or calculate based on available height
              itemCount={USERS.length}
              itemSize={ITEM_HEIGHT}
              width={300}
              style={{ scrollbarWidth: 'none' }}
            >
              {Row}
            </List>
          </Box>

          <Typography
            fontSize="16px"
            fontWeight={500}
            mt="20px"
            mb="20px"
            color="#737678"
          >
            OR
          </Typography>

          <InviteCard
            name="Invite"
            profilePic=""
            onClick={() => setInviteDialogOpen(true)}
          />
        </Box>

        {/* Right Section */}
        <Box
          flex={1}
          bgcolor="white"
          borderRadius="8px"
          display="flex"
          justifyContent={invitedUsers.length ? 'flex-start' : 'center'}
          alignItems={invitedUsers.length ? 'flex-start' : 'center'}
          minHeight="350px"
          border={'1px solid #A3A3A3'}
          p="16px"
        >
          {invitedUsers.length === 0 ? (
            <Box textAlign="center" width="254px">
              <Box
                component="img"
                src="/assets/user-profile/add-user.svg"
                alt="Add"
                sx={{ width: 60, height: 60 }}
              />
              <Typography fontSize="14px" fontWeight={400}>
                Your employees will show up here when you add them
              </Typography>
            </Box>
          ) : (
            <Box
              sx={{ position: 'relative', overflow: 'hidden', width: '100%' }}
            >
              <Box
                display="flex"
                gap="16px"
                sx={{
                  overflowX: { xs: 'auto', md: 'visible' },
                  flexWrap: { xs: 'nowrap', md: 'wrap' },
                  paddingBottom: '8px',
                  scrollBehavior: 'smooth',
                  scrollbarWidth: 'none', // Firefox
                  '&::-webkit-scrollbar': {
                    display: 'none', // Chrome, Safari, Edge
                  },
                }}
              >
                {invitedUsers.map((user) => (
                  <InvitedUserCard
                    key={user.email}
                    name={user.name}
                    email={user.email}
                    profilePic={user.profilePic}
                    role={user.role}
                    onDelete={() => handleDeleteUser(user.name)}
                  />
                ))}
              </Box>

              {/* Gradient fade effect on the right */}
              <Box
                sx={{
                  position: 'absolute',
                  right: 0,
                  top: 0,
                  width: '10%',
                  height: '100%',
                  background:
                    'linear-gradient(to left, white 10%, transparent)',
                  pointerEvents: 'none',
                  display: { xs: 'block', md: 'none' }, // Only show on mobile
                }}
              />
            </Box>
          )}
        </Box>
      </Box>
      <InviteDialog
        open={inviteDialogOpen}
        onClose={() => setInviteDialogOpen(false)}
        onInvite={handleManualInvite}
      />
    </Box>
  );
};

export default TeamSection;
