import { initializeApp, FirebaseApp } from 'firebase/app';
import { getAuth, Auth } from 'firebase/auth';

const isTestEnv = process.env.NODE_ENV === 'test' || process.env.CI === 'true';

// Firebase configuration
const firebaseConfig = {
  apiKey:
    process.env.NEXT_PUBLIC_FIREBASE_API_KEY || 'placeholder-during-build',
  authDomain:
    process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || 'placeholder-during-build',
  projectId:
    process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || 'placeholder-during-build',
  storageBucket:
    process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET ||
    'placeholder-during-build',
  messagingSenderId:
    process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID ||
    'placeholder-during-build',
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID || 'placeholder-during-build',
  measurementId:
    process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID ||
    'placeholder-during-build',
};

// Initialize Firebase only if not in test environment
let app: FirebaseApp | undefined;
let auth: Auth | undefined;

try {
  // Skip initialization in test environments
  if (!isTestEnv) {
    app = initializeApp(firebaseConfig);
    auth = getAuth(app);
  } else {
    console.log('Skipping Firebase initialization in test environment');
  }
} catch (error) {
  console.error('Firebase initialization error:', error);
}

export { app, auth };
