'use client';

import { NextIntlClientProvider, AbstractIntlMessages } from 'next-intl';
import { ReactNode } from 'react';
import { plusJakartaSans } from '../fonts';
import { getCdnUrl } from '@minicardiac-client/utilities';
import { ThemeProvider, CssBaseline } from '@mui/material';
import { ToastContainer } from 'react-toastify';
import { ClientOnly, theme } from '@minicardiac-client/shared';
import {
  CarouselProvider,
  SnackbarProvider,
} from '@minicardiac-client/components';
import NextQueryProvider from '../../providers/next-query-provider';
import { AuthProvider } from '@minicardiac-client/apis';
import { LoadingProvider } from '../../providers/loading-provider';

import '../global.css';
import 'react-toastify/dist/ReactToastify.css';
import { HelmetProvider } from 'react-helmet-async';

type Props = {
  children: ReactNode;
  locale: string;
  messages: AbstractIntlMessages;
};

function ThemeRegistry({ children }: { children: ReactNode }) {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      {children}
      <ToastContainer />
    </ThemeProvider>
  );
}

export default function LocaleLayout({ children, locale, messages }: Props) {
  return (
    <html
      lang={locale}
      className={`${plusJakartaSans.variable} ${plusJakartaSans.className}`}
    >
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="description" content="MiniCardiac Application" />
        <link
          rel="icon"
          href={getCdnUrl('/assets/icons/minicardiac-icon.svg')}
        />
        <link
          href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@300;400;500;600;700;800&display=swap"
          rel="stylesheet"
        />
      </head>
      <body className={plusJakartaSans.variable}>
        <HelmetProvider>
          <NextIntlClientProvider locale={locale} messages={messages}>
            <ClientOnly>
              <NextQueryProvider>
                <SnackbarProvider>
                  <AuthProvider>
                    <LoadingProvider>
                      <CarouselProvider>
                        <ThemeRegistry>{children}</ThemeRegistry>
                      </CarouselProvider>
                    </LoadingProvider>
                  </AuthProvider>
                </SnackbarProvider>
              </NextQueryProvider>
            </ClientOnly>
          </NextIntlClientProvider>
        </HelmetProvider>
      </body>
    </html>
  );
}
