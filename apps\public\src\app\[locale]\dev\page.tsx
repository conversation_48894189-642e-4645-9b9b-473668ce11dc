'use client';

import { useState, useEffect } from 'react';
import { Box, Button } from '@mui/material';
import { SubscriptionComponentsPage as SubscriptionComponentsPageComponent } from '@minicardiac-client/components';
import { useRouter } from '@/apps/public/src/i18n/navigation';

// This page is intentionally made public by bypassing authentication checks
export default function DevPage() {
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>(
    'monthly'
  );
  const router = useRouter();

  // Prevent any redirects that might be happening
  useEffect(() => {
    router.replace('/dev');
  }, [router]);

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4, mb: 2 }}>
        <Box
          sx={(theme) => ({
            display: 'flex',
            border: '0.5px solid',
            borderColor: theme.palette.secondary.main,
            borderRadius: '8px',
            width: 142,
            height: 44,
            padding: '4px 5px',
            gap: '4px',
            backgroundColor: '#FFFFFF',
            overflow: 'hidden',
          })}
        >
          <Button
            onClick={() => setBillingCycle('monthly')}
            sx={(theme) => ({
              width: 68,
              height: 36,
              minWidth: 68,
              borderRadius: '8px',
              backgroundColor:
                billingCycle === 'monthly' ? '#E3C6DFBF' : 'transparent',
              color:
                billingCycle === 'monthly'
                  ? theme.palette.secondary.main
                  : '#1E1E1E',
              fontFamily: "'Plus Jakarta Sans', sans-serif",
              fontWeight: billingCycle === 'monthly' ? 600 : 300,
              fontSize: '14px',
              textTransform: 'none',
              padding: '0 6px',
              '&:hover': {
                backgroundColor:
                  billingCycle === 'monthly'
                    ? '#E3C6DFBF'
                    : 'rgba(0, 0, 0, 0.04)',
              },
            })}
          >
            Monthly
          </Button>
          <Button
            onClick={() => setBillingCycle('yearly')}
            sx={(theme) => ({
              width: 68,
              height: 36,
              minWidth: 68,
              borderRadius: '8px',
              backgroundColor:
                billingCycle === 'yearly' ? '#E3C6DFBF' : 'transparent',
              color:
                billingCycle === 'yearly'
                  ? theme.palette.secondary.main
                  : '#1E1E1E',
              fontFamily: "'Plus Jakarta Sans', sans-serif",
              fontWeight: billingCycle === 'yearly' ? 600 : 300,
              fontSize: '14px',
              textTransform: 'none',
              padding: '0 6px',
              '&:hover': {
                backgroundColor:
                  billingCycle === 'yearly'
                    ? '#E3C6DFBF'
                    : 'rgba(0, 0, 0, 0.04)',
              },
            })}
          >
            Yearly
          </Button>
        </Box>
      </Box>

      <SubscriptionComponentsPageComponent billingCycle={billingCycle} />
    </Box>
  );
}
