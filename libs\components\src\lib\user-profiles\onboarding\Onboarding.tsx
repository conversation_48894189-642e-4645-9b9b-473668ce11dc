import { Box, Typography, useMediaQuery } from '@mui/material';
import { QualificationSection } from './QualificationSection';
import { WorkExperienceSection } from './WorkExperienceSection';
import { ClinicalInterestsSection } from './ClinicalInterestsSection';
import { AreasOfExpertiseSection } from './AreasOfExpertiseSection';
import { FAQSection } from './FAQSection';
import { AboutSection } from './AboutSection';
import { GetInTouchSection } from './GetInTouchSection';
import TeamSection from './TeamSection';
import ShowcaseSection from './ShowcaseSection';
import CustomSection from './CustomSection';
import FundraiserSection from './FundraiserSection';
import { OnboardingMobileProfileHeader } from './OnboardingMobileProfileHeader';
import { OnboardingProfileHeader } from './OnboardingProfileHeader';
import { useTheme } from '@emotion/react';
import { useState } from 'react';
import { Profile } from '@minicardiac-client/types';
import ProfileSkeletonUI from '../../skeleton-ui/ProfileSkeletonUI';

// use or modify this constant data till api integration
const PROFILE_DATA = {
  name: 'Vanya Goel',
  college: 'St. Andrews Medical School, Copenhagen',
  profilePic: '/assets/user-profile/profile-image.jpg',
  backgroundImage: '/assets/user-profile/profile-header.jpg',
  connections: 576,
  degrees: 'MD, CCT, PhD',
  designation: 'Head of Cardiology',
  workplace: 'St. Andrews Hospital, Copenhagen',
  rating: 0,
  followers: 3565,
  intro:
    'Dedicated and driven medical student with a passion for patient care, research, and advancing medical knowledge.',
  video: '/assets/user-profile/sample-video.mp4',
  awards: [
    {
      title: 'HSJ Award',
      description: 'For Excellence in Patient Care | 2016',
      recipient: 'Recipient of the',
      icon: '/assets/user-profile/award-1.svg',
    },
    {
      title: 'National Scholar Award',
      description: 'For Academic Excellence | 2015',
      recipient: 'Recipient of the',
      icon: '/assets/user-profile/award-2.svg',
    },
  ],
  about: `I’m Dr. Simmons, a board-certified cardiologist with a passion for preventing heart disease before it starts. While treating cardiovascular conditions is a vital part of my work, my true focus is helping patients take proactive steps to protect their heart health and overall well-being.

With 15 years of experience in cardiology, I’ve seen firsthand how lifestyle choices, early detection, and personalized care can dramatically reduce the risk of heart disease. My approach is built on evidence-based medicine, cutting-edge diagnostics, and a deep commitment to patient education. Whether it’s optimizing nutrition, designing heart-healthy exercise plans, or managing risk factors like high blood pressure and cholesterol, I work closely with my patients to create sustainable, effective strategies for lifelong health.`,
};

export const Onboarding = () => {
  const theme: any = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const [profile] = useState<Profile>(PROFILE_DATA);

  if (!profile) return <ProfileSkeletonUI />;

  const AddSection = () => (
    <Typography
      sx={{
        fontWeight: 600,
        fontSize: '16px',
        color: '#A24295',
        textAlign: 'center',
      }}
    >
      + Add Section
    </Typography>
  );

  return (
    <Box
      sx={{
        mt: { xs: '0px', sm: '40px' },
        display: 'flex',
        flexDirection: 'column',
        gap: '20px',
      }}
    >
      {isMobile ? (
        <OnboardingMobileProfileHeader profile={profile} />
      ) : (
        <OnboardingProfileHeader profile={profile} />
      )}

      <Box display="flex" p={{ xs: '16px', sm: '0px' }}>
        <AboutSection />
      </Box>

      <AddSection />

      <Box display="flex" p={{ xs: '16px', sm: '0px' }}>
        <QualificationSection />
      </Box>

      <AddSection />

      <Box display="flex" p={{ xs: '16px', sm: '0px' }}>
        <WorkExperienceSection />
      </Box>

      <AddSection />

      <Box display="flex" p={{ xs: '16px', sm: '0px' }}>
        <ClinicalInterestsSection />
      </Box>

      <AddSection />

      <Box display="flex" p={{ xs: '16px', sm: '0px' }}>
        <AreasOfExpertiseSection />
      </Box>

      <AddSection />

      <Box display="flex" p={{ xs: '16px', sm: '0px' }}>
        <FAQSection />
      </Box>

      <AddSection />

      <Box display="flex" p={{ xs: '16px', sm: '0px' }}>
        <TeamSection />
      </Box>

      <AddSection />

      <Box display="flex" p={{ xs: '16px', sm: '0px' }}>
        <ShowcaseSection />
      </Box>

      <AddSection />

      <Box display="flex" p={{ xs: '16px', sm: '0px' }}>
        <FundraiserSection />
      </Box>

      <AddSection />

      <Box display="flex" p={{ xs: '16px', sm: '0px' }}>
        <CustomSection />
      </Box>

      <AddSection />

      <Box display="flex" p={{ xs: '16px', sm: '0px' }}>
        <GetInTouchSection />
      </Box>
    </Box>
  );
};
