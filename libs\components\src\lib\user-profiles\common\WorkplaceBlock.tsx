'use client';

import { Box, Typography } from '@mui/material';
import BusinessCenterIcon from '@mui/icons-material/BusinessCenter';

interface Position {
  designation: string;
  duration: string;
}

interface WorkplaceBlockProps {
  workplace: string;
  companyLogo: string;
  positions: Position[];
}

export default function WorkplaceBlock({
  workplace,
  companyLogo,
  positions,
}: WorkplaceBlockProps) {
  const showLine = positions.length > 1;

  return (
    <Box display="flex" gap="16px">
      {/* Logo and vertical line */}
      <Box display="flex" flexDirection="column" alignItems="center">
        {/* Logo */}
        <Box
          width="56px"
          height="56px"
          borderRadius="50%"
          bgcolor="#EDEDED"
          display="flex"
          alignItems="center"
          justifyContent="center"
        >
          {companyLogo ? (
            <img
              src={companyLogo}
              alt={`Logo of ${workplace}`}
              width="40"
              height="40"
              style={{ borderRadius: '50%' }}
            />
          ) : (
            <BusinessCenterIcon sx={{ fontSize: 28, color: '#A24295' }} />
          )}
        </Box>

        {/* Vertical line: only if more than 1 position */}
        {showLine && (
          <Box
            width="0.5px"
            height={`${(positions.length - 1) * 104}px`}
            bgcolor="#A3A3A3"
            mt="8px"
          />
        )}
      </Box>

      {/* Right section: workplace + positions */}
      <Box
        display="flex"
        flexDirection="column"
        gap={{ xs: '20px', sm: '40px' }}
      >
        {/* Workplace Header */}
        <Typography fontSize={{ xs: '16px', sm: '20px' }} fontWeight={600}>
          {workplace}
        </Typography>

        {/* Positions */}
        {positions.map((pos, idx) => (
          <Box key={idx} display="flex" flexDirection="column" gap="4px">
            <Typography fontSize="16px" fontWeight={500}>
              {pos.designation}
            </Typography>
            <Typography fontSize="16px" fontWeight={400} color="neutral[600]">
              {pos.duration}
            </Typography>
          </Box>
        ))}
      </Box>
    </Box>
  );
}
