import { Box, Typography } from '@mui/material';
import CustomForm from './CustomForm';

export const FAQSection = () => {
  const formData = [
    {
      subtitles:
        'The questions you add here will be displayed on your page as a bulleted list with expandable answers.',
      rows: [
        {
          columns: 1,
          fields: [
            {
              label: 'Question',
              placeholder: 'Type a question here',
              sx: { maxWidth: '840px', width: '100%' },
            },
          ],
        },
        {
          columns: 1,
          fields: [
            {
              label: 'Answer',
              placeholder: 'Type your answer here',
              sx: { maxWidth: '840px', width: '100%' },
            },
          ],
        },
      ],
      addButtonLabel: 'Question',
    },
  ];

  return (
    <Box
      maxWidth="976px"
      width="100%"
      mx="auto"
      p={{ xs: '16px', sm: '20px' }}
      borderRadius="8px"
      bgcolor="#fff"
    >
      <Typography fontSize="24px" fontWeight={600} mb="20px">
        FAQ (Frequently Asked Questions)
      </Typography>

      {formData.map((form, idx) => (
        <CustomForm
          key={idx}
          subtitles={form.subtitles}
          rows={form.rows}
          addButtonLabel={`Add ${form.addButtonLabel}`}
        />
      ))}
    </Box>
  );
};
