import { create } from 'zustand';
import { FeedPost } from '@minicardiac-client/apis';

interface ScheduledPostState {
  posts: FeedPost[];
  setPosts: (posts: FeedPost[]) => void;
  updatePost: (postId: string, updatedPost: Partial<FeedPost>) => void;
  removePost: (postId: string) => void;
}

export const useScheduledPostStore = create<ScheduledPostState>((set) => ({
  posts: [],
  setPosts: (posts) => set({ posts }),
  updatePost: (postId, updatedPost) =>
    set((state) => ({
      posts: state.posts.map((post) =>
        post.id === postId ? { ...post, ...updatedPost } : post
      ),
    })),
  removePost: (postId: string) =>
    set((state) => ({
      posts: state.posts.filter((post) => post.id !== postId),
    })),
}));
