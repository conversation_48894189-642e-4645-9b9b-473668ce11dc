import { ReactElement, cloneElement, useState } from 'react';
import DraggablePost from '../content-posts/DraggablePost';

type ConditionalDraggablePostProps = {
  postId: string;
  folderFilter: boolean;
  children: ReactElement;
  type: 'question' | 'poll' | 'article' | 'media' | 'text' | 'link';
};

const ConditionalDraggablePost = ({
  postId,
  folderFilter,
  children,
  type,
}: ConditionalDraggablePostProps) => {
  const [dragEnabled, setDragEnabled] = useState(false);

  const handleDoubleClick = (e: React.MouseEvent) => {
    const target = e.currentTarget;

    setDragEnabled(true);

    setTimeout(() => {
      const event = new PointerEvent('pointerdown', {
        bubbles: true,
        cancelable: true,
      });
      target.dispatchEvent(event);
    }, 0);

    // Optional: Disable after 5 seconds
    setTimeout(() => {
      setDragEnabled(false);
    }, 5000);
  };

  if (!folderFilter) {
    return cloneElement(children, {
      onDoubleClick: handleDoubleClick,
      style: {
        cursor: 'default',
        ...children.props.style,
      },
    });
  }

  return (
    <DraggablePost postId={postId} type={type} disabled={!dragEnabled}>
      {({ setNodeRef, listeners, attributes }) =>
        cloneElement(children, {
          ref: setNodeRef,
          onDoubleClick: handleDoubleClick,
          ...(dragEnabled ? listeners : {}),
          ...(dragEnabled ? attributes : {}),
          style: {
            cursor: dragEnabled ? 'grab' : 'default',
            ...children.props.style,
          },
        })
      }
    </DraggablePost>
  );
};

export default ConditionalDraggablePost;
