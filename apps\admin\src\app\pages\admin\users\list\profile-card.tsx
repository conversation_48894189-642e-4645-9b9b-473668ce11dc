// @mui
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';

import { Image } from '@minicardiac-client/components';
import { User } from '@minicardiac-client/types';

interface ProfileCardDetailsProps {
  user: User;
}

export default function ProfileCard({ user }: ProfileCardDetailsProps) {
  const { name } = user;

  const renderImg = (
    <Box
      sx={{
        position: 'relative',
      }}
    >
      <Image
        alt={`${name}-profile-image`}
        src={'/profilepic.png'}
        sx={{
          objectFit: 'cover',
          width: {
            xs: '100%',
            md: 300,
          },
          height: 300,
          overflow: 'hidden',
        }}
      />
    </Box>
  );

  return <Card>{renderImg}</Card>;
}
