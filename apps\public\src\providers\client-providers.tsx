'use client';

import 'react-toastify/dist/ReactToastify.css';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { theme, ClientOnly } from '@minicardiac-client/shared';
import { AuthProvider } from './auth-provider';

import {
  CarouselProvider,
  SnackbarProvider,
} from '@minicardiac-client/components';
import { LoadingProvider } from './loading-provider';
import NextQueryProvider from './next-query-provider';
import { ToastContainer } from 'react-toastify';

function ThemeRegistry({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      {children}
      <ToastContainer />
    </ThemeProvider>
  );
}

export function ClientProviders({ children }: { children: React.ReactNode }) {
  return (
    <ClientOnly>
      <NextQueryProvider>
        <SnackbarProvider>
          <AuthProvider>
            <LoadingProvider>
              <CarouselProvider>
                <ThemeRegistry>{children}</ThemeRegistry>
              </CarouselProvider>
            </LoadingProvider>
          </AuthProvider>
        </SnackbarProvider>
      </NextQueryProvider>
    </ClientOnly>
  );
}
