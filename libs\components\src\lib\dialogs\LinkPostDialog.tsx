'use client';
import { useState, useCallback } from 'react';
import {
  Box,
  Typography,
  TextField,
  Stack,
  Menu,
  MenuItem,
  useMediaQuery,
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { toast } from 'react-toastify';

// Placeholder for your custom/composable UI pieces:
import CustomDialog from './CustomDialog';
import CustomToggleButtonGroup from '../buttons/CustomToggleButtonGroup';
import { LoadingButton } from '../loading-button';
import { Iconify } from '../iconify';
import { PostButton } from '../buttons/PostButton';
import { BackButton } from '../buttons/Backbutton';
import { useCreateLinkPost } from '@minicardiac-client/apis';
import { useLinkPreview, LinkPreview } from '../hooks/useLinkPreview';

interface LinkPostDialogProps {
  open: boolean;
  onClose: () => void;
}

const LinkPostDialog = ({ open, onClose }: LinkPostDialogProps) => {
  const [link, setLink] = useState('');
  const [caption, setCaption] = useState('');
  const [tags, setTags] = useState('');
  const [audience, setAudience] = useState<'PROFESSIONAL' | 'PUBLIC' | 'BOTH'>(
    'PROFESSIONAL'
  );
  const [community, setCommunity] = useState<
    'CARDIAC_SURGEON' | 'CARDIOLOGIST' | 'BOTH'
  >('CARDIAC_SURGEON');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  // Link preview state
  const [linkPreview, setLinkPreview] = useState<LinkPreview | null>(null);
  const { fetchLinkPreview, isLoading: isLoadingPreview } = useLinkPreview();

  const theme = useTheme();
  const screenBelowSM = useMediaQuery(theme.breakpoints.down('sm'));
  const openMenu = Boolean(anchorEl);

  // Real function to fetch link preview using open-graph-scraper
  const generateLinkPreview = async (url: string) => {
    if (!url.trim()) {
      setLinkPreview(null);
      return;
    }

    const preview = await fetchLinkPreview(url.trim());
    setLinkPreview(preview);
  };

  // Update link and generate preview
  const handleLinkChange = (value: string) => {
    setLink(value);
    if (value.trim()) {
      generateLinkPreview(value.trim());
    } else {
      setLinkPreview(null);
    }
  };

  // API integration
  const createLinkPost = useCreateLinkPost({
    onSuccess: () => {
      toast.success('Link post created successfully!');
      // Reset form
      setLink('');
      setCaption('');
      setTags('');
      setAudience('PROFESSIONAL');
      setCommunity('CARDIAC_SURGEON');
      setLinkPreview(null);
      onClose();
    },
    onError: (error) => {
      toast.error(error.message || 'Failed to create link post');
    },
  });

  // Handle form submission
  const handlePost = useCallback(() => {
    if (!link.trim() || !caption.trim()) {
      toast.error('Please fill in both link and content fields');
      return;
    }

    const payload = {
      link: link.trim(),
      content: caption.trim(),
      tags: tags
        .split(',')
        .map((tag: string) => tag.trim())
        .filter(Boolean),
      community: audience, // Using audience for community field
      audience: community, // Using community for audience field (professional level)
      postStatus: 'published' as const,
    };

    createLinkPost.mutate(payload);
  }, [link, caption, tags, audience, community, createLinkPost]);

  return (
    <CustomDialog
      open={open}
      onClose={onClose}
      title=""
      sx={{
        p: 0,
        px: { xs: 0, sm: theme.spacing(10) },
        pt: { xs: 0, sm: theme.spacing(6.25) },
        alignItems: { xs: 'stretch', sm: 'start' },
        '.MuiDialog-paper': {
          maxHeight: { xs: '100%', sm: 'calc(100% - 64px)' },
        },
      }}
    >
      <Box
        display="flex"
        flexDirection="column"
        padding={{ xs: theme.spacing(2), sm: theme.spacing(5) }}
        width="100%"
        sx={{ backgroundColor: 'background.default' }}
      >
        {/* Header */}
        <Box
          position="relative"
          display="flex"
          justifyContent={{ xs: 'space-between', sm: 'center' }}
          alignItems="center"
          height="35px"
          mb={theme.spacing(2.5)}
        >
          <Box
            sx={{
              display: 'flex',
              gap: theme.spacing(2),
              alignItems: 'center',
            }}
          >
            {screenBelowSM && <BackButton onClick={onClose} />}
            <Typography
              sx={{
                fontFamily: theme.typography.fontFamily,
                fontWeight: 500,
                fontSize: { xs: '20px', sm: '28px' },
                color: 'text.primary',
              }}
            >
              New Link Post
            </Typography>
          </Box>
          <Typography
            sx={{
              position: 'absolute',
              right: 0,
              top: 0,
              fontFamily: theme.typography.fontFamily,
              fontWeight: 600,
              fontSize: '16px',
              color: 'secondary.main',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
            }}
          >
            Drafts <Iconify icon="solar:arrow-right-linear" />
          </Typography>
        </Box>

        {/* Content */}
        <Box
          display={'flex'}
          flexDirection={{ xs: 'column', md: 'row' }}
          gap={theme.spacing(1.5)}
        >
          {/* Link Preview */}
          <Box
            width={{ xs: '100%', md: '50%' }}
            height={'300px'}
            border={'1px solid'}
            borderColor="divider"
            borderRadius={theme.spacing(1.5)}
            display="flex"
            sx={{ bgcolor: 'neutral.100', overflow: 'hidden' }}
          >
            {!link ? (
              <Box
                display="flex"
                flexDirection="column"
                alignItems="center"
                justifyContent="center"
                width="100%"
                height="300px"
              >
                <Iconify icon="ph:image-light" width={40} height={40} />
                <Typography
                  variant="body2"
                  color="text.secondary"
                  mt={theme.spacing(2.5)}
                  align="center"
                >
                  You can see the preview to your link <br /> here once you
                  enter it.
                </Typography>
              </Box>
            ) : isLoadingPreview ? (
              <Box
                display="flex"
                alignItems="center"
                justifyContent="center"
                width="100%"
                height="300px"
              >
                <Typography variant="body2" color="text.secondary">
                  Loading preview...
                </Typography>
              </Box>
            ) : linkPreview ? (
              <Box display="flex" width="100%" height="300px">
                {/* Preview Image */}
                <Box
                  width="240px"
                  height="100%"
                  sx={{
                    backgroundImage: `url(${
                      linkPreview.image ||
                      'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=240&h=300&fit=crop'
                    })`,
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                    backgroundColor: 'neutral.200',
                  }}
                />
                {/* Preview Content */}
                <Box
                  flex={1}
                  p={theme.spacing(2.5)}
                  display="flex"
                  flexDirection="column"
                  justifyContent="space-between"
                  sx={{ backgroundColor: 'background.default' }}
                >
                  {/* Domain/Source */}
                  <Box
                    display="flex"
                    alignItems="center"
                    gap={theme.spacing(1)}
                    mb={theme.spacing(1.5)}
                  >
                    <Iconify
                      icon="mdi:web"
                      width={16}
                      height={16}
                      color={theme.palette.neutral[600]}
                    />
                    <Typography
                      variant="caption"
                      color="text.secondary"
                      sx={{ fontSize: '12px' }}
                    >
                      {linkPreview.domain}
                    </Typography>
                  </Box>
                  {/* Title */}
                  <Typography
                    variant="subtitle1"
                    sx={{
                      fontWeight: 600,
                      fontSize: '16px',
                      lineHeight: '22px',
                      mb: theme.spacing(1.5),
                      display: '-webkit-box',
                      WebkitLineClamp: 3,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                    }}
                  >
                    {linkPreview.title}
                  </Typography>
                  {/* Description */}
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{
                      fontSize: '14px',
                      lineHeight: '20px',
                      display: '-webkit-box',
                      WebkitLineClamp: 4,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                      flex: 1,
                    }}
                  >
                    {linkPreview.description}
                  </Typography>
                </Box>
              </Box>
            ) : (
              <Box
                display="flex"
                alignItems="center"
                justifyContent="center"
                width="100%"
                height="300px"
              >
                <Typography variant="body2" color="text.secondary">
                  Unable to generate preview
                </Typography>
              </Box>
            )}
          </Box>

          {/* Inputs (Link, Content) */}
          <Box
            width={{ xs: '100%', md: '50%' }}
            display="flex"
            flexDirection="column"
            gap={theme.spacing(2)}
          >
            <TextField
              label="Link"
              placeholder="Drop the link you would like to share here!"
              fullWidth
              value={link}
              onChange={(e) => handleLinkChange(e.target.value)}
            />
            <TextField
              label="Caption"
              placeholder="Add Caption for your link"
              fullWidth
              multiline
              value={caption}
              onChange={(e) => setCaption(e.target.value)}
              sx={{
                '& .MuiInputBase-root': {
                  height: '200px',
                  alignItems: 'flex-start',
                },
                '& .MuiInputBase-input': {
                  height: '200px !important',
                  overflow: 'auto !important',
                },
              }}
            />
          </Box>
        </Box>

        {/* Tags, Toggles */}
        <Box
          display="flex"
          flexDirection={{ xs: 'column', md: 'row' }}
          gap={{ xs: theme.spacing(1.25), lg: theme.spacing(5) }}
          alignItems={{ xs: 'center', md: 'end' }}
          mt={0}
          mb={{ xs: theme.spacing(10), sm: 0 }}
        >
          <TextField
            placeholder="#Surgery #Valve"
            value={tags}
            onChange={(e) => setTags(e.target.value)}
            fullWidth
            label="Tags"
            InputLabelProps={{ shrink: true }}
          />
          <Box width={{ xs: '100%', md: '224px' }}>
            <CustomToggleButtonGroup
              label="Audience"
              options={['PROFESSIONAL', 'PUBLIC', 'BOTH']}
              selected={audience}
              onChange={(v) => setAudience(v)}
              width={{ xs: '100%', md: '224px' }}
            />
          </Box>
          <Box width={{ xs: '100%', md: '282px' }}>
            <CustomToggleButtonGroup
              label="Professional Community"
              options={['Cardiac Surgery', 'Cardiology', 'Both']}
              selected={
                community === 'CARDIAC_SURGEON'
                  ? 'Cardiac Surgery'
                  : community === 'CARDIOLOGIST'
                  ? 'Cardiology'
                  : 'Both'
              }
              onChange={(v) =>
                setCommunity(
                  v === 'Cardiac Surgery'
                    ? 'CARDIAC_SURGEON'
                    : v === 'Cardiology'
                    ? 'CARDIOLOGIST'
                    : 'BOTH'
                )
              }
              width={{ xs: '100%', md: '282px' }}
            />
          </Box>
        </Box>

        {/* Footer */}
        <Stack
          direction={{ xs: 'column', sm: 'row' }}
          spacing={{ xs: theme.spacing(1.25), sm: theme.spacing(2.5) }}
          justifyContent="center"
          sx={{
            position: { xs: 'fixed', sm: 'static' },
            bottom: 0,
            left: 0,
            mt: { xs: 0, sm: theme.spacing(5) },
            width: { xs: '100%', sm: 'auto' },
            background: { xs: 'background.default', sm: 'transparent' },
            padding: { xs: theme.spacing(2.5), sm: 0 },
            boxShadow: { xs: '0 -4px 20px 0 rgba(0,0,0,0.1)', sm: 'none' },
          }}
        >
          <LoadingButton
            variant="outlined"
            onClick={onClose}
            sx={{
              width: { xs: '156px' },
              height: '40px',
              backgroundColor: 'background.default',
              border: { xs: 'none', sm: '1px solid' },
              borderColor: 'secondary.main',
              color: 'secondary.main',
              fontSize: '16px',
              fontWeight: 700,
              '&:hover': { backgroundColor: 'secondary.light' },
            }}
          >
            Cancel
          </LoadingButton>
          <PostButton
            setAnchorEl={setAnchorEl}
            handlePost={handlePost}
            disabled={
              !link.trim() || !caption.trim() || createLinkPost.isPending
            }
            isOpen={openMenu}
          />
          <Menu
            anchorEl={anchorEl}
            open={openMenu}
            onClose={() => setAnchorEl(null)}
          >
            <MenuItem onClick={handlePost}>Post now</MenuItem>
            <MenuItem onClick={() => setAnchorEl(null)}>Save as draft</MenuItem>
          </Menu>
        </Stack>
      </Box>
    </CustomDialog>
  );
};

export default LinkPostDialog;
