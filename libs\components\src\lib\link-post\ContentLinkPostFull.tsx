'use client';

import { Box,Button } from '@mui/material';
import { Icon } from '@iconify/react';
import { LinkPreviewCard } from '../content-posts/link-post/LinkPreviewCard';

interface ContentLinkPostFullProps {
  content: string;
  linkUrl?: string;
  linkPreview?: {
    title: string;
    description: string;
    image: string;
    domain: string;
  };
}

export const ContentLinkPostFull = ({
  content,
  linkUrl,
  linkPreview,
}: ContentLinkPostFullProps) => {
  const handleVisitLink = () => {
    if (linkUrl) {
      window.open(linkUrl, '_blank', 'noopener,noreferrer');
    }
  };

  return (
    <Box display="flex" flexDirection="column" alignItems="center" gap={3}>
      {/* Link Preview */}
      <Box
        width="100%"
        maxWidth="824px"
        display="flex"
        justifyContent="center"
        p="20px"
        bgcolor="#f8f9fa"
        borderRadius="12px"
      >
        <Box display="flex" flexDirection="column" alignItems="center" gap={3}>
          <LinkPreviewCard
            linkUrl={linkUrl}
            linkPreview={linkPreview}
            onClick={handleVisitLink}
            size="large"
          />
          
          <Button
            onClick={handleVisitLink}
            variant="contained"
            startIcon={<Icon icon="mdi:external-link" />}
            sx={{
              backgroundColor: '#A24295',
              color: '#fff',
              borderRadius: '8px',
              padding: '12px 24px',
              fontSize: '14px',
              fontWeight: 600,
              textTransform: 'none',
              '&:hover': {
                backgroundColor: '#8A2C80',
                transform: 'translateY(-1px)',
              },
            }}
          >
            Visit Link
          </Button>
        </Box>
      </Box>

      {/* Content */}
      {content && (
        <Box
          width="100%"
          maxWidth="824px"
          sx={{
            fontSize: '16px',
            lineHeight: '1.6',
            color: '#1E1E1E',
            p: '20px',
            backgroundColor: '#fff',
            borderRadius: '12px',
            boxShadow: '0px 1px 6px rgba(0, 0, 0, 0.05)',
          }}
          dangerouslySetInnerHTML={{ __html: content }}
        />
      )}
    </Box>
  );
};
