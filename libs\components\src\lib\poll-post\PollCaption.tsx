'use client';

import { Box, Typography } from '@mui/material';
import { useRouter } from 'next/navigation';
import { FullPageLoader } from '../full-page-loader';
import { useEffect, useRef, useState } from 'react';
import { useTranslations } from 'next-intl';
import { generateSlug } from '@minicardiac-client/utilities';

const MAX_LINES = 2;
const LINE_HEIGHT_PX = 18;

interface PollCaptionProps {
  POLL_CAPTION: string;
  postId?: string;
}

export const PollCaption = ({ POLL_CAPTION, postId }: PollCaptionProps) => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [isOverflowing, setIsOverflowing] = useState(false);
  const captionRef = useRef<HTMLDivElement>(null);
  const t = useTranslations('pollPost');

  useEffect(() => {
    if (captionRef.current) {
      const el = captionRef.current;
      const totalHeight = el.scrollHeight;
      const maxHeight = LINE_HEIGHT_PX * MAX_LINES;
      setIsOverflowing(totalHeight > maxHeight);
    }
  }, [POLL_CAPTION]);

  const handleRedirect = () => {
    const slug = generateSlug(POLL_CAPTION);
    setLoading(true);
    router.push(`/feed/poll/${postId}/${slug}`);
  };

  return (
    <>
      <FullPageLoader
        open={loading}
        message={t('loadingPoll')}
        sx={{ backgroundColor: '#1E1E1E40' }}
      />

      <Box sx={{ position: 'relative', width: '100%' }}>
        <Typography
          ref={captionRef}
          sx={{
            fontSize: '12px',
            lineHeight: `${LINE_HEIGHT_PX}px`,
            fontWeight: 400,
            color: '#1E1E1E',
            display: '-webkit-box',
            WebkitLineClamp: MAX_LINES,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            userSelect: 'text',
            wordBreak: 'break-word',
          }}
          onClick={handleRedirect}
        >
          {POLL_CAPTION}
        </Typography>

        {isOverflowing && (
          <Typography
            sx={{
              mt: '4px',
              fontSize: '12px',
              lineHeight: '18px',
              fontWeight: 600,
              color: '#A24295',
              cursor: 'pointer',
              userSelect: 'none',
              display: 'inline-block',
            }}
            onClick={handleRedirect}
          >
            {t('seeMore')}
          </Typography>
        )}
      </Box>
    </>
  );
};
