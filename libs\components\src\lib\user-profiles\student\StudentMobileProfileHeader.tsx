import { Avatar, Box, Typography } from '@mui/material';
import ProfileHeaderMobile from '../common/ProfileHeaderMobile';
import { Profile } from '@minicardiac-client/types';
import ActionButtons from '../common/ActionButtons';

export const StudentMobileProfileHeader = ({
  profile,
}: {
  profile: Profile;
}) => {
  return (
    <ProfileHeaderMobile
      profile={profile}
      ConnectUserComponent={MobileConnectButtons}
      ProfileConnectionsComponent={MobileProfileInfo}
    />
  );
};

const MobileConnectButtons = () => {
  return <ActionButtons accountType="student" />;
};

const MobileProfileInfo = ({ profile }: { profile: Profile }) => {
  return (
    <>
      <Avatar
        src={profile.profilePic}
        sx={{
          width: 121,
          height: 121,
          border: '4px solid white',
          position: 'absolute',
          bottom: -60,
          left: 16,
        }}
      />

      <Box
        sx={{
          position: 'absolute',
          left: 150,
          bottom: -50,
          height: '40px',
          display: 'flex',
          alignItems: 'center',
          gap: 2,
        }}
      >
        {/* Connections */}
        <Box sx={{ textAlign: 'center' }}>
          <Typography fontSize={16} fontWeight={600}>
            {profile.connections}
          </Typography>
          <Typography fontSize={12} fontWeight={400}>
            Connections
          </Typography>
        </Box>
      </Box>
    </>
  );
};
