'use client';

import React, { useState } from 'react';
import {
  Box,
  Button,
  IconButton,
  Menu,
  MenuItem,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import { Iconify } from '../../iconify';
import { ColoredButton } from '../../buttons/ColoredButton';
import { OutlinedButton } from '../../buttons/OutlineButton';

interface ActionButtonsProps {
  isOwner?: boolean;
  accountType?: string;
}

// Reusable Owner Button
const OwnerActionButton = ({ isMobile }: { isMobile: boolean }) => {
  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'flex-start',
        mt: isMobile ? 1 : 0,
        width: '100%',
        maxWidth: 412,
      }}
    >
      <Button
        variant="contained"
        sx={{
          borderRadius: '8px',
          textTransform: 'none',
          fontWeight: 700,
          backgroundColor: 'transparent',
          border: { xs: '1px solid #A24295', lg: '1px solid white' },
          color: { xs: '#A24295', lg: 'white' },
          fontSize: { xs: 14, lg: 16 },
          height: isMobile ? 36 : 40,
          width: '100%',
        }}
      >
        Go to My Network →
      </Button>
    </Box>
  );
};

// Dropdown menu
const MoreOptionsMenu = ({
  anchorEl,
  open,
  onClose,
}: {
  anchorEl: HTMLElement | null;
  open: boolean;
  onClose: () => void;
}) => {
  if (!open) return null;

  return (
    <Menu
      anchorEl={anchorEl}
      open={open}
      onClose={onClose}
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'right',
      }}
      transformOrigin={{
        vertical: 'top',
        horizontal: 'right',
      }}
    >
      <MenuItem onClick={onClose}>Share</MenuItem>
      <MenuItem onClick={onClose}>Report</MenuItem>
      <MenuItem onClick={onClose}>Block (optional report)</MenuItem>
    </Menu>
  );
};

function ActionButtons({
  isOwner = false,
  accountType = 'other',
}: ActionButtonsProps) {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  if (isOwner) {
    return <OwnerActionButton isMobile={isMobile} />;
  }

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 1,
        mt: isMobile ? 1 : 0,
        width: '100%',
        justifyContent: isMobile ? 'space-between' : 'flex-start',
        flexDirection: 'row',
      }}
    >
      {accountType === 'student' ? (
        <>
          <ColoredButton
            label="+ Connect"
            sx={{ width: { xs: 120, lg: 180 } }}
          />
          <OutlinedButton
            label="Contact"
            sx={{ width: { xs: 120, lg: 180 } }}
          />
        </>
      ) : (
        <>
          <ColoredButton label="Follow" />
          <OutlinedButton label="+ Connect" />
          <OutlinedButton label="Contact" />
        </>
      )}

      <Box>
        <IconButton onClick={handleClick}>
          <Iconify
            icon="pepicons-pop:dots-y"
            width={30}
            height={30}
            sx={{ color: isMobile ? '#A24295' : 'white' }}
          />
        </IconButton>

        {/* Conditionally render the menu */}
        {open && (
          <MoreOptionsMenu
            anchorEl={anchorEl}
            open={open}
            onClose={handleClose}
          />
        )}
      </Box>
    </Box>
  );
}

export default React.memo(ActionButtons);
