'use client';
// Dummy other links data, similar to otherArticles
const otherLinks = [
  {
    id: 'link-1',
    title: 'Innovations in Cardiology 2025',
    coverImagePath:
      'https://assets.dev.minicardiac.com/assets/media-post-images/media-post-1.jpg',
    caption: 'Latest updates in cardiac care.',
    url: 'https://www.example.com',
  },
];

import { useParams } from 'next/navigation';
import { FullPageLoader } from '@minicardiac-client/components';
import { useAuth } from '@minicardiac-client/apis';
import PostDetailLayout from '@/libs/components/src/lib/content-posts/detail-post/PostDetailLayout';
import { usePostById } from '@/libs/apis/src/lib/posts/use-feed';
import Head from '@/libs/components/src/lib/head/Head';

export default function LinkPostPageWrapper() {
  const { authState } = useAuth();
  const params = useParams();
  const linkPostId = params?.id as string;
  const { data: post, isLoading } = usePostById(linkPostId);

  if (authState.isLoading || isLoading) {
    return <FullPageLoader open={true} />;
  }

  if (!post) {
    return (
      <div style={{ padding: '2rem', textAlign: 'center', fontSize: '1.2rem' }}>
        Invalid post ID. No post found.
      </div>
    );
  }

  return (
    <>
      <Head title={post?.title || (post as any)?.caption || post?.content} />
      <PostDetailLayout
        user={{
          name: post?.publisherName || '',
          profilePic: post?.profileImageUrlThumbnail || '',
          postedAgo: post?.postedAt || '',
        }}
        sidebarItems={otherLinks}
        sidebarTitle="Other Links"
        postId={linkPostId}
        post={post}
      >
        {/* Link preview/title */}
        <div style={{ marginBottom: '1rem' }}>
          <a
            href={(post as any)?.link || post?.title}
            target="_blank"
            rel="noopener noreferrer"
          >
            {post?.title || (post as any)?.link}
          </a>
        </div>
        {/* Show caption for link posts, fallback to content */}
        <div>{(post as any)?.caption || post?.content}</div>
      </PostDetailLayout>
    </>
  );
}
