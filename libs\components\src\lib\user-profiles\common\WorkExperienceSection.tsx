'use client';

import { Box, Typography } from '@mui/material';
import WorkplaceBlock from './WorkplaceBlock';

const workExperienceData = [
  {
    workplace: 'Copenhagen Central Hospital, Copenhagen',
    companyLogo: '', // can be replaced with a path to an image if available
    positions: [
      {
        designation: 'Intern',
        duration: '2019 - 2020',
      },
    ],
  },
  {
    workplace: 'All India Institute of Medical Sciences, Delhi',
    companyLogo: '',
    positions: [
      {
        designation: 'Resident Doctor',
        duration: '2020 - 2021',
      },
      {
        designation: 'Assistant Surgeon',
        duration: '2021 - 2022',
      },
    ],
  },
  {
    workplace: 'Fortis Memorial Research Institute, Gurgaon',
    companyLogo: '',
    positions: [
      {
        designation: 'Consultant Physician',
        duration: '2022 - 2023',
      },
    ],
  },
];
const totalExperience = 1;

export default function WorkExperienceSection() {
  return (
    <Box
      maxWidth="976px"
      padding="20px"
      borderRadius="8px"
      display="flex"
      flexDirection="column"
      gap="20px"
      width="100%"
      bgcolor={'white'}
    >
      <Typography fontSize={{ xs: '20px', sm: '24px' }} fontWeight={600}>
        Work Experience ({totalExperience}{' '}
        {totalExperience === 1 ? 'Year' : 'Years'}){' '}
      </Typography>

      {workExperienceData.map((workplace, idx) => (
        <WorkplaceBlock key={idx} {...workplace} />
      ))}
    </Box>
  );
}
