import ResponsiveLayout from '@/libs/components/src/lib/layout/ResponsiveLayout';
import OrganizationProfile from '@/libs/components/src/lib/user-profiles/organization/OrganizationProfile';
import ProfessionalProfile from '@/libs/components/src/lib/user-profiles/professional/ProfessionalProfile';
import StudentProfile from '@/libs/components/src/lib/user-profiles/student/StudentProfile';
import { notFound } from 'next/navigation';

interface Props {
  params: {
    locale: string;
    username: string;
  };
}

export default async function VanityProfilePage({ params }: Props) {
  const { username } = params;

  const profile = { role: 'student' }; // Replace this with actual function when api is integrated

  if (!profile || !profile.role) return notFound();

  if (profile.role === 'student') {
    return (
      <ResponsiveLayout>
        <StudentProfile username={username} />
      </ResponsiveLayout>
    );
  }

  if (profile.role === 'professional') {
    return (
      <ResponsiveLayout>
        <ProfessionalProfile username={username} />
      </ResponsiveLayout>
    );
  }

  if (profile.role === 'organization') {
    return (
      <ResponsiveLayout>
        <OrganizationProfile username={username} />
      </ResponsiveLayout>
    );
  }

  return notFound();
}
