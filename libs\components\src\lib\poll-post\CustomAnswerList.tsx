import { Box, Typography } from '@mui/material';
import PollOptionCard from './PollOptionCard';
import MessageIcon from '../Icons/PostIcons/MessageIcon';
import { useTranslations } from 'next-intl';
import { useDeletePollVote } from '@minicardiac-client/apis';
import { Option, PollData } from '@minicardiac-client/types';

interface Props {
  answers: Option[] | undefined;
  totalVotes: number;
  setPollData: React.Dispatch<React.SetStateAction<PollData>>;
  onSelect: (id: string) => void;
  postId: string;
  expiresAt: string;
}

const CustomAnswerList = ({
  answers,
  totalVotes,
  setPollData,
  onSelect,
  postId,
  expiresAt,
}: Props) => {
  const t = useTranslations('pollPost');

  const { mutate: deleteVote } = useDeletePollVote({
    onSuccess: () => {
      setPollData((prev) => {
        return {
          ...prev,
          options: prev?.options?.map((option) => ({
            ...option,
            isUserVote: false,
          })),
        };
      });
    },
  });

  return (
    <Box display="flex" flexDirection="column" gap="16px" mt="16px">
      {answers?.map(
        (option) =>
          option.isCustom && (
            <PollOptionCard
              key={option.id}
              label={option?.text}
              votes={option?.votesCount}
              totalVotes={totalVotes}
              selected={option.isUserVote}
              onSelect={() => onSelect(option.id)}
              isCustom={true}
              percentage={option.percentage}
              expiresAt={expiresAt}
            >
              <Box display="flex" alignItems="center" gap="8px">
                <MessageIcon />
                <Typography sx={{ fontSize: '16px', fontWeight: 400 }}>
                  {option.text}
                </Typography>
              </Box>
            </PollOptionCard>
          )
      )}

      {/* Retract Vote */}
      <Typography
        onClick={() => deleteVote(postId)}
        sx={{
          color: '#A24295',
          fontWeight: 600,
          fontSize: '16px',
          cursor: 'pointer',
          mt: '8px',
          mb: '8px',
          textAlign: 'left',
        }}
      >
        {t('retractVote')}
      </Typography>
    </Box>
  );
};

export default CustomAnswerList;
