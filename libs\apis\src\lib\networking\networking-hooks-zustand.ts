import { useNetworkingStore } from './networking-store.js';
import { useMemo } from 'react';
import {
  useSendConnectionRequest as useOriginalSendConnectionRequest,
  useFollowProfile as useOriginalFollowProfile,
  useUnfollowProfile as useOriginalUnfollowProfile,
  useCancelConnectionRequest as useOriginalCancelConnectionRequest,
  useGetDiscoverProfiles as useOriginalGetDiscoverProfiles,
} from './networking-hooks.js';

/**
 * Enhanced hook for sending connection requests with Zustand state management
 * Updates the UI state immediately and reverts on error
 */
export const useSendConnectionRequestWithState = () => {
  const sendConnectionRequestMutation = useOriginalSendConnectionRequest();
  const setConnectionRequest = useNetworkingStore(
    (state) => state.setConnectionRequest
  );

  return {
    ...sendConnectionRequestMutation,
    mutateAsync: async (profileId: string) => {
      // Update UI state immediately for better UX
      setConnectionRequest(profileId, true);

      try {
        // Make API call
        const result = await sendConnectionRequestMutation.mutateAsync(
          profileId
        );
        return result;
      } catch (error) {
        // Revert UI state on error
        setConnectionRequest(profileId, false);
        throw error;
      }
    },
  };
};

/**
 * Enhanced hook for following profiles with Zustand state management
 * Updates the UI state immediately and reverts on error
 */
export const useFollowProfileWithState = () => {
  const followProfileMutation = useOriginalFollowProfile();
  const setFollowedProfile = useNetworkingStore(
    (state) => state.setFollowedProfile
  );

  return {
    ...followProfileMutation,
    mutateAsync: async (profileId: string) => {
      // Update UI state immediately for better UX
      setFollowedProfile(profileId, true);

      try {
        // Make API call
        const result = await followProfileMutation.mutateAsync(profileId);
        return result;
      } catch (error) {
        // Revert UI state on error
        setFollowedProfile(profileId, false);
        throw error;
      }
    },
  };
};

/**
 * Enhanced hook for unfollowing profiles with Zustand state management
 * Updates the UI state immediately and reverts on error
 */
export const useUnfollowProfileWithState = () => {
  const unfollowProfileMutation = useOriginalUnfollowProfile();
  const setFollowedProfile = useNetworkingStore(
    (state) => state.setFollowedProfile
  );

  return {
    ...unfollowProfileMutation,
    mutateAsync: async (profileId: string) => {
      // Update UI state immediately for better UX
      setFollowedProfile(profileId, false);

      try {
        // Make API call
        const result = await unfollowProfileMutation.mutateAsync(profileId);
        return result;
      } catch (error) {
        // Revert UI state on error
        setFollowedProfile(profileId, true);
        throw error;
      }
    },
  };
};

/**
 * Enhanced hook for canceling connection requests with Zustand state management
 * Updates the UI state immediately and reverts on error
 */
export const useCancelConnectionRequestWithState = () => {
  const cancelConnectionRequestMutation = useOriginalCancelConnectionRequest();
  const setConnectionRequest = useNetworkingStore(
    (state) => state.setConnectionRequest
  );

  return {
    ...cancelConnectionRequestMutation,
    mutateAsync: async (profileId: string) => {
      // Update UI state immediately for better UX
      setConnectionRequest(profileId, false);

      try {
        // Make API call
        const result = await cancelConnectionRequestMutation.mutateAsync(
          profileId
        );
        return result;
      } catch (error) {
        // Revert UI state on error
        setConnectionRequest(profileId, true);
        throw error;
      }
    },
  };
};

/**
 * Enhanced hook for getting discover profiles with Zustand state enhancement
 * Automatically enhances profiles with UI state from the store
 * Uses useMemo to prevent unnecessary recalculations and infinite render loops
 */
export const useGetDiscoverProfilesWithState = (accountType?: string) => {
  const discoverProfilesQuery = useOriginalGetDiscoverProfiles(accountType);
  const enhanceProfiles = useNetworkingStore((state) => state.enhanceProfiles);

  const enhancedData = useMemo(() => {
    return discoverProfilesQuery.data
      ? enhanceProfiles(discoverProfilesQuery.data)
      : undefined;
  }, [discoverProfilesQuery.data, enhanceProfiles]);

  return {
    ...discoverProfilesQuery,
    data: enhancedData,
  };
};
