import { Avatar, Box, IconButton, Typography } from '@mui/material';
import { Iconify } from '../iconify';

export const InviteCard = ({
  name,
  profilePic,
  role,
  onClick,
}: {
  name: string;
  profilePic: string;
  role?: string;
  onClick: () => void;
}) => {
  return (
    <Box
      width="300px"
      height="64px"
      minHeight={'64px'}
      bgcolor="#fff"
      border={'1px solid #A3A3A3'}
      borderRadius="12px"
      px="16px"
      display="flex"
      alignItems="center"
      justifyContent="space-between"
      sx={{ cursor: 'pointer' }}
    >
      <Box display="flex" alignItems="center">
        <Avatar
          src={profilePic}
          sx={{ width: 32, height: 32, mr: '8px' }}
          alt={name}
        />
        <Box>
          <Typography fontSize="14px" fontWeight={500}>
            {name}
          </Typography>
          <Typography fontSize="12px" color="#6B7280">
            {role}
          </Typography>
        </Box>
      </Box>
      <IconButton onClick={onClick}>
        {/* <AddIcon fill="#A24295" /> */}
        <Iconify icon="quill:add" color="#A24295" />
      </IconButton>
    </Box>
  );
};
