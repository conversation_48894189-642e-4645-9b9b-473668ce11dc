'use client';

import { useState, useCallback } from 'react';
import {
  Box,
  Typography,
  IconButton,
  Divider,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import { Icon } from '@iconify/react';
import { useTranslations } from 'next-intl';
import CustomDialog from '../dialogs/CustomDialog';
import PostHeader from './PostHeader';
import CommentsList from '../comments/CommentsList';
import AddCommentInput from '../comments/AddCommentInput';
import { useLikePost, useComments } from '@minicardiac-client/apis';
import { useFeedStore } from '../store/useFeedStore';
import { LinkPreviewCard } from './link-post/LinkPreviewCard';

interface ContentLinkPostDialogProps {
  open: boolean;
  onClose: () => void;
  linkUrl?: string;
  linkPreview?: {
    title: string;
    description: string;
    image: string;
    domain: string;
  };
  user: {
    name: string;
    profilePic: string;
    postedAgo: string;
  };
  content?: string;
  likes?: number;
  comments?: number;
  reposts?: number;
  shares?: number;
  isLiked?: boolean;
  postId?: string;
}

const ContentLinkPostDialog = ({
  open,
  onClose,
  linkUrl,
  linkPreview,
  user,
  content,
  likes = 0,
  comments = 0,
  reposts = 0,
  shares = 0,
  isLiked = false,
  postId = '',
}: ContentLinkPostDialogProps) => {
  const [liked, setLiked] = useState(isLiked);
  const [likeCount, setLikeCount] = useState(likes);
  const [reposted, setReposted] = useState(false);
  const [repostCount, setRepostCount] = useState(reposts);

  const t = useTranslations('linkPost');

  const likeMutation = useLikePost();

  const handleLikeChange = useCallback(
    (postId: string, like: boolean) => {
      useFeedStore.getState().updateLike(postId, like);

      likeMutation.mutate(
        { postId, like },
        {
          onError: (error) => {
            console.error('Failed to like post:', error);
            useFeedStore.getState().updateLike(postId, !like);
            setLiked(!like);
            setLikeCount((prev) => prev + (like ? -1 : 1));
          },
        }
      );
    },
    [likeMutation]
  );

  const handleRepostToggle = () => {
    const newReposted = !reposted;
    setReposted(newReposted);
    setRepostCount((prev) => prev + (newReposted ? 1 : -1));
  };

  const { comments: fetchedComments, isLoading: isCommentsLoading } =
    useComments(postId, { enabled: !!postId });

  const handleVisitLink = () => {
    if (linkUrl) {
      window.open(linkUrl, '_blank', 'noopener,noreferrer');
    }
  };

  return (
    <CustomDialog
      open={open}
      onClose={onClose}
      sx={{
        '& .MuiPaper-root': {
          width: '100%',
          maxWidth: 'none',
          maxHeight: '740px',
          height: '100%',
          margin: 0,
          backgroundColor: '#fff',
          boxSizing: 'border-box',
        },
        paddingX: { xs: '0px', sm: '20px', md: '60px' },
        paddingTop: { xs: '0px', sm: '45px' },
      }}
    >
      <Box
        width="100%"
        display="flex"
        flexDirection={{ xs: 'column', sm: 'row' }}
        bgcolor="#fff"
        height={{ xs: '100vh', sm: '100%' }}
        p={{ xs: '0px', sm: '40px' }}
        gap="20px"
        borderRadius="8px"
        boxSizing="border-box"
      >
        {/* Link Preview Section */}
        <Box
          position="relative"
          width={{ xs: '100%', sm: '58%' }}
          height="100%"
          maxHeight="740px"
          bgcolor="#f8f9fa"
          display="flex"
          flexDirection="column"
          justifyContent="center"
          alignItems="center"
          sx={{ borderRadius: '8px', overflow: 'hidden', p: '20px' }}
        >
          <LinkPreviewCard
            linkUrl={linkUrl}
            linkPreview={linkPreview}
            onClick={handleVisitLink}
            size="large"
          />
          
          {/* Visit Link Button */}
          <Box
            onClick={handleVisitLink}
            sx={{
              mt: '20px',
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '12px 20px',
              backgroundColor: '#A24295',
              color: '#fff',
              borderRadius: '8px',
              cursor: 'pointer',
              transition: 'all 0.2s ease',
              '&:hover': {
                backgroundColor: '#8A2C80',
                transform: 'translateY(-1px)',
              },
            }}
          >
            <Icon icon="mdi:external-link" width={20} height={20} />
            <Typography sx={{ fontSize: '14px', fontWeight: 600 }}>
              Visit Link
            </Typography>
          </Box>
        </Box>

        {/* Content Section */}
        <Box
          width={{ xs: '100%', sm: '42%' }}
          height="100%"
          display="flex"
          flexDirection="column"
          boxSizing="border-box"
          position="relative"
          sx={{ borderTopRightRadius: '8px', borderBottomRightRadius: '8px' }}
        >
          {/* Header */}
          <Box display="flex" justifyContent="space-between" px="8px" pt="8px">
            <PostHeader user={user} showOptions={false} />
            <Box display="flex" gap="20px">
              <IconButton sx={{ p: 0, color: '#A24295' }}>
                <MoreHorizIcon sx={{ fontSize: 36 }} />
              </IconButton>
              <IconButton onClick={onClose} sx={{ p: 0, color: '#A24295' }}>
                <CloseIcon sx={{ fontSize: 36 }} />
              </IconButton>
            </Box>
          </Box>

          {/* Main Content */}
          <Box
            flex={1}
            overflow="auto"
            pr="4px"
            sx={{
              'scrollbar-width': 'none', // Firefox
              '-ms-overflow-style': 'none', // IE/Edge
              '&::-webkit-scrollbar': {
                display: 'none', // Chrome/Safari
              },
            }}
          >
            <Box pb={2}>
              {/* Caption */}
              {content && (
                <Box
                  mt="20px"
                  lineHeight="22px"
                  sx={{ fontSize: '16px', color: '#1E1E1E' }}
                  dangerouslySetInnerHTML={{ __html: content }}
                />
              )}
              
              <Divider sx={{ mt: '12px', mb: '8px' }} />

              {/* Reactions */}
              <Box display="flex" justifyContent="space-between" mb="20px">
                <Typography fontSize="12px" fontWeight={600}>
                  {t('comments')} ({fetchedComments.length})
                </Typography>
                <Box display="flex" gap="28px">
                  <Box
                    display="flex"
                    alignItems="center"
                    gap="4px"
                    sx={{ cursor: 'pointer' }}
                    onClick={() => handleLikeChange(postId, !liked)}
                  >
                    <Icon
                      icon={liked ? 'mdi:heart' : 'mdi:heart-outline'}
                      width={20}
                      height={20}
                      style={{ color: liked ? '#A24295' : '#666' }}
                    />
                    <Typography
                      fontSize="12px"
                      fontWeight={600}
                      color={liked ? '#A24295' : '#666'}
                    >
                      {t('like')} ({likeCount})
                    </Typography>
                  </Box>
                  
                  <Box
                    display="flex"
                    alignItems="center"
                    gap="4px"
                    sx={{ cursor: 'pointer' }}
                    onClick={handleRepostToggle}
                  >
                    <Icon
                      icon="mdi:repeat"
                      width={20}
                      height={20}
                      style={{ color: reposted ? '#A24295' : '#666' }}
                    />
                    <Typography
                      fontSize="12px"
                      fontWeight={600}
                      color={reposted ? '#A24295' : '#666'}
                    >
                      {t('repost')} ({repostCount})
                    </Typography>
                  </Box>
                  
                  <Box display="flex" alignItems="center" gap="4px">
                    <Icon
                      icon="mdi:share-variant"
                      width={20}
                      height={20}
                      style={{ color: '#666' }}
                    />
                    <Typography
                      fontSize="12px"
                      fontWeight={600}
                      color="#A24295"
                    >
                      {t('share')} ({shares})
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Box>

            <Box flex={1} display="flex" flexDirection="column" gap="8px">
              {isCommentsLoading ? (
                <Typography
                  fontSize="14px"
                  color="#888"
                  textAlign="center"
                  mt="8px"
                >
                  Loading comments...
                </Typography>
              ) : (
                <CommentsList
                  comments={fetchedComments}
                  postId={postId}
                  sx={{ mb: 0 }}
                />
              )}
            </Box>
          </Box>

          <Box p="8px" borderTop="1px solid #ddd">
            <AddCommentInput postId={postId} />
          </Box>
        </Box>
      </Box>
    </CustomDialog>
  );
};

export default ContentLinkPostDialog;
