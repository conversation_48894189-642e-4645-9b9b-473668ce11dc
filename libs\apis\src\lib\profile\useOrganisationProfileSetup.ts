import { useMutation } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { postApi } from '../posts/post-api.js';
import { OrganisationProfileFormData } from '@minicardiac-client/types';

export function useOrganisationProfileSetup({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: (error: string) => void;
} = {}) {
  return useMutation({
    mutationFn: async (formData: OrganisationProfileFormData) => {
      const uniqueImageId = Date.now().toString();
      const defaultImageUrl = `default-profile-${uniqueImageId}.jpg`;

      const payload = {
        introductoryStatement: formData.introductoryStatement || '',
        profileImageUrl: formData.profileImageUrl || defaultImageUrl,
        profileImageUrlThumbnail:
          formData.profileImageUrlThumbnail || defaultImageUrl,
        segmentCategoryId: formData.category || '',
        location: formData.location || '',
        primarySpeciality: formData.mainProfession || 'CARDIAC_SURGEON',
        mapLink: formData.mapLink || '',
      };

      return await postApi.createOrganisationProfile(payload);
    },
    onSuccess: () => onSuccess?.(),
    onError: (err: AxiosError | Error) => {
      const errorMsg =
        err instanceof AxiosError &&
        err.response?.data?.message?.includes(
          'unique constraint "users_profile_image_url_unique"'
        )
          ? 'This profile image is already in use. Please choose a different image.'
          : err.message || 'Failed to save profile data';

      onError?.(errorMsg);
    },
  });
}
