'use client';

import { Box } from '@mui/material';
import { useState, useEffect, useCallback } from 'react';
import { usePathname } from 'next/navigation';
import SignInForm from './SignInForm';
import SignupTypeList from './SignupTypeList';
import SignUpForm from './SignUpForm';
// Animation temporarily removed to fix rendering issues

interface AuthTabContentProps {
  onSubmit?: (data: {
    email: string;
    password: string;
    displayName?: string;
    organizationName?: string;
  }) => void;
  onForgotPassword?: () => void;
  onTypeSelect?: (path: string) => void;
  activeTab: number;
  isLoading?: boolean;
  error?: string | null;
  displayNameLabel?: string;
  namePlaceholder?: string;
  emailLabel?: string;
  passwordLabel?: string;
  forgotPasswordLabel?: string;
  continueLabel?: string;
  orLabel?: string;
  googleLabel?: string;
  appleLabel?: string;
  locale?: string;
}

const AuthTabContent = (props: AuthTabContentProps) => {
  const {
    onSubmit,
    onForgotPassword,
    onTypeSelect,
    activeTab = 0,
    isLoading,
    error,
    displayNameLabel = 'Display Name',
    namePlaceholder = 'Name',
    emailLabel = 'Email',
    passwordLabel = 'Password',
    forgotPasswordLabel = 'Forgot Password?',
    continueLabel = 'Continue',
    orLabel = 'OR',
    googleLabel = 'Continue with Google',
    appleLabel = 'Continue with Apple',
  } = props;

  const pathname = usePathname();
  const [userType, setUserType] = useState<string>('');
  console.log('AuthTabContent - Current userType:', userType);
  const [showPasswords, setShowPasswords] = useState<Record<string, boolean>>({
    'signin-password': false,
    'signup-password': false,
    'confirm-password': false,
  });

  const togglePasswordVisibility = useCallback(
    (fieldId: 'signin-password' | 'signup-password' | 'confirm-password') => {
      setShowPasswords((prev) => ({
        ...prev,
        [fieldId]: !prev[fieldId],
      }));
    },
    []
  );

  useEffect(() => {
    console.log('useEffect - Path changed:', pathname);
    if (!pathname) {
      console.log('No pathname available');
      return;
    }
    const pathParts = pathname.split('/').filter(Boolean);
    console.log('Path parts:', pathParts);
    const signupIndex = pathParts.indexOf('signup');
    console.log('Signup index:', signupIndex);
    
    if (signupIndex !== -1 && pathParts.length > signupIndex + 1) {
      const typeFromUrl = pathParts[signupIndex + 1];
      console.log('Setting userType from URL:', typeFromUrl);
      setUserType(typeFromUrl);
    } else {
      console.log('No userType in URL, resetting to empty string');
      setUserType('');
    }
  }, [pathname, activeTab]);

  const handleTypeSelect = (path: string) => {
    console.log('handleTypeSelect called with path:', path);
    if (onTypeSelect) {
      console.log('Calling onTypeSelect with path:', path);
      onTypeSelect(path);
    } else {
      console.warn('onTypeSelect is not defined');
    }
  };

  const motionBoxStyle = {
    position: 'absolute' as const,
    width: '100%',
    top: 0,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: '32px',
    display: 'flex',
    flexDirection: 'column' as const,
    alignItems: 'center',
    justifyContent: 'center',
  };

  // Animation variants temporarily removed to fix rendering issues

  console.log('Rendering AuthTabContent with activeTab:', activeTab, 'userType:', userType);
  
  // Render only the active tab content without animation for now
  const renderContent = () => {
    if (activeTab === 0) {
      return (
        <div style={motionBoxStyle}>
          <SignInForm
            onSubmit={onSubmit}
            onForgotPassword={onForgotPassword}
            isLoading={isLoading}
            error={error}
            emailLabel={emailLabel}
            passwordLabel={passwordLabel}
            forgotPasswordLabel={forgotPasswordLabel}
            continueLabel={continueLabel}
            orLabel={orLabel}
            googleLabel={googleLabel}
            appleLabel={appleLabel}
            showPassword={showPasswords['signin-password']}
            onTogglePasswordVisibility={() =>
              togglePasswordVisibility('signin-password')
            }
          />
        </div>
      );
    }

    // For signup tab
    if (!userType) {
      return (
        <div style={{ ...motionBoxStyle, paddingTop: '20px' }}>
          <SignupTypeList onTypeSelect={handleTypeSelect} />
        </div>
      );
    }

    // For signup form
    return (
      <div style={motionBoxStyle}>
        <SignUpForm
          userType={userType}
          onSubmit={onSubmit}
          isLoading={isLoading}
          error={error}
          showPassword={showPasswords['signup-password']}
          showConfirmPassword={showPasswords['confirm-password']}
          onTogglePasswordVisibility={(field) =>
            togglePasswordVisibility(
              field === 'password' ? 'signup-password' : 'confirm-password'
            )
          }
          displayNameLabel={displayNameLabel}
          namePlaceholder={namePlaceholder}
          emailLabel={emailLabel}
          passwordLabel={passwordLabel}
          continueLabel={continueLabel}
          orLabel={orLabel}
          googleLabel={googleLabel}
          appleLabel={appleLabel}
        />
      </div>
    );
  };

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
      }}
    >
      <Box
        sx={{
          width: {
            md: '600px',
            sm: '400px',
            xs: '400px',
          },
          position: 'relative',
          overflow: 'hidden',
          height: '100%',
        }}
      >
        {renderContent()}
      </Box>
    </Box>
  );
};

export default AuthTabContent;

