'use client';

import { SignupPage } from '@minicardiac-client/components';
import { useRouter } from '@/apps/public/src/i18n/navigation';
import { useState } from 'react';
import { useRegisterUser } from '@minicardiac-client/apis';

export default function SignUpOrganizationPage() {
  const router = useRouter();
  const [isRegistering, setIsRegistering] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { mutateAsync: registerUser } = useRegisterUser();

  const handleSignUp = async (data: {
    email: string;
    password: string;
    displayName?: string;
    organizationName?: string;
  }) => {
    setIsRegistering(true);
    setError(null);
    try {
      const response = await registerUser({
        email: data.email,
        password: data.password,
        displayName: data.organizationName || '',
        accountType: 'ORGANISATION',
      });

      if (response.requiresOTP) {
        router.push(
          `/verify-otp?email=${encodeURIComponent(
            data.email
          )}&name=${encodeURIComponent(
            data.organizationName || ''
          )}&accountType=ORGANISATION`
        );
      } else {
        router.push('/organisation/profile');
      }
    } catch (err) {
      const errorMessage = 'Something went wrong. Please try again.';
      setError(errorMessage);
    } finally {
      setIsRegistering(false);
    }
  };

  return (
    <SignupPage
      userType="organization"
      onBack={() => router.back()}
      onSignUp={handleSignUp}
      isLoading={isRegistering}
      error={error}
    />
  );
}
