import {
  Box,
  Typography,
  Button,
  FormControlLabel,
  Checkbox,
  SxProps,
} from '@mui/material';
import NavigatorTabs from '../common/NavigatorTabs';
import { useState } from 'react';
import { CustomEditorBase } from '../../textEditor/CustomEditorBase';
import MediaUploadIcon from '../../Icons/FeedIcons/MediaUploadIcon';

export const AboutSection = () => {
  const [isDifferentVideo, setIsDifferentVideo] = useState(false);
  const [content, setContent] = useState('');

  return (
    <Box
      maxWidth="976px"
      width="100%"
      mx="auto"
      p={{ xs: '16px', sm: '20px' }}
      borderRadius="8px"
      bgcolor="#fff"
    >
      <NavigatorTabs activeTab="About" />

      {/* Main Content Container */}
      <Box
        display="flex"
        flexDirection={{ xs: 'column', lg: 'row' }}
        justifyContent={'space-between'}
        gap="20px"
        mt="20px"
      >
        {/* Left Part - Featured Video */}
        <Box>
          <Typography fontWeight={600} fontSize="16px" mb="16px">
            Featured Video:{' '}
            <Typography component="span" fontWeight={400}>
              Showcase your expertise with a pinned video
            </Typography>
          </Typography>

          <Box
            sx={{
              display: 'flex',
              gap: '16px',
              flexDirection: { xs: 'column', sm: 'row' },
            }}
          >
            {/* Public video */}
            <AddVideoBox title="Click to add video" subtitle="For Public" />
            {/* Professional video */}
            <AddVideoBox
              title="Click to add video"
              subtitle="For Professional / Organisations"
            />
          </Box>
        </Box>

        {/* Right Part - Highlights */}
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: { xs: 'flex-start', sm: 'center' },
          }}
        >
          <Typography fontWeight={600} fontSize="16px" mb="16px">
            Highlights
          </Typography>

          <Box
            width={{ xs: '100%', sm: '373px' }}
            minHeight="131px"
            bgcolor="#F5F5F5"
            borderRadius="8px"
            p={{ xs: '18px', sm: '20px' }}
            textAlign={'center'}
          >
            <Typography fontWeight={400} fontSize="16px" textAlign={'center'}>
              Would you like to highlight any awards, affiliations, or
              accomplishments? <br />
              Here’s your chance!
            </Typography>

            <Button
              sx={{
                mt: '16px',
                textTransform: 'none',
                fontWeight: 600,
                fontSize: '16px',
              }}
            >
              + Add Highlights
            </Button>
          </Box>
        </Box>
      </Box>

      {/* New Section Below */}
      <Box mt="20px">
        <FormControlLabel
          control={
            <Checkbox
              checked={isDifferentVideo}
              onChange={(e) => setIsDifferentVideo(e.target.checked)}
            />
          }
          label={
            <Typography fontWeight={400} fontSize="16px">
              Use different videos for Public and Professional/Organisation
              viewers
            </Typography>
          }
        />
      </Box>

      <Box>
        <CustomEditorBase
          value={content}
          onChange={setContent}
          label={'Biography'}
          placeholder="Describe your experience, specialties, and what sets your practice apart."
          sx={{
            maxHeight: '240px',
          }}
        />
      </Box>
    </Box>
  );
};

const AddVideoBox = ({
  title,
  subtitle,
  sx,
}: {
  title: string;
  subtitle: string;
  sx?: SxProps;
}) => {
  return (
    <Box
      width="250px"
      height="131px"
      bgcolor="white"
      border="1px solid #A24295"
      borderRadius="8px"
      p={{ xs: '12px', md: '20px' }}
      display="flex"
      alignItems="center"
      justifyContent="center"
      flexDirection="column"
      sx={{
        width: { xs: '100%', lg: '250px' },
        height: { xs: '106px', sm: '150px', lg: '131px' },
      }}
    >
      <MediaUploadIcon size={40} />

      <Typography
        mt={{ xs: '0px', sm: '12px' }}
        fontWeight={500}
        fontSize="16px"
        color="#A3A3A3"
      >
        {title}
      </Typography>
      <Typography mt="4px" fontWeight={500} fontSize="12px">
        {subtitle}
      </Typography>
    </Box>
  );
};
