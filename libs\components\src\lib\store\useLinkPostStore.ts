import { create } from 'zustand';

interface LinkPostState {
  link: string;
  content: string;
  tags: string;
  setLink: (link: string) => void;
  setContent: (content: string) => void;
  setTags: (tags: string) => void;
  reset: () => void;
}

export const useLinkPostStore = create<LinkPostState>((set) => ({
  link: '',
  content: '',
  tags: '',
  setLink: (link) => set({ link }),
  setContent: (content) => set({ content }),
  setTags: (tags) => set({ tags }),
  reset: () => set({ link: '', content: '', tags: '' }),
}));
