import { TextField } from '@mui/material';
import React from 'react';

interface YearSelectProps {
  label: string;
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  sx?: object;
}

const BASE_YEAR = 1950;

const getYearOptions = () => {
  const currentYear = new Date().getFullYear();
  const numberOfYears = currentYear - (BASE_YEAR - 1);
  return Array.from({ length: numberOfYears }, (_, i) => BASE_YEAR + i);
};

const YearSelect: React.FC<YearSelectProps> = ({
  label,
  placeholder,
  value,
  onChange,
  sx,
}) => (
  <TextField
    select
    label={label}
    value={value}
    onChange={(e) => onChange(e.target.value)}
    fullWidth
    SelectProps={{ native: true }}
    InputLabelProps={{ shrink: true }}
    sx={sx}
  >
    <option value="" disabled>
      {placeholder}
    </option>
    {getYearOptions().map((year) => (
      <option key={year} value={year}>
        {year}
      </option>
    ))}
  </TextField>
);

export default YearSelect;
