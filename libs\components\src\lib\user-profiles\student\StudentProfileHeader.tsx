import { Avatar, Box, Typography } from '@mui/material';
import ProfileHeader from '../common/ProfileHeader';
import { Profile } from '@minicardiac-client/types';
import ActionButtons from '../common/ActionButtons';
import EditButton from '../../buttons/EditButton';

export const StudentProfileHeader = ({ profile }: { profile: Profile }) => {
  return (
    <ProfileHeader
      profile={profile}
      ConnectUserComponent={ConnectUser}
      ProfileConnectionsComponent={ProfileConnections}
    />
  );
};

const ConnectUser = ({ profile }: { profile: Profile }) => {
  return (
    <>
      {/* Right: Connections + Buttons */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', lg: 'column' },
          alignItems: 'flex-start',
          justifyContent: { xs: 'space-between', lg: 'none' },
          gap: 2,
          px: { xs: '20px', lg: '0px' },
        }}
      >
        <Typography
          fontSize={{ xs: 20, lg: 24 }}
          fontWeight={400}
          sx={{
            color: { lg: 'white' },
          }}
        >
          {profile.connections} Connections
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <ActionButtons accountType="student" />
        </Box>
      </Box>
    </>
  );
};

const ProfileConnections = ({ profile }: { profile: Profile }) => {
  return (
    <>
      {/* Bottom Info */}
      <Box
        sx={{
          p: { xs: '16px', lg: '20px' },
          display: 'flex',
          justifyContent: 'space-between',
          flexDirection: { xs: 'column', lg: 'row' },
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          background: 'transparent',
          flexWrap: 'wrap',
          gap: '10px',
        }}
      >
        {/* Left: Avatar + Name + College */}
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Avatar
            src={profile.profilePic}
            sx={{ width: 88, height: 94, borderRadius: '50%' }}
          />
          <Box sx={{ ml: { xs: '10px', lg: '20px' } }}>
            <Typography
              fontWeight={700}
              fontSize={{ xs: 20, lg: 24 }}
              color="white"
              sx={{
                '&:hover .edit-name-button': {
                  opacity: 1,
                  pointerEvents: 'auto',
                },
                cursor: 'pointer',
                width: 'fit-content',
                display: 'flex',
                alignItems: 'center',
              }}
            >
              {profile.name}
              <EditButton
                className="edit-name-button"
                sx={{
                  position: 'static',
                  backgroundColor: 'transparent',
                }}
                width={20}
                height={20}
              />
            </Typography>
            <Box
              sx={{
                '&:hover .edit-college-button': {
                  opacity: 1,
                  pointerEvents: 'auto',
                  display: { xs: 'none', smd: 'block' },
                },
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
              }}
            >
              <Typography
                sx={{
                  mt: 1,
                  fontSize: 12,
                  fontWeight: 600,
                  backgroundColor: '#F8F9FA',
                  color: '#A24295',
                  borderRadius: '20px',
                  p: 1,
                  minHeight: '30px',
                  maxWidth: 400,
                  height: 'auto',
                  whiteSpace: 'normal',
                  wordBreak: 'break-word',
                  lineHeight: '16px',
                  textAlign: 'left',
                }}
              >
                {profile.college}
              </Typography>
              <EditButton
                className="edit-college-button"
                sx={{
                  position: 'static',
                  backgroundColor: 'transparent',
                }}
                width={20}
                height={20}
              />
            </Box>
          </Box>
        </Box>

        <Box
          sx={{
            display: { xs: 'none', lg: 'block' },
          }}
        >
          <ConnectUser profile={profile} />
        </Box>
      </Box>
    </>
  );
};
