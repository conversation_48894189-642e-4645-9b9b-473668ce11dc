import { NextRequest, NextResponse } from 'next/server';

function escapeRegExp(str: string): string {
  return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

function extractMetaContent(
  html: string,
  property: string,
  nameAttribute = 'property'
): string | null {
  const escapedProperty = escapeRegExp(property);
  const regex = new RegExp(
    `<meta[^>]*${nameAttribute}=["']${escapedProperty}["'][^>]*content=["']([^"']*)["']`,
    'i'
  );
  const match = html.match(regex);
  return match ? match[1] : null;
}

function extractTitle(html: string): string | null {
  const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
  return titleMatch ? titleMatch[1].trim() : null;
}

function extractDescription(html: string): string | null {
  let description = extractMetaContent(html, 'description', 'name');

  if (!description) {
    description = extractMetaContent(html, 'og:description');
  }

  if (!description) {
    description = extractMetaContent(html, 'twitter:description', 'name');
  }

  return description;
}

function extractImage(html: string): string | null {
  let image = extractMetaContent(html, 'og:image');

  if (!image) {
    image = extractMetaContent(html, 'twitter:image', 'name');
  }

  if (!image) {
    image = extractMetaContent(html, 'twitter:image:src', 'name');
  }

  return image;
}

export async function POST(request: NextRequest) {
  try {
    const { url } = await request.json();

    if (!url) {
      return NextResponse.json({ error: 'URL is required' }, { status: 400 });
    }

    let validUrl: string;
    try {
      const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`);
      validUrl = urlObj.toString();
    } catch {
      return NextResponse.json(
        { error: 'Invalid URL format' },
        { status: 400 }
      );
    }

    const domain = new URL(validUrl).hostname;

    // Primary fetch with enhanced headers for better compatibility
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000);

      const response = await fetch(validUrl, {
        method: 'GET',
        headers: {
          'User-Agent':
            'facebookexternalhit/1.1 (+http://www.facebook.com/externalhit_uatext.php)',
          Accept:
            'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Cache-Control': 'no-cache',
          Pragma: 'no-cache',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const html = await response.text();

        const ogTitle =
          extractMetaContent(html, 'og:title') ||
          extractMetaContent(html, 'twitter:title', 'name');
        const ogDescription = extractDescription(html);
        const ogImage = extractImage(html);
        const ogSiteName = extractMetaContent(html, 'og:site_name');
        const ogType = extractMetaContent(html, 'og:type') || 'website';

        const title = ogTitle || extractTitle(html) || `Website - ${domain}`;
        const description = ogDescription || 'Click to visit this website';
        const image =
          ogImage ||
          `https://www.google.com/s2/favicons?domain=${domain}&sz=128`;

        const linkPreview = {
          title: title.length > 100 ? title.substring(0, 97) + '...' : title,
          description:
            description.length > 200
              ? description.substring(0, 197) + '...'
              : description,
          image: image,
          domain: ogSiteName || domain,
          url: validUrl,
          success: true,
          author: extractMetaContent(html, 'author', 'name') || null,
          publishedTime:
            extractMetaContent(html, 'article:published_time') || null,
          type: ogType,
        };

        return NextResponse.json(linkPreview);
      }
    } catch (fetchError) {
      console.log(
        `Primary fetch failed for ${validUrl}, trying fallback:`,
        fetchError
      );
    }

    // Fallback fetch with basic browser user agent
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 8000);

      const fallbackResponse = await fetch(validUrl, {
        method: 'GET',
        headers: {
          'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (fallbackResponse.ok) {
        const html = await fallbackResponse.text();

        const title = extractTitle(html) || `Website - ${domain}`;
        const description =
          extractDescription(html) || 'Click to visit this website';

        return NextResponse.json({
          title: title.length > 100 ? title.substring(0, 97) + '...' : title,
          description:
            description.length > 200
              ? description.substring(0, 197) + '...'
              : description,
          image: `https://www.google.com/s2/favicons?domain=${domain}&sz=128`,
          domain: domain,
          url: validUrl,
          success: true,
        });
      }
    } catch (fallbackError) {
      const errorMessage =
        fallbackError instanceof Error
          ? fallbackError.message
          : String(fallbackError);
      console.log(`Fallback fetch failed for ${validUrl}:`, errorMessage);
    }

    // Final fallback response
    return NextResponse.json({
      title: `Website - ${domain}`,
      description: 'Click to visit this website',
      image: `https://www.google.com/s2/favicons?domain=${domain}&sz=128`,
      domain: domain,
      url: validUrl,
      success: true,
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);

    console.error('Link preview error:', {
      success: false,
      requestUrl: 'unknown',
      errorMessage,
    });

    return NextResponse.json(
      { error: 'Failed to fetch link preview' },
      { status: 500 }
    );
  }
}
