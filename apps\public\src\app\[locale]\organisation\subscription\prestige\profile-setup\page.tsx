'use client';

import React from 'react';
import { Box, Container, useMediaQuery } from '@mui/material';
import { useRouter } from 'next/navigation';
import {
  BackButton,
  CustomizedSteppers,
  OrganisationProfileSetupForm,
  PatientProfileWelcome,
  Subtitle,
} from '@minicardiac-client/components';
import {
  ProfileFormData,
  OrganisationProfileFormData,
} from '@minicardiac-client/components';
import { axiosInstance, useAuth } from '@minicardiac-client/apis';
import { useSnackbar } from '@minicardiac-client/components';
import { useTheme } from '@emotion/react';

export default function OrganisationPrestigeProfileSetupPage() {
  const router = useRouter();
  const [activeStep] = React.useState(0);
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const { showSuccess, showError } = useSnackbar();
  const [formData, setFormData] = React.useState<ProfileFormData>({
    introductoryStatement: '',
    profileImageUrl: '',
    profileImageUrlThumbnail: '',
    title: '',
    qualifications: '',
    jobTitle: '',
    employerId: '',
  });
  const [error, setError] = React.useState<string | null>(null);

  const { authState } = useAuth();
  const theme: any = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));

  React.useEffect(() => {
    const refreshUserSession = async () => {
      try {
        // Import dynamically to avoid circular dependencies
      } catch (error) {
        console.error('Failed to refresh session:', error);
      }
    };

    refreshUserSession();
  }, []);

  const handleFormChange = (data: OrganisationProfileFormData) => {
    // Convert OrganisationProfileFormData to ProfileFormData for compatibility
    const profileData: ProfileFormData = {
      introductoryStatement: data.introductoryStatement,
      profileImageUrl: data.profileImageUrl,
      profileImageUrlThumbnail: data.profileImageUrlThumbnail,
      title: data.title,
      qualifications: data.qualifications,
      jobTitle: data.jobTitle,
      employerId: data.employerId,
    };
    setFormData(profileData);
  };

  const handleContinue = async () => {
    // Set submitting state
    setIsSubmitting(true);
    setError(null);

    try {
      // Generate a unique identifier for the profile image if none is provided
      const uniqueImageId = Date.now().toString();
      const defaultImageUrl = `default-profile-${uniqueImageId}.jpg`;

      // Post the profile data to the API
      await axiosInstance.post('onboarding/profile-setup/specialist', {
        introductoryStatement: formData.introductoryStatement || '',
        profileImageUrl: formData.profileImageUrl || defaultImageUrl,
        profileImageUrlThumbnail:
          formData.profileImageUrlThumbnail || defaultImageUrl,
        title: formData.title || '',
        qualifications: formData.qualifications || '',
        jobTitle: formData.jobTitle || '',
        employerId: formData.employerId || undefined,
      });

      // Show success message and navigate
      showSuccess('Profile saved successfully!');
      setTimeout(() => {
        router.push('/professional/paid/document-upload');
      }, 1000);
    } catch (err: any) {
      console.error('Error saving profile data:', err);

      // Check for the specific unique constraint error
      if (
        err.response?.data?.message?.includes(
          'unique constraint "users_profile_image_url_unique"'
        )
      ) {
        const errorMsg =
          'This profile image is already in use. Please choose a different image.';
        setError(errorMsg);
        showError(errorMsg);
      } else {
        setError(err.message || 'Failed to save profile data');
        showError(err.message || 'Failed to save profile data');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDoThisLater = () => {
    // Navigate to another page or dashboard
    router.push('/feed?fromSignup=true');
  };

  // Add custom CSS to fix the layout
  React.useEffect(() => {
    // Create a style element
    const style = document.createElement('style');

    // Add CSS to fix the Introductory Statement position

    //Removing mt, UI breaking. Don't use this type of css
    style.innerHTML = `
      /* Move Introductory Statement below Title */
      .MuiGrid-container > .MuiGrid-root:last-child {
        order: 5 !important;
        margin-top: 0px !important;
        width: 100% !important;
      }

      /* Ensure the Introductory Statement has proper width */
      .MuiGrid-container > .MuiGrid-root:last-child > div {
        width: 100% !important;
      }
    `;

    // Append the style to the document head
    document.head.appendChild(style);

    // Clean up on unmount
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  return (
    <Container maxWidth="lg">
      <Box mt={'20px'}>
        {/* Welcome Header */}
        {!isSmallScreen && (
          <PatientProfileWelcome
            patientName={authState.user?.displayName || ''}
            subtitle={''}
          />
        )}

        <BackButton handleBackButton={() => router.back()} />

        <Subtitle
          text={'Let’s set up your Organisation Account!'}
          sx={{ fontSize: { xs: '12px', sm: '16px' } }}
          marginBottom={'34px'}
        />
        <CustomizedSteppers
          activeStep={activeStep}
          steps={['Profile Setup', 'Document Upload', 'Adding Network']}
        />

        {/* Use the ProfileSetupForm component with isBasicPlan=false for paid users */}
        <Box sx={{ mt: { xs: '20px', sm: '30px', md: '40px' } }}>
          <OrganisationProfileSetupForm
            isBasicPlan={false}
            onChange={handleFormChange}
            onSave={handleContinue}
            onSkip={handleDoThisLater}
            isSubmitting={isSubmitting}
            subscriptionType={'Prestige'}
          />
          {error && (
            <Box sx={{ color: 'error.main', mt: 2, textAlign: 'center' }}>
              {error}
            </Box>
          )}
        </Box>
      </Box>
    </Container>
  );
}
