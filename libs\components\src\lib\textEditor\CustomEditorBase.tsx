import { useEffect } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import TextAlign from '@tiptap/extension-text-align';
import HorizontalRule from '@tiptap/extension-horizontal-rule';
import Heading from '@tiptap/extension-heading';
import Placeholder from '@tiptap/extension-placeholder';
import Link from '@tiptap/extension-link';

import { RichTextEditorProvider } from 'mui-tiptap';

import { SxProps, ThemeProvider } from '@mui/material/styles';
import { Box, Divider, GlobalStyles, Typography } from '@mui/material';
import { Theme } from '@emotion/react';
import { CustomEditorMenuArticle } from './CustomEditorMenuArticle';
import { editorTheme } from '@minicardiac-client/utilities';
import { CustomEditorMenuText } from './CustomEditorMenuText';
import Image from '@tiptap/extension-image';

type EditorMenuType = 'article' | 'text';

export function CustomEditorBase({
  value,
  onChange,
  label,
  placeholder,
  sx,
  menuType = 'text',
}: {
  value: string;
  onChange: (content: string) => void;
  label: string;
  placeholder?: string;
  sx?: SxProps<Theme>;
  menuType?: EditorMenuType;
}) {
  const editor = useEditor({
    extensions: [
      Image.configure({
        inline: false,
      }),
      StarterKit.configure({
        heading: false, // disable default heading handling in StarterKit
      }),
      Underline,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      HorizontalRule,
      Heading.configure({
        levels: [1, 2, 3],
      }),
      Placeholder.configure({
        placeholder: placeholder,
      }),
      Link.configure({
        openOnClick: true,
        autolink: true,
        linkOnPaste: true,
        HTMLAttributes: {
          rel: 'noopener noreferrer',
          target: '_blank',
          class: 'editor-link',
        },
      }),
    ],
    content: value || '',
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      if (onChange) {
        onChange(html);
      }
    },
  });

  useEffect(() => {
    if (editor && value !== editor.getHTML()) {
      editor.commands.setContent(value || '', false);
    }
  }, [value, editor]);

  if (!editor) return null;

  const renderMenu = () => {
    if (menuType === 'article')
      return <CustomEditorMenuArticle editor={editor} />;
    if (menuType === 'text') return <CustomEditorMenuText editor={editor} />;
    return null;
  };

  return (
    <ThemeProvider theme={editorTheme}>
      <GlobalStyles
        styles={{
          '.ProseMirror-focused': {
            border: 'none !important',
            outline: 'none !important',
            boxShadow: 'none !important',
          },
          '.ProseMirror': {
            outline: 'none',
            border: 'none',
            boxShadow: 'none',
            minHeight: '100%',
          },
          '.ProseMirror p.is-editor-empty:first-of-type::before': {
            content: `'${placeholder}'`,
            color: '#999',
            float: 'left',
            height: 0,
            pointerEvents: 'none',
            userSelect: 'none',
          },
          '.ProseMirror h1': {
            fontSize: '1.8rem',
            fontWeight: 600,
            margin: '0.5rem 0',
          },
          '.ProseMirror h2': {
            fontSize: '1.4rem',
            fontWeight: 500,
            margin: '0.4rem 0',
          },
          '.ProseMirror h3': {
            fontSize: '1.2rem',
            fontWeight: 500,
            margin: '0.3rem 0',
          },
          '.ProseMirror p': {
            fontSize: '1rem',
            margin: '0.2rem 0',
          },
          '.ProseMirror img': {
            maxWidth: '100%',
            maxHeight: '300px',
            height: 'auto',
            width: 'auto',
            objectFit: 'contain',
            display: 'block',
            margin: '8px 0',
            borderRadius: '4px',
          },
        }}
      />

      <RichTextEditorProvider editor={editor}>
        <Box
          sx={{
            border: '1px solid #A3A3A3',
            borderRadius: '8px',
            backgroundColor: '#fff',
            position: 'relative',
            height: { xs: '513px', sm: '322px' },
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-between',
            mt: '40px',
            ...sx,
          }}
        >
          <Box sx={{ p: '15px', overflow: 'auto' }}>
            <Typography
              variant="body1"
              sx={{
                position: 'absolute',
                top: '-12px',
                left: '10px',
                px: '6px',
                backgroundColor: '#FFFFFF',
                fontWeight: 500,
                fontSize: editorTheme.typography.pxToRem(16),
                color: '#1E1E1E',
                zIndex: 1,
              }}
            >
              {label}
            </Typography>

            <EditorContent
              editor={editor}
              style={{
                width: '100%',
                height: '100%',
                border: 'none',
              }}
            />
          </Box>

          <Box>
            <Divider orientation="horizontal" sx={{ height: '20px' }} />

            <Box
              sx={{
                gap: '10px',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                p: '10px',
              }}
            >
              {renderMenu()}
            </Box>
          </Box>
        </Box>
      </RichTextEditorProvider>
    </ThemeProvider>
  );
}
