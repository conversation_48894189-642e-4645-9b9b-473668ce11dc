import { Box, Typography } from '@mui/material';

type ShowcaseCardProps = {
  image: string;
  title: string;
  highlight: string;
  description: string;
};

export default function ShowcaseCard({
  image,
  title,
  highlight,
  description,
}: ShowcaseCardProps) {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: { xs: 'column', sm: 'row' },
        gap: { xs: '20px', sm: '20px' },
        alignItems: 'center',
      }}
    >
      <Box
        component="img"
        src={image}
        alt={title}
        width={{ xs: '297px', sm: '381px' }}
        height={{ xs: '304px', sm: '280px' }}
        borderRadius="8px"
        sx={{ objectFit: 'cover' }}
      />

      <Box
        sx={{
          display: 'flex',
          gap: '20px',
          flexDirection: 'column',
        }}
      >
        <Typography fontWeight={600} fontSize="20px">
          {title}
        </Typography>
        <Box>
          <Typography
            fontWeight={600}
            fontSize="16px"
            lineHeight="18px"
            mt="10px"
          >
            {highlight}
          </Typography>
          <Typography
            fontWeight={400}
            fontSize="16px"
            lineHeight="18px"
            mt="8px"
          >
            {description}
          </Typography>
        </Box>
      </Box>
    </Box>
  );
}
