import React from 'react';
import Box from '@mui/material/Box';
import Skeleton from '@mui/material/Skeleton';

interface PostSkeletonCardProps {
  mb?: number | string;
}

export const PostSkeletonCard: React.FC<PostSkeletonCardProps> = ({
  mb = 2,
}) => (
  <Box mb={mb} p={2} borderRadius={2} boxShadow={1} bgcolor="background.paper">
    <Box display="flex" alignItems="center" mb={1}>
      <Skeleton variant="circular" width={40} height={40} />
      <Box ml={2} flex={1}>
        <Skeleton variant="text" width="40%" height={20} />
        <Skeleton variant="text" width="30%" height={16} />
      </Box>
    </Box>
    <Skeleton variant="rectangular" width="100%" height={120} sx={{ mb: 1 }} />
    <Skeleton variant="text" width="90%" height={20} />
    <Skeleton variant="text" width="80%" height={20} />
    <Skeleton variant="text" width="60%" height={20} />
  </Box>
);
