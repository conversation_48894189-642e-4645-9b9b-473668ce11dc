import { useTheme } from '@emotion/react';
import { Button, SxProps, useMediaQuery } from '@mui/material';

export const ColoredButton = ({
  label,
  sx,
}: {
  label: string;
  sx?: SxProps;
}) => {
  const theme: any = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  return (
    <Button
      variant="contained"
      fullWidth={isMobile}
      sx={{
        backgroundColor: '#A24295',
        fontSize: { xs: 14, lg: 16 },
        height: isMobile ? 36 : 40,
        ...sx,
      }}
    >
      {label}
    </Button>
  );
};
