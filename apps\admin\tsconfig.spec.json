{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "./out-tsc/jest", "jsx": "react-jsx", "types": ["jest", "node", "@nx/react/typings/cssmodule.d.ts", "@nx/react/typings/image.d.ts"], "module": "esnext", "moduleResolution": "bundler"}, "include": ["jest.config.ts", "src/**/*.test.ts", "src/**/*.spec.ts", "src/**/*.test.tsx", "src/**/*.spec.tsx", "src/**/*.test.js", "src/**/*.spec.js", "src/**/*.test.jsx", "src/**/*.spec.jsx", "src/**/*.d.ts"], "references": [{"path": "./tsconfig.app.json"}]}