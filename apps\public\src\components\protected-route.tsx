'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth, useVerifySession } from '@minicardiac-client/apis';
import { getDecodedToken } from '@minicardiac-client/utilities';
import { FullPageLoader } from '@minicardiac-client/components';

interface RouteGuardProps {
  children: React.ReactNode;
  disableRedirect?: boolean;
}

// Helper function to get middleware session info
const getMiddlewareSessionInfo = () => {
  if (typeof window === 'undefined') return null;

  const sessionValid =
    document
      .querySelector('meta[name="session-valid"]')
      ?.getAttribute('content') === 'true';
  const userAuthenticated =
    document
      .querySelector('meta[name="user-authenticated"]')
      ?.getAttribute('content') === 'true';
  const customToken = document
    .querySelector('meta[name="custom-token"]')
    ?.getAttribute('content');
  const userInfo = document
    .querySelector('meta[name="user-info"]')
    ?.getAttribute('content');

  return {
    sessionValid,
    userAuthenticated,
    customToken,
    userInfo: userInfo ? JSON.parse(userInfo) : null,
  };
};

export const ProtectedRoute: React.FC<RouteGuardProps> = ({ children }) => {
  const { authState } = useAuth();
  const sessionQuery = useVerifySession();
  const router = useRouter();
  const pathname = usePathname();
  const [decoded, setDecoded] = useState<any>(null);
  const [middlewareInfo, setMiddlewareInfo] = useState<any>(null);

  // Get middleware session info on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const info = getMiddlewareSessionInfo();
      setMiddlewareInfo(info);
      console.log('Middleware session info:', info);
    }
  }, []);

  useEffect(() => {
    const fetchToken = async () => {
      try {
        const decodedToken = await getDecodedToken();
        console.log('Decoded Token:', decodedToken); // Debug log
        setDecoded(decodedToken);
      } catch (error) {
        console.error('Error getting decoded token:', error);

        // If middleware has session info, use it as fallback
        if (middlewareInfo?.userInfo) {
          setDecoded(middlewareInfo.userInfo);
        }
      }
    };

    if (authState.isAuthenticated && !decoded) {
      fetchToken();
    }
  }, [authState, decoded, middlewareInfo]);

  // Handle email verification redirect (keeping your existing logic)
  useEffect(() => {
    if (!authState.isAuthenticated || !decoded || !decoded.email_verified) {
      console.log('User is not authenticated or email is not verified');
      return;
    }

    console.log('Auth State:', {
      isAuthenticated: authState.isAuthenticated,
      user: authState.user,
      currentPath: pathname,
      decodedToken: decoded,
      middlewareInfo,
    });

    // Skip redirects for these paths (keeping your existing logic)
    if (pathname === '/dev' || pathname?.startsWith('/professional')) {
      return;
    }

    // Only handle email verification, skip profile completion for all users
    if (decoded && !decoded.email_verified) {
      router.push(
        `/verify-otp?email=${encodeURIComponent(
          decoded.email
        )}&name=${encodeURIComponent(decoded.name || '')}&accountType=${
          decoded.accountType || 'PUBLIC'
        }`
      );
    }
    // Profile completion redirect is now disabled for both roles
  }, [authState, decoded, pathname, router, middlewareInfo]);

  // Special case for the /dev route and professional section - bypass authentication
  // (keeping your existing logic)
  if (
    pathname === '/dev' ||
    (pathname && pathname.startsWith('/professional'))
  ) {
    return <>{children}</>;
  }

  // Enhanced loading state - consider middleware info
  if (authState.isLoading || sessionQuery.isLoading) {
    return <FullPageLoader open={true} />;
  }

  // Enhanced authentication check - use middleware info as backup
  // const isAuthenticated = authState.isAuthenticated || middlewareInfo?.userAuthenticated;
  const hasValidSession = !sessionQuery.isError || middlewareInfo?.sessionValid;

  if (hasValidSession) {
    return <>{children}</>;
  }

  // If middleware indicates session is invalid, show loading while redirect happens
  if (middlewareInfo?.sessionValid === false) {
    console.log('Middleware indicates invalid session, redirecting...');
    return <FullPageLoader open={true} />;
  }

  return null;
};

export const RedirectIfAuthenticated: React.FC<RouteGuardProps> = ({
  children,
  disableRedirect = false,
}) => {
  const { authState } = useAuth();
  const sessionQuery = useVerifySession();
  const router = useRouter();
  const [hasRedirected, setHasRedirected] = useState(false);
  const [middlewareInfo, setMiddlewareInfo] = useState<any>(null);

  // Get middleware session info on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const info = getMiddlewareSessionInfo();
      setMiddlewareInfo(info);
    }
  }, []);

  useEffect(() => {
    const handleRedirect = async () => {
      // Prevent multiple redirects
      if (hasRedirected) return;

      // If middleware already handled redirect, don't do it again
      if (middlewareInfo?.userAuthenticated && !disableRedirect) {
        return; // Let middleware handle it
      }

      // Wait for session verification to complete successfully
      if (sessionQuery.isLoading || sessionQuery.isError) {
        return;
      }

      try {
        const decodedToken = await getDecodedToken();

        if (decodedToken && !disableRedirect) {
          setHasRedirected(true);
          router.push('/feed');
        }
      } catch (error) {
        console.error('Error in redirect check:', error);

        // Use middleware info as fallback
        if (middlewareInfo?.userAuthenticated && !disableRedirect) {
          setHasRedirected(true);
          router.push('/feed');
        }
      }
    };

    if (
      !authState.isLoading &&
      authState.isAuthenticated &&
      !sessionQuery.isLoading &&
      !sessionQuery.isError &&
      !hasRedirected
    ) {
      handleRedirect();
    }
  }, [
    authState,
    sessionQuery.isLoading,
    sessionQuery.isError,
    router,
    disableRedirect,
    hasRedirected,
    middlewareInfo,
  ]);

  // Enhanced loading state
  if (authState.isLoading || sessionQuery.isLoading) {
    return disableRedirect ? <>{children}</> : <FullPageLoader open={true} />;
  }

  // Enhanced authentication check
  const isAuthenticated =
    authState.isAuthenticated || middlewareInfo?.userAuthenticated;
  const hasValidSession = !sessionQuery.isError || middlewareInfo?.sessionValid;

  if (!isAuthenticated || sessionQuery.isError) {
    return <>{children}</>;
  }

  // If authenticated with valid session, show loader while redirect happens
  if (isAuthenticated && hasValidSession && !disableRedirect) {
    return <FullPageLoader open={true} />;
  }

  return null;
};

export const PatientProfileRedirect: React.FC<RouteGuardProps> = ({
  children,
}) => {
  const { authState } = useAuth();
  const sessionQuery = useVerifySession();
  const router = useRouter();
  const [middlewareInfo, setMiddlewareInfo] = useState<any>(null);

  // Get middleware session info on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const info = getMiddlewareSessionInfo();
      setMiddlewareInfo(info);
    }
  }, []);

  useEffect(() => {
    const handleRedirect = async () => {
      try {
        const decodedToken = await getDecodedToken();

        if (decodedToken?.currentStage === undefined) {
          router.push(
            `/verify-otp?email=${encodeURIComponent(
              decodedToken?.email || ''
            )}&name=${encodeURIComponent(decodedToken?.name || '')}`
          );
        } else if (decodedToken?.currentStage === 'completed') {
          router.push('/feed');
        }
      } catch (error) {
        console.error('Error in patient profile redirect:', error);

        // Use middleware info as fallback
        if (middlewareInfo?.userInfo) {
          const userInfo = middlewareInfo.userInfo;
          if (userInfo.currentStage === undefined) {
            router.push(
              `/verify-otp?email=${encodeURIComponent(
                userInfo.email || ''
              )}&name=${encodeURIComponent(userInfo.name || '')}`
            );
          } else if (userInfo.currentStage === 'completed') {
            router.push('/feed');
          }
        }
      }
    };

    if (
      !authState.isLoading &&
      authState.isAuthenticated &&
      !sessionQuery.isLoading
    ) {
      handleRedirect();
    }

    // Commenting to stop going to the OTP page
    // if (!authState.isAuthenticated && !authState.isLoading) {
    //   router.push('/signup/patient');
    // }
  }, [authState, sessionQuery, router, middlewareInfo]);

  if (authState.isLoading || sessionQuery.isLoading) {
    return <FullPageLoader open={true} />;
  }

  return <>{children}</>;
};

export const VerifyOTPRedirect: React.FC<RouteGuardProps> = ({ children }) => {
  const { authState } = useAuth();
  const sessionQuery = useVerifySession();
  const router = useRouter();
  const pathname = usePathname();
  const isVerifyOtpPage = pathname?.startsWith('/verify-otp');
  const [middlewareInfo, setMiddlewareInfo] = useState<any>(null);

  // Get middleware session info on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const info = getMiddlewareSessionInfo();
      setMiddlewareInfo(info);
    }
  }, []);

  useEffect(() => {
    const handleRedirect = async () => {
      try {
        const decodedToken = await getDecodedToken();

        if (decodedToken?.currentStage === undefined) {
          router.push(
            `/verify-otp?email=${encodeURIComponent(
              decodedToken?.email || ''
            )}&name=${encodeURIComponent(decodedToken?.name || '')}`
          );
        } else if (decodedToken?.currentStage === 'completed') {
          // Keep your existing logic - commented out redirect
          // router.push('/feed');
        }
      } catch (error) {
        console.error('Error in verify OTP redirect:', error);

        // Use middleware info as fallback
        if (middlewareInfo?.userInfo) {
          const userInfo = middlewareInfo.userInfo;
          if (userInfo.currentStage === undefined) {
            router.push(
              `/verify-otp?email=${encodeURIComponent(
                userInfo.email || ''
              )}&name=${encodeURIComponent(userInfo.name || '')}`
            );
          }
        }
      }
    };

    if (
      !authState.isLoading &&
      authState.isAuthenticated &&
      !sessionQuery.isLoading
    ) {
      handleRedirect();
    }
  }, [authState, sessionQuery, router, middlewareInfo]);

  // Don't show loader on verify-otp page as it handles its own loading state
  if ((authState.isLoading || sessionQuery.isLoading) && !isVerifyOtpPage) {
    return <FullPageLoader open={true} />;
  }

  return <>{children}</>;
};
