import React, { useState } from 'react';
import { Box, Typography, Button, SxProps } from '@mui/material';
import DeleteIcon from '../../Icons/DeleteIcon';
import YearSelect from '../../form-fields/YearSelect';
import TextInput from '../../form-fields/TextInput';

interface Field {
  label: string;
  placeholder: string;
  sx?: object;
  value?: string;
  type?: string;
  optional?: boolean;
}

interface Row {
  columns: number;
  fields: Field[];
}

interface CustomFormProps {
  title?: string;
  subtitles?: string;
  rows: Row[];
  addButtonLabel: string;
  sx?: SxProps;
}

const CustomForm = ({
  title,
  subtitles,
  rows,
  addButtonLabel,
  sx,
}: CustomFormProps) => {
  const [formRowsList, setFormRowsList] = useState<Row[][]>([rows]);

  // Check if all current field values are filled
  const isCurrentFormComplete = formRowsList[formRowsList.length - 1].every(
    (row) =>
      row.fields.every(
        (field) => field.optional || (field.value && field.value.trim() !== '')
      )
  );

  const handleInputChange = (
    formIndex: number,
    rowIndex: number,
    fieldIndex: number,
    value: string
  ) => {
    setFormRowsList((prev) =>
      prev.map((formRows, fIdx) =>
        fIdx === formIndex
          ? formRows.map((row, rIdx) =>
              rIdx === rowIndex
                ? {
                    ...row,
                    fields: row.fields.map((field, fldIdx) =>
                      fldIdx === fieldIndex ? { ...field, value } : field
                    ),
                  }
                : row
            )
          : formRows
      )
    );
  };

  const handleAddForm = () => {
    const clonedRows = structuredClone(rows);
    setFormRowsList([...formRowsList, clonedRows]);
  };

  return (
    <Box
      p={{ xs: '0px', sm: '20px' }}
      mb={{ xs: '16px', sm: '0px' }}
      sx={{
        ...sx,
      }}
    >
      {/* Title */}
      {title && (
        <Typography fontSize={'20px'} fontWeight={600} mb="40px">
          {title}
        </Typography>
      )}

      {subtitles && (
        <Typography fontSize={'16px'} fontWeight={400} mb="40px">
          {subtitles}
        </Typography>
      )}

      {formRowsList.map((formRows, formIndex) => (
        <Box
          key={formIndex}
          display="flex"
          flexDirection="row"
          justifyContent="space-between"
          alignItems="flex-start"
          gap={{ xs: '12px', sm: '20px' }}
          mb={formIndex === formRowsList.length - 1 ? 0 : 4}
        >
          {/* Delete icon */}
          <DeleteIcon fill="#A3A3A3" />

          {/* Form grid layout */}
          <Box flex={1}>
            {formRows.map((row, rowIndex) => (
              <Box
                key={rowIndex}
                display="grid"
                gap={{ xs: '20px', sm: '48px' }}
                mb={{ xs: '20px', sm: '48px' }}
                sx={{
                  gridTemplateColumns: {
                    xs: '1fr',
                    md:
                      row.columns === 1
                        ? '1fr'
                        : row.columns === 2
                        ? '1fr 1fr'
                        : '1fr 1fr 1fr',
                  },
                }}
              >
                {row.fields.map((field, idx) => (
                  <Box key={idx}>
                    {field.type === 'year' ? (
                      <YearSelect
                        label={field.label}
                        placeholder={field.placeholder}
                        value={field.value || ''}
                        onChange={(val) =>
                          handleInputChange(formIndex, rowIndex, idx, val)
                        }
                        sx={field.sx}
                      />
                    ) : (
                      <TextInput
                        label={field.label}
                        placeholder={field.placeholder}
                        value={field.value || ''}
                        onChange={(val) =>
                          handleInputChange(formIndex, rowIndex, idx, val)
                        }
                        sx={field.sx}
                      />
                    )}
                  </Box>
                ))}
              </Box>
            ))}
          </Box>
        </Box>
      ))}

      {/* + Add Degree Button */}
      <Button
        onClick={handleAddForm}
        disabled={!isCurrentFormComplete}
        sx={{
          color: isCurrentFormComplete ? '#A24295' : '#A3A3A3',
          fontWeight: 600,
          fontSize: '16px',
          textTransform: 'none',
          p: 0,
          background: 'transparent',
          '&:hover': {
            backgroundColor: 'transparent',
            textDecoration: isCurrentFormComplete ? 'underline' : 'none',
          },
          mb: { xs: '16px', sm: '0px' },
        }}
      >
        + {addButtonLabel}
      </Button>
    </Box>
  );
};

export default React.memo(CustomForm);
