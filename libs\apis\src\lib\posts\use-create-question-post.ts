import { useMutation } from '@tanstack/react-query';
import { postApi, CreateTextPostRequest } from './post-api.js';
import { toast } from 'react-toastify';

interface ApiResponse {
  data: unknown;
}

interface UseCreateQuestionPostOptions {
  onSuccess?: (data: ApiResponse) => void;
  onError?: (error: Error) => void;
}

export function useCreateQuestionPost(options?: UseCreateQuestionPostOptions) {
  return useMutation({
    mutationFn: async (data: CreateTextPostRequest) => {
      try {
        const response = await postApi.createQuestionPost(data);
        return response;
      } catch (error) {
        console.error('Error creating question post:', error);
        throw error;
      }
    },
    onSuccess: (data: ApiResponse) => {
      toast.success('Question post created successfully!');
      options?.onSuccess?.(data);
    },
    onError: (error: unknown) => {
      console.error('Error creating question post:', error);
      const errorMessage =
        error &&
        typeof error === 'object' &&
        'response' in error &&
        error.response &&
        typeof error.response === 'object' &&
        'data' in error.response &&
        error.response.data &&
        typeof error.response.data === 'object' &&
        'message' in error.response.data &&
        typeof error.response.data.message === 'string'
          ? error.response.data.message
          : 'Failed to create question post';
      toast.error(errorMessage);
      if (error instanceof Error) {
        options?.onError?.(error);
      } else {
        options?.onError?.(new Error(errorMessage));
      }
    },
  });
}
