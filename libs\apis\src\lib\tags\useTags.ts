import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-toastify';
import { postApi } from '../posts/post-api.js';

export type UseTagsOptions = {
  query?: string;
  limit?: number;
  enabled?: boolean;
};

export type UseFollowingTagsOptions = {
  limit?: number;
  offset?: number;
  searchKeyword?: string;
  enabled?: boolean;
};

export type UseSuggestedTagsOptions = {
  limit?: number;
  offset?: number;
  searchKeyword?: string;
  enabled?: boolean;
};

export function useSearchTags(options: UseTagsOptions = {}) {
  const { query = '', limit = 10, enabled = true } = options;

  return useQuery({
    queryKey: ['tags', { query, limit }],
    queryFn: async () => {
      try {
        const tags = await postApi.getTags({ query, limit });
        return tags.data;
      } catch (error) {
        console.error('Failed to fetch tags:', error);
        toast.error(
          error instanceof Error
            ? error.message || 'Something went wrong while loading tags.'
            : 'Something went wrong while loading tags.'
        );
        throw error;
      }
    },
    enabled,
  });
}

export function useFollowTag() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (tagId: string) => {
      try {
        return await postApi.followTag(tagId);
      } catch (error) {
        console.error('Failed to follow tag:', error);
        toast.error(
          error instanceof Error
            ? error.message || 'Something went wrong while following tag.'
            : 'Something went wrong while following tag.'
        );
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tags'] });
      queryClient.invalidateQueries({ queryKey: ['following-tags'] });
    },
  });
}

export function useUnfollowTag() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (tagId: string) => {
      try {
        return await postApi.unfollowTag(tagId);
      } catch (error) {
        console.error('Failed to unfollow tag:', error);
        toast.error(
          error instanceof Error
            ? error.message || 'Something went wrong while unfollowing tag.'
            : 'Something went wrong while unfollowing tag.'
        );
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tags'] });
      queryClient.invalidateQueries({ queryKey: ['following-tags'] });
    },
  });
}

export function useFollowingTagsWithPosts(
  options: UseFollowingTagsOptions = {}
) {
  const {
    limit = 10,
    offset = 0,
    searchKeyword = '',
    enabled = true,
  } = options;

  return useQuery({
    queryKey: ['following-tags', { limit, offset, searchKeyword }],
    queryFn: async () => {
      try {
        const data = await postApi.getFollowingTagsWithPosts({
          limit,
          offset,
          searchKeyword,
        });
        return data.data;
      } catch (error) {
        console.error('Failed to fetch following tags with posts:', error);
        toast.error(
          error instanceof Error
            ? error.message ||
                'Something went wrong while loading followed tags.'
            : 'Something went wrong while loading followed tags.'
        );
        throw error;
      }
    },
    enabled,
  });
}

export function useSuggestedTagsWithPosts(
  options: UseSuggestedTagsOptions = {}
) {
  const {
    limit = 10,
    offset = 0,
    searchKeyword = '',
    enabled = true,
  } = options;

  return useQuery({
    queryKey: ['suggested-tags', { limit, offset, searchKeyword }],
    queryFn: async () => {
      try {
        const data = await postApi.getSuggestedTagsWithPosts({
          limit,
          offset,
          searchKeyword,
        });
        return data.data;
      } catch (error) {
        console.error('Failed to fetch suggested tags with posts:', error);
        toast.error(
          error instanceof Error
            ? error.message ||
                'Something went wrong while loading suggested tags.'
            : 'Something went wrong while loading suggested tags.'
        );
        throw error;
      }
    },
    enabled,
  });
}
