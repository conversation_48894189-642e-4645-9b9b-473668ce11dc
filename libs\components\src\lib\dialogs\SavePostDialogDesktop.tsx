'use client';

import { useState } from 'react';
import {
  Box,
  Dialog,
  DialogContent,
  Typography,
  TextField,
  Button,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import Checkbox from '../checkbox/Checkbox';
import PostCard from '../content-posts/PostCard';
import { useTranslations } from 'next-intl';

const folders = [
  'Important Articles',
  'Surgery',
  'New Procedures',
  'Cardiology',
  'Patient Notes',
  'Weekly Reviews',
  'Case Studies',
  'Quick Reads',
];

const SavePostDialog = ({
  open,
  onClose,
}: {
  open: boolean;
  onClose: () => void;
}) => {
  const t = useTranslations('savePost');

  const [newFolderName, setNewFolderName] = useState('');
  const [selectedFolders, setSelectedFolders] = useState<string[]>([]);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const handleToggleFolder = (folder: string) => {
    setSelectedFolders((prev) =>
      prev.includes(folder)
        ? prev.filter((item) => item !== folder)
        : [...prev, folder]
    );
  };

  const canSave = newFolderName.trim() !== '' || selectedFolders.length > 0;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          width: '100%',
          maxWidth: '766px',
          maxHeight: '512px',
          borderRadius: '12px',
          padding: '40px',
          backgroundColor: '#fff',
        },
      }}
    >
      <DialogContent sx={{ padding: 0 }}>
        {/* Header */}
        <Typography
          fontSize="28px"
          fontWeight={500}
          color="#1E1E1E"
          fontFamily="Plus Jakarta Sans"
        >
          {t('title')}
        </Typography>

        {/* Middle Section */}
        <Box
          mt="40px"
          display="flex"
          flexDirection={isMobile ? 'column' : 'row'}
          justifyContent="space-between"
          gap="20px"
        >
          <Box
            sx={{
              border: '1px solid #F8F9FA',
              borderRadius: '8px',
              boxShadow:
                '0px 4px 20px rgba(0, 0, 0, 0.1), 0px 0px 10px rgba(163, 163, 163, 0.2)',
            }}
          >
            <PostCard
              post={{
                postId: 'post123',
                content: 'Check out this amazing sunset over the mountains!',
                postType: 'image',
                workspaceId: 'workspace456',
                publisherName: 'John Doe',
                username: 'johndoe',
                profileImageUrlThumbnail:
                  'https://randomuser.me/api/portraits/men/32.jpg',
                medias: [
                  {
                    mediaPath:
                      'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=700&q=80',
                    mediaType: 'image',
                    altText: 'Sunset over mountains',
                  },
                ],
              }}
            />
          </Box>

          {/* Folder List */}
          <Box
            flex={1}
            maxHeight="166px"
            overflow="auto"
            padding="16px"
            borderRadius="8px"
            border="1px solid #A3A3A3"
            sx={{
              '&::-webkit-scrollbar': { display: 'none' },
              '-ms-overflow-style': 'none',
              'scrollbar-width': 'none',
            }}
          >
            <Box display="flex" flexWrap="wrap" gap="16px">
              {folders.map((folder) => (
                <Checkbox
                  key={folder}
                  label={folder}
                  checked={selectedFolders.includes(folder)}
                  onToggle={() => handleToggleFolder(folder)}
                />
              ))}
            </Box>
          </Box>
        </Box>

        {/* Add Folder Section */}
        <Box
          mt="40px"
          display="flex"
          justifyContent="flex-end"
          gap="16px"
          alignItems="center"
        >
          <TextField
            label={t('folderLabel')}
            placeholder={t('folderPlaceholder')}
            value={newFolderName}
            onChange={(e) => setNewFolderName(e.target.value)}
            InputLabelProps={{ shrink: true }}
            sx={{ width: '256px' }}
          />
          <Button
            variant="outlined"
            onClick={onClose}
            sx={{
              width: '156px',
              height: '40px',
              fontWeight: 700,
              borderColor: '#A24295',
              color: '#A24295',
              '&:hover': {
                backgroundColor: '#f9f0f5',
              },
            }}
          >
            {t('addFolder')}
          </Button>
        </Box>

        {/* Footer Buttons */}
        <Box display="flex" justifyContent="center" mt="40px" gap="20px">
          <Button
            variant="outlined"
            onClick={onClose}
            sx={{
              width: '156px',
              height: '40px',
              fontWeight: 700,
              borderColor: '#A24295',
              color: '#A24295',
              '&:hover': {
                backgroundColor: '#f9f0f5',
              },
            }}
          >
            {t('cancel')}
          </Button>
          <Button
            variant="contained"
            onClick={onClose}
            disabled={!canSave}
            sx={{
              width: '156px',
              height: '40px',
              fontWeight: 700,
              backgroundColor: canSave ? '#A24295' : '#A3A3A3',
              color: '#fff',
              '&:hover': {
                backgroundColor: canSave ? '#932080' : '#A3A3A3',
              },
            }}
          >
            {t('save')}
          </Button>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default SavePostDialog;
