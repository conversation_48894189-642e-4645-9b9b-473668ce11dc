'use client';

import { useRouter } from 'next/navigation';
import { Box, Card, Stack, Typography } from '@mui/material';
import { TagPost } from '@minicardiac-client/apis';
import { Profile } from '../navigations/Profile';
import {
  generateSlug,
  getCdnUrl,
  removeHtml,
} from '@minicardiac-client/utilities';

export default function PostCard({ post }: { post: TagPost }) {
  const {
    postId,
    content,
    postType,
    publisherName,
    profileImageUrlThumbnail,
    medias = [],
  } = post;

  const router = useRouter();

  const handleClick = (e: React.MouseEvent) => {
    // Generate slug
    const slug = generateSlug(content);

    router.push(`/feed/${postType}/${postId}/${slug}`);
  };

  return (
    <Card
      onClick={(e) => {
        e.stopPropagation();
        handleClick(e);
      }}
      sx={{
        width: '205px',
        minWidth: '205px',
        minHeight: '166px',
        borderRadius: '8px',
        boxShadow: 'none',
        backgroundColor: 'white',
        padding: '12px',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        gap: '8px',
        cursor: 'pointer',
        ':hover': {
          scale: 1.05,
        },
      }}
    >
      {/* Header */}
      <Stack direction="row" alignItems="center" spacing={1}>
        <Profile
          displayName={publisherName}
          photoURL={profileImageUrlThumbnail}
          size={24}
        />
        <Typography fontSize="12px" fontWeight={600}>
          {publisherName || 'Roger Taylor'}
        </Typography>
      </Stack>

      {/* Content */}
      <Typography
        fontSize="12px"
        fontWeight={400}
        sx={{
          display: '-webkit-box',
          WebkitLineClamp: 2,
          WebkitBoxOrient: 'vertical',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        }}
      >
        {removeHtml(content)}
      </Typography>

      {/* Image Preview Strip */}
      <Box
        sx={{
          display: 'flex',
          overflow: 'hidden',
          position: 'relative',
          mt: '4px',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            gap: '12px',
            overflowX: 'auto',
            pr: '16px',
            '&::-webkit-scrollbar': {
              display: 'none',
            },
          }}
        >
          {medias?.length > 0 &&
            medias.map((img, i) => (
              <Box
                key={i}
                component="img"
                src={getCdnUrl(img.mediaPath)}
                alt={`img-${i}`}
                sx={{
                  width: '70px',
                  height: '66px',
                  objectFit: 'cover',
                  borderRadius: '4px',
                  flexShrink: 0,
                }}
              />
            ))}
        </Box>

        {/* Fade effect */}
        <Box
          sx={{
            position: 'absolute',
            right: -1,
            top: 0,
            bottom: 0,
            width: '20px',
            background: `linear-gradient(to right, rgba(255,255,255,0) 0%, #F8F9FA 80%)`,
            pointerEvents: 'none',
          }}
        />
      </Box>
    </Card>
  );
}
