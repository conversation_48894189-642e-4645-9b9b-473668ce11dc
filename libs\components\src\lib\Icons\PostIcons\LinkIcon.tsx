const LinkIcon = ({
    fill = '#A3A3A3',
    hoverFill = '#A24295',
    size = 28,
    useGradient = true,
  }) => {
    const gradient1 = 'url(#paint0_linear_8285_16756)';
    const gradient2 = 'url(#paint1_linear_8285_16756)';
  
    return (
      <svg
        width={size}
        height={size}
        viewBox="0 0 28 28"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M21.9019 16.5314L25.8444 12.5882C28.7206 9.71273 28.7206 5.03284 25.8444 2.15733C22.9686 -0.71911 18.289 -0.71911 15.4132 2.15733L9.7235 7.84706C6.84737 10.7226 6.84737 15.4025 9.7235 18.278C10.1482 18.7029 10.6129 19.0632 11.1048 19.3625L14.1508 16.3162C13.5706 16.1774 13.0202 15.8847 12.5685 15.4333C11.2613 14.126 11.2613 11.9987 12.5685 10.6918L18.2583 5.00204C19.5655 3.69479 21.6928 3.69479 22.9997 5.00204C24.307 6.30928 24.307 8.43628 22.9997 9.74353L21.3282 11.4154C22.017 13.0365 22.2074 14.8195 21.9019 16.5314Z"
          fill={useGradient ? gradient1 : fill}
        />
        <path
          d="M6.09991 11.4677L2.15733 15.4106C-0.71911 18.2865 -0.71911 22.966 2.15733 25.8419C5.03284 28.718 9.71273 28.718 12.5885 25.8419L18.278 20.1521C21.1541 17.2763 21.1538 12.5967 18.278 9.72122C17.8536 9.29625 17.3888 8.936 16.897 8.63672L13.851 11.683C14.4309 11.8221 14.9812 12.1142 15.4333 12.5659C16.7405 13.8732 16.7405 16.0002 15.4333 17.3074L9.74322 22.9971C8.43597 24.3044 6.30897 24.3044 5.00173 22.9971C3.69448 21.6899 3.69448 19.5629 5.00173 18.2557L6.67358 16.5841C5.9845 14.963 5.7941 13.1797 6.09991 11.4677Z"
          fill={useGradient ? gradient2 : fill}
        />
        <defs>
          <linearGradient
            id="paint0_linear_8285_16756"
            x1="17.784"
            y1="0"
            x2="17.784"
            y2="19.3625"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#F92243" />
            <stop offset="1" stopColor="#A24295" />
          </linearGradient>
          <linearGradient
            id="paint1_linear_8285_16756"
            x1="10.2175"
            y1="8.63672"
            x2="10.2175"
            y2="27.9989"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#F92243" />
            <stop offset="1" stopColor="#A24295" />
          </linearGradient>
        </defs>
      </svg>
    );
  };
  
  export default LinkIcon;
  