{"name": "@minicardiac-client/admin", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/admin/src", "projectType": "application", "targets": {"test": {"executor": "@nx/vite:test", "outputs": ["{workspaceRoot}/coverage/apps/admin"], "options": {"config": "apps/admin/vite.config.ts"}}, "deploy": {"executor": "nx:run-commands", "options": {"command": "aws s3 sync apps/admin/dist s3://$BUCKET_NAME/ --delete && aws cloudfront create-invalidation --distribution-id $DISTRIBUTION_ID --paths '/*'"}}}}