'use client';

import { useParams } from 'next/navigation';

import { FullPageLoader } from '@minicardiac-client/components';
import { useAuth } from '@minicardiac-client/apis';

import PostDetailLayout from '@/libs/components/src/lib/content-posts/detail-post/PostDetailLayout';
import { usePostById } from '@/libs/apis/src/lib/posts/use-feed';
import Head from '@/libs/components/src/lib/head/Head';
import { ContentLinkPostFull } from '@/libs/components/src/lib/link-post/ContentLinkPostFull';

// Mock other posts data - in real app this would come from API
const otherLinkPosts = [
  {
    id: '1',
    thumbnail:
      'https://images.unsplash.com/photo-1611162617474-5b21e879e113?w=300&h=200&fit=crop',
    title: 'Innovative Surgical Techniques',
  },
  {
    id: '2',
    thumbnail:
      'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=300&h=200&fit=crop',
    title: 'Medical Research Advances',
  },
  {
    id: '3',
    thumbnail:
      'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=300&h=200&fit=crop',
    title: 'Healthcare Technology Updates',
  },
];

export default function LinkPostPageWrapper() {
  const { authState } = useAuth();
  const params = useParams();

  const postId = params?.id as string;

  const { data: post, isLoading } = usePostById(postId);

  if (authState.isLoading || isLoading) {
    return <FullPageLoader open={true} />;
  }

  if (!post) {
    return (
      <div style={{ padding: '2rem', textAlign: 'center', fontSize: '1.2rem' }}>
        Invalid post ID. No post found.
      </div>
    );
  }

  return (
    <>
      <Head
        title={
          post.linkPreview?.title || post.linkPreview?.domain || 'Link Post'
        }
      />

      <PostDetailLayout
        user={{
          name: post.publisherName || '',
          profilePic: post.profileImageUrlThumbnail || '',
          postedAgo: post.postedAt || '',
        }}
        sidebarItems={otherLinkPosts}
        sidebarTitle="Other Link Posts"
        postId={postId}
        post={post}
      >
        <ContentLinkPostFull
          content={post.content}
          linkUrl={post.linkUrl}
          linkPreview={post.linkPreview}
        />
      </PostDetailLayout>
    </>
  );
}
