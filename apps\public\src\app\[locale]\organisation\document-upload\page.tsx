'use client';

import React from 'react';
import { Box } from '@mui/material';
import { useRouter } from '@/apps/public/src/i18n/navigation';
import {
  useSnackbar,
  DocumentUploadPageTemplate,
  ActionButtonsTemplate,
} from '@minicardiac-client/components';
import DocumentUploadForm from '@/libs/components/src/lib/onboarding/components/Documents/DocumentUpload';
import {
  useAuth,
  uploadDocuments,
  refreshSession,
} from '@minicardiac-client/apis';

export default function OrganisationDocumentUploadPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const { showSuccess, showError } = useSnackbar();
  const { authState } = useAuth();

  const handleDoThisLater = () => {
    router.push('/feed?fromSignup=true');
  };

  const handleContinue = async () => {
    setIsSubmitting(true);
    setError(null);

    try {
      await refreshSession();
      await uploadDocuments();

      showSuccess('Documents saved successfully!');
      setTimeout(() => {
        router.push('/organisation/subscription/paid/add-network');
      }, 1000);
    } catch (err: any) {
      console.error('Error in document upload process:', err);

      const errorMsg =
        err.message?.includes('session') || err.message?.includes('auth')
          ? 'Session expired. Please sign in again.'
          : err.message || 'Failed to save document data';

      setError(errorMsg);
      showError(errorMsg);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <DocumentUploadPageTemplate
      userName={authState.user?.displayName || ''}
      subtitleText="Let's set up your Organisation Account!"
      showBackButton={true}
      onBack={() => router.back()}
      currentStep={1}
      steps={['Profile Setup', 'Document Upload', 'Adding Network']}
    >
      <DocumentUploadForm hideSteppers={true} />

      <ActionButtonsTemplate
        onSave={handleContinue}
        onSkip={handleDoThisLater}
        isSubmitting={isSubmitting}
        isValid={true}
        saveButtonText="Save and Continue"
        skipButtonText="Do this later"
        variant="organization"
      />

      {error && (
        <Box sx={{ color: 'error.main', mt: 2, textAlign: 'center' }}>
          {error}
        </Box>
      )}
    </DocumentUploadPageTemplate>
  );
}
