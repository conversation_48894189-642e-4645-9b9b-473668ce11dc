// import { test, expect } from '@playwright/test';

// // Skip this test on Firefox and Chromium for now will do in future but
// // test.skip(
// //   ({ browserName }) => browserName === 'firefox' || browserName === 'chromium',
// //   'Skip on Firefox and Chromium for now'
// // );

// const searchInputSelector = 'input[placeholder="Search by keyword, poster, or tag"]';

// // Fix: Remove unnecessary escape
// const searchInputSelectorFixed = 'input[placeholder="Search by keyword, poster, or tag"]'.replace(/\\"/g, '"');

// // Helper to wait for input value
// async function waitForInputValue(searchInput: import('@playwright/test').Locator, value: string) {
//   await expect(searchInput).toHaveValue(value, { timeout: 2000 });
// }

// test('Feed Search: does not show error on valid search', async ({ page }) => {
//   await page.goto('http://localhost:3000/en/feed');
//   await expect(page.locator('.MuiCircularProgress-root')).not.toBeVisible({ timeout: 15000 });
//   const searchInput = page.locator(searchInputSelectorFixed);
//   await searchInput.fill('test');
//   await waitForInputValue(searchInput, 'test');
//   const error = page.locator('text=/error|hydration|failed/i');
//   expect(await error.count()).toBe(0);
// });

// test('Feed Search: no results for nonsense keyword', async ({ page }) => {
//   await page.goto('http://localhost:3000/en/feed');
//   await expect(page.locator('.MuiCircularProgress-root')).not.toBeVisible({ timeout: 15000 });
//   const searchInput = page.locator(searchInputSelectorFixed);
//   await searchInput.fill('nonsensekeyword123');
//   await waitForInputValue(searchInput, 'nonsensekeyword123');
//   const noResults = page.locator('text=/no results/i');
//   expect(await noResults.count()).toBeGreaterThan(0);
//   const error = page.locator('text=/error|hydration|failed/i');
//   expect(await error.count()).toBe(0);
// });

// test('Feed Search: handles special characters', async ({ page }) => {
//   await page.goto('http://localhost:3000/en/feed');
//   await expect(page.locator('.MuiCircularProgress-root')).not.toBeVisible({ timeout: 15000 });
//   const searchInput = page.locator(searchInputSelectorFixed);
//   await searchInput.fill('!@#$%^&*()_+[]{}|;:\'\",./<>?');
//   await waitForInputValue(searchInput, '!@#$%^&*()_+[]{}|;:\'\",./<>?');
//   const error = page.locator('text=/error|hydration|failed/i');
//   expect(await error.count()).toBe(0);
// });

// test('Feed Search: handles only spaces', async ({ page }) => {
//   await page.goto('http://localhost:3000/en/feed');
//   await expect(page.locator('.MuiCircularProgress-root')).not.toBeVisible({ timeout: 15000 });
//   const searchInput = page.locator(searchInputSelectorFixed);
//   await searchInput.fill('     ');
//   await waitForInputValue(searchInput, '     ');
//   const error = page.locator('text=/error|hydration|failed/i');
//   expect(await error.count()).toBe(0);
// });

// test('Feed Search: handles very long string', async ({ page }) => {
//   await page.goto('http://localhost:3000/en/feed');
//   await expect(page.locator('.MuiCircularProgress-root')).not.toBeVisible({ timeout: 15000 });
//   const searchInput = page.locator(searchInputSelectorFixed);
//   const longString = 'a'.repeat(500);
//   await searchInput.fill(longString);
//   await waitForInputValue(searchInput, longString);
//   const error = page.locator('text=/error|hydration|failed/i');
//   expect(await error.count()).toBe(0);
// });

// test('Feed Search: handles unicode and emoji', async ({ page }) => {
//   await page.goto('http://localhost:3000/en/feed');
//   await expect(page.locator('.MuiCircularProgress-root')).not.toBeVisible({ timeout: 15000 });
//   const searchInput = page.locator(searchInputSelectorFixed);
//   await searchInput.fill('测试тест');
//   await waitForInputValue(searchInput, '测试тест');
//   const error = page.locator('text=/error|hydration|failed/i');
//   expect(await error.count()).toBe(0);
// });

// test('Feed Search: handles SQL injection-like input', async ({ page }) => {
//   await page.goto('http://localhost:3000/en/feed');
//   await expect(page.locator('.MuiCircularProgress-root')).not.toBeVisible({ timeout: 15000 });
//   const searchInput = page.locator(searchInputSelectorFixed);
//   const sqlString = "'; DROP TABLE users; --";
//   await searchInput.fill(sqlString);
//   await waitForInputValue(searchInput, sqlString);
//   const error = page.locator('text=/error|hydration|failed/i');
//   expect(await error.count()).toBe(0);
// });

import { test, expect } from '@playwright/test';

// Skip this test on Firefox and Chromium for now will do in future but
// test.skip(
//   ({ browserName }) => browserName === 'firefox' || browserName === 'chromium',
//   'Skip on Firefox and Chromium for now'
// );

test('Feed Search: does not show error on valid search', async ({ page }) => {
  await page.goto('http://localhost:3000/en/feed');
  await page.waitForSelector('.MuiCircularProgress-root', {
    state: 'detached',
    timeout: 15000,
  });

  const searchInput = page.locator(
    'input[placeholder="Search by keyword, poster, or tag"]'
  );
  await expect(searchInput).toBeVisible();

  await searchInput.fill('test');
  await page.waitForTimeout(1000);

  // Assert that no error message is visible
  const error = page.locator('text=/error|hydration|failed/i');
  expect(await error.count()).toBe(0);
});
