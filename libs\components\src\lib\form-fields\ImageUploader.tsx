import React, { useEffect, useRef, useState } from 'react';
import {
  Box,
  Typography,
  useTheme,
  useMediaQuery,
  SxProps,
  Theme,
} from '@mui/material';
import MediaUploadIcon from '../Icons/FeedIcons/MediaUploadIcon';

interface ImageUploaderProps {
  thumbnail: File | null;
  onFileChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  label?: string;
  subtitles?: string;
  sx?: SxProps<Theme>;
}

const ImageUploader: React.FC<ImageUploaderProps> = ({
  thumbnail,
  onFileChange,
  label = 'Click to upload image',
  subtitles = 'Or drag and drop',
  sx,
}) => {
  const theme: any = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragging, setIsDragging] = useState(false);

  useEffect(() => {
    if (thumbnail) {
      const objectUrl = URL.createObjectURL(thumbnail);
      setPreviewUrl(objectUrl);

      return () => {
        URL.revokeObjectURL(objectUrl);
      };
    } else {
      setPreviewUrl(null);
      return;
    }
  }, [thumbnail]);

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDragging(false);
    if (event.dataTransfer.files?.[0]) {
      const file = event.dataTransfer.files[0];

      const fakeInputEvent = {
        target: {
          files: [file],
        },
      } as unknown as React.ChangeEvent<HTMLInputElement>;

      onFileChange?.(fakeInputEvent);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  return (
    <Box
      width={{ xs: '100%', md: '500px' }}
      height={{ xs: '196px', sm: '335px' }}
      border={`1px solid ${
        isDragging ? theme.palette.primary.main : theme.palette.secondary.main
      }`}
      borderRadius="8px"
      display="flex"
      justifyContent="center"
      alignItems="center"
      position="relative"
      sx={{
        cursor: 'pointer',
        overflow: 'hidden',
        backgroundColor: isDragging
          ? theme.palette.action.hover
          : 'transparent',
        transition: 'background-color 0.2s ease',
        ...sx,
      }}
      onClick={handleClick}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
    >
      <input
        type="file"
        accept="image/*"
        ref={fileInputRef}
        onChange={onFileChange}
        style={{ display: 'none' }}
      />

      {thumbnail ? (
        <Box
          component="img"
          src={previewUrl || ''}
          alt="Thumbnail"
          sx={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
          }}
        />
      ) : (
        <Box
          textAlign="center"
          display="flex"
          flexDirection="column"
          alignItems="center"
        >
          <MediaUploadIcon size={52} />
          <Typography
            fontSize={{ xs: '16px', sm: '20px' }}
            mt="12px"
            fontWeight={500}
          >
            {isMobile ? 'Tap to upload image' : label}
          </Typography>
          {!isMobile && (
            <Typography
              fontSize="14px"
              mt="4px"
              color={theme.palette.neutral?.[500] || '#757575'}
            >
              {subtitles}
            </Typography>
          )}
        </Box>
      )}
    </Box>
  );
};

export default ImageUploader;
