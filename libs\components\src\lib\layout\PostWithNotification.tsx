'use client';

import React from 'react';
import { NotificationBanner } from '../common/NotificationBanner';

interface PostWithNotificationProps {
  children: React.ReactNode;
  tags?: string[];
  postId?: string;
}

export const PostWithNotification: React.FC<PostWithNotificationProps> = ({ 
  children, 
  tags = [], 
  postId 
}) => {
  const hasPublicForumTag = tags.includes('PublicForum');

  return (
    <>
      {hasPublicForumTag && (
        <NotificationBanner
          message="Your post has been posted to #PublicForum. This means that it will only be visible to other users within this tag and not on their main feed. Please upgrade your plan if you would like to change this."
          width="100%"
          showCloseButton={true}
          onClose={() => console.log('Notification closed for post:', postId)}
          sx={{ mb: 1 }}
        />
      )}
      {children}
    </>
  );
};

export default PostWithNotification;
