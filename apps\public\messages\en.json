{"linkPost": {"comments": "Comments", "like": "Like", "repost": "Repost", "share": "Share"}, "signin": {"title": "Welcome to MiniCardiac", "subtitle": "The heart of cardiac healthcare", "welcomeTo": "Welcome to", "emailLabel": "Email", "passwordLabel": "Password", "forgotPassword": "Forgot Password?", "continue": "Continue", "signIn": "Sign in", "signUp": "Sign up", "or": "OR", "google": "Continue with Google", "apple": "Continue with Apple"}, "signup": {"title": "Welcome to MiniCardiac", "subtitle": "The heart of cardiac healthcare", "welcomeTo": "Welcome to", "welcomeBackTo": "Welcome back to", "displayName": "Display Name", "name": "Name", "emailLabel": "Email", "passwordLabel": "Password", "forgotPassword": "Forgot Password?", "continue": "Continue", "signIn": "Sign in", "signUp": "Sign up", "or": "OR", "signUpAs": "Sign up as", "google": "Continue with Google", "apple": "Continue with Apple", "enterOtpSent": "Enter OTP sent to your registered email address", "resendOtp": "Resend OTP", "otpLabel": "OTP", "otpPlaceholder": "Enter OTP", "otpVerifiedSuccess": "OTP verified successfully!", "otpVerificationFailed": "Verification failed. No authentication token received.", "authenticationSuccessRedirect": "Authentication successful! Redirecting...", "otpResent": "OTP has been resent to your email", "otpInputPlaceholder": "6-digit OTP", "otpInstruction": "Enter OTP sent to your registered email address", "otpResentSuccess": "OTP resent successfully!", "sending": "Sending...", "verifying": "Verifying...", "missingEmailParam": "Missing email parameter. Please go back to the sign-up page.", "redirectingToProfile": "Redirecting to your profile...", "profileSetupForm": {"addProfilePicture": "+ Add Profile Picture", "introductoryStatement": "Introductory Statement", "introductoryStatementPlaceholder": "Write a couple of lines to introduce yourself to others in the community. If you would like to - this is entirely optional!", "loadingText": "Loading...", "saveAndContinue": "Save and Continue", "addPhotoLabel": "+Add Photo", "changePhotoLabel": "Change Photo", "removePhotoLabel": "Remove Photo", "characters": "characters"}, "loading": "Loading...", "genericErrorMessage": "Something went wrong. Please try again later."}, "signUpPublicFlow": {"subtitlePublicFlow": "To get you started, let's set up your public profile!"}, "feedPage": {"feed": "Feed", "minicardiac": "MiniCardiac"}, "feedNavigation": {"tag": "Tag", "tags": "Tags", "follow": "Follow", "following": "Following", "savedPosts": "Saved Posts", "addFolder": "Add Folder", "search": "Search", "filter": "Filter", "searchPlaceholder": "Search by keyword, poster, or tag", "voiceSearch": "Voice Search", "profile": "Profile", "myAccount": "My Account", "logout": "Logout", "followed": "Followed", "suggested": "Suggested", "browseAllTags": "Browse all tags", "yourFolders": "Your folders", "noFolders": "No folders added yet.", "clickToGetStarted": "Click below to get started!", "postTypes": {"text": "Text posts", "poll": "Poll posts", "media": "Media posts", "article": "Article posts", "question": "Question posts"}}, "tagsPage": {"yourTags": "Your Tags", "suggestedTags": "Suggested", "seeAll": "See all", "followedTag": "Followed {tag}", "tags": "Tags", "minicardiac": "MiniCardiac", "suggested": "Suggested", "followed": "Followed", "browseAllTags": "Browse all tags", "loading": "Loading..."}, "comment": {"showMore": "Show more", "like": "Like", "reply": "Reply", "pinAnswer": "<PERSON>n as <PERSON>", "noComments": "No comments yet."}, "questionPost": {"questionPost": "Question Post", "minicardiac": "MiniCardiac", "like": "Like", "comment": "Comment", "repost": "Repost", "share": "Share", "noComments": "No comments yet.", "noAnswerTitle": "No Answer Yet", "noAnswerSubtitle": "Share your ideas!", "loadingQuestion": "Loading question...", "title": "New Question", "drafts": "Drafts", "editorLabel": "Your Question", "editorPlaceholder": "Curious about something? Ask your peers by writing down your question here.", "tagsLabel": "Tags", "tagsPlaceholder": "#Surgery #Valve", "community": "Community", "audience": "Audience", "cancel": "Cancel", "featuredAnswer": "Featured Answer", "unpin": "Unpin", "unpinSuccess": "Comment unpinned successfully", "unpinError": "Failed to unpin the comment", "menu": {"schedule": "Schedule", "draft": "Save Draft", "sponsor": "Add to Sponsorship Queue"}, "toast": {"posted": "Posted"}}, "pollPost": {"pollPost": "Poll Post", "minicardiac": "MiniCardiac", "loadingPoll": "Loading poll...", "seeMore": "See more", "selectCustomAnswers": "Select from Custom Answers", "pollTitle": "What is your opinion on the new update?", "retractVote": "Retract Vote", "customAnswerPlaceholder": "Do you have another suggestion? Type it here!", "submit": "Submit", "newPollPost": "New Poll Post", "drafts": "Drafts", "createPoll": "Create Poll", "addDetails": "Add Details", "cancel": "Cancel", "next": "Next", "schedule": "Schedule", "saveDraft": "Save Draft", "sponsorshipQueue": "Add to Sponsorship Queue", "pollPosted": "Poll Posted", "caption": "Caption", "captionPlaceholder": "Curious to see how clinical practice is evolving—thanks for participating!", "tags": "Tags", "tagsPlaceholder": "#Surgery #Research", "community": "Community", "audience": "Audience", "questionLabel": "Poll Question", "questionPlaceholder": "Got a question to put to your audience? Ask it here.", "option": "Option", "addOption": "Add another option", "customAnswer": "Allow custom answers"}, "articlePost": {"articlePost": "Article Post", "minicardiac": "MiniCardiac", "loading": "Loading article...", "readFull": "Read the full article", "newArticlePost": "New Article Post", "drafts": "Drafts", "stepCompose": "Compose Article", "stepDetails": "Add Details", "editorLabel": "Your article", "editorPlaceholder": "Write your content here!", "cancel": "Cancel", "next": "Next", "schedule": "Schedule", "saveDraft": "Save Draft", "addToSponsorship": "Add to Sponsorship Queue", "articlePosted": "Article Posted", "tapToSelectThumbnail": "Tap to select thumbnail", "clickToAddThumbnail": "Click to add thumbnail", "dragAndDrop": "Or drag and drop", "title": "Title", "titlePlaceholder": "Add a title for your article", "summary": "Summary", "summaryPlaceholder": "Add a summary for your article!", "tags": "Tags", "community": "Community", "audience": "Audience"}, "mediaPost": {"mediaPost": "Media Post", "minicardiac": "MiniCardiac", "loading": "Loading media post...", "readMore": "Read more", "comments": "Comments", "like": "Like", "repost": "Repost", "share": "Share", "details": "Details", "commentPlaceholder": "What do you think of this post?", "seeMore": "See more", "loadingMessage": "Loading media post...", "newMediaPost": "New Media Post", "drafts": "Drafts", "addMedia": "Add Media", "addDetails": "Add Details", "tags": "Tags", "tagsPlaceholder": "#Surgery #Valve", "community": "Community", "audience": "Audience", "cancel": "Cancel", "next": "Next", "schedule": "Schedule", "saveDraft": "Save Draft", "addToSponsorship": "Add to Sponsorship Queue", "posted": "Posted", "clickToAddMedia": "Click to add media", "orDragAndDrop": "Or drag and drop", "reorderHint": "Click and drag images to re-order them", "caption": "Caption", "captionPlaceholder": "Add a caption for your post!"}, "schedulePost": {"title": "Schedule Post", "return": "Return to Post", "schedule": "Schedule", "toast": {"scheduled": "Post Scheduled"}}, "savePost": {"title": "Save Post", "folderLabel": "Folder Name", "folderPlaceholder": "New folder name", "cancel": "Cancel", "save": "Save", "addFolder": "Add Folder", "savedPost": "Saved Post", "minicardiac": "MiniCardiac"}, "newFolderDialog": {"title": "Add New Folder", "folderLabel": "Folder Name", "folderPlaceholder": "The name of your new collection", "cancel": "Cancel", "save": "Save"}, "allProfilesDialog": {"title": "All Profiles", "cancel": "Cancel", "switch": "Switch"}, "articlePostDetails": {"title": "Title", "titlePlaceholder": "Add a title for your article!", "summary": "Summary", "summaryPlaceholder": "Add a summary for your article!", "clickToAddThumbnail": "Click to add thumbnail", "dragAndDrop": "Or drag and drop", "tags": "Tags", "community": "Audience", "audience": "Professional Community"}, "headers": {"minicardiac": "MiniCardiac"}}