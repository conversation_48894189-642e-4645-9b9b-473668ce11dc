import { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography, useTheme } from '@mui/material';

interface ConfirmationDialogProps {
  open: boolean;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm: () => void;
  onCancel: () => void;
}

export function ConfirmationDialog({
  open,
  title,
  message,
  confirmText = 'Delete',
  cancelText = 'Cancel',
  onConfirm,
  onCancel,
}: ConfirmationDialogProps) {
  const theme = useTheme();

  return (
    <Dialog
      open={open}
      onClose={onCancel}
      PaperProps={{
        sx: {
          width: '100%',
          maxWidth: '400px',
          borderRadius: '12px',
          padding: '24px',
        },
      }}
    >
      <DialogTitle sx={{ p: 0, mb: 2, fontSize: '1.25rem', fontWeight: 600 }}>
        {title}
      </DialogTitle>

      <DialogContent sx={{ p: 0, mb: 3 }}>
        <Typography variant="body1">{message}</Typography>
      </DialogContent>

      <DialogActions sx={{ p: 0, gap: theme.spacing(1.5) }}>
        <Button
          onClick={onCancel}
          variant="outlined"
          sx={{
            flex: 1,
            height: '40px',
            borderRadius: '8px',
            borderColor: theme.palette.grey[300],
            color: theme.palette.text.secondary,
            textTransform: 'none',
            fontWeight: 600,
            '&:hover': {
              backgroundColor: theme.palette.grey[100],
              borderColor: theme.palette.grey[300],
            },
          }}
        >
          {cancelText}
        </Button>
        <Button
          onClick={onConfirm}
          variant="contained"
          sx={{
            flex: 1,
            height: '40px',
            borderRadius: '8px',
            backgroundColor: theme.palette.primary.main,
            color: theme.palette.primary.contrastText,
            textTransform: 'none',
            fontWeight: 600,
            '&:hover': {
              backgroundColor: theme.palette.primary.dark,
            },
          }}
        >
          {confirmText}
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default ConfirmationDialog;
