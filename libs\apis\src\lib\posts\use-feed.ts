import { useQuery } from '@tanstack/react-query';
import { postApi } from './post-api.js';
import { FeedPostType } from '@minicardiac-client/types';

// API response type (what comes from backend)
interface ApiPostType extends Omit<FeedPostType, 'linkUrl'> {
  link?: string;
  caption?: string;
}

export interface FeedResponse {
  data: ApiPostType[];
  total: number;
  limit: number;
  offset: number;
}

interface FeedQueryOptions {
  postTypes?: string[];
  limit?: number;
  offset?: number;
  searchKeyword?: string;
  tagName?: string;
}

const allowedPostTypes = [
  'question',
  'poll',
  'article',
  'media',
  'text',
  'link',
] as const;
type AllowedPostType = (typeof allowedPostTypes)[number];

export function useFeed(options: FeedQueryOptions = {}) {
  const {
    postTypes = ['text', 'question', 'article', 'media', 'link'], // "link" included by default
    limit = 25,
    offset = 0,
    searchKeyword = '',
    tagName = '',
  } = options;

  // Merge tagName into searchKeyword if provided
  // Add foldername in future here if needed
  const mergedSearchKeyword = tagName
    ? `${searchKeyword} ${tagName}`.trim()
    : searchKeyword;

  return useQuery<
    FeedResponse,
    Error,
    { data: FeedPostType[]; total: number; limit: number; offset: number }
  >({
    queryKey: ['feed', { postTypes, limit, offset, searchKeyword, tagName }],
    queryFn: async () => {
      const response = await postApi.getFeed({
        postTypes,
        limit,
        offset,
        searchKeyword: mergedSearchKeyword,
      });
      console.log('Feed Response:', response);
      return response as unknown as FeedResponse;
    },
    select: (data: FeedResponse) => {
      const processedData = {
        ...data,
        data: data.data.map((post: ApiPostType): FeedPostType => {
          const isLinkPost = post.postType === 'link';
          return {
            ...post,
            isLiked: post.isLiked || false,
            likesCount: post.likesCount || 0,
            commentsCount: post.commentsCount || 0,
            shareCount: post.shareCount || 0,
            repostCount: post.repostCount || 0,
            publisherName: post.publisherName || post.username || 'Anonymous',
            title: isLinkPost ? post.link || '' : post.title || '',
            content: post.content || '',
            // Map link-specific fields
            linkUrl: isLinkPost ? post.link || '' : undefined,
            linkPreview:
              isLinkPost && post.link
                ? {
                    title: (() => {
                      try {
                        const url = new URL(post.link);
                        return url.hostname.replace('www.', '');
                      } catch {
                        return post.link || 'Link';
                      }
                    })(),
                    description: post.content || 'Shared link',
                    image: '', // No image from API yet
                    domain: (() => {
                      try {
                        return new URL(post.link).hostname;
                      } catch {
                        return post.link || '';
                      }
                    })(),
                  }
                : undefined,
          };
        }),
      };
      return processedData;
    },
  });
}

// Hook to fetch a single post by ID
export function usePostById(postId?: string) {
  return useQuery<FeedPostType>({
    queryKey: ['post', postId],
    queryFn: async () => {
      if (!postId) throw new Error('Post ID is required');

      const response = await postApi.getPostById(postId);

      const validatedPostType: AllowedPostType = allowedPostTypes.includes(
        response.postType
      )
        ? response.postType
        : (() => {
            console.warn(
              `Unexpected post type: ${response.postType}, defaulting to 'text'`
            );
            return 'text';
          })();

      return {
        ...response,
        postType: validatedPostType,
        isLiked: response.isLiked ?? false,
        likesCount: response.likesCount ?? 0,
        commentsCount: response.commentsCount ?? 0,
        repostCount: response.repostCount ?? 0,
        shareCount: response.shareCount ?? 0,
        publisherName:
          response.publisherName || response.username || 'Anonymous',
        title: response.title ?? '',
      };
    },
    enabled: !!postId,
  });
}
