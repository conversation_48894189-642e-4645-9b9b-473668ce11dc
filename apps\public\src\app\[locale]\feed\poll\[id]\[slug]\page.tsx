'use client';

import { useParams } from 'next/navigation';

import { FullPageLoader } from '@minicardiac-client/components';
import { useAuth } from '@minicardiac-client/apis';

import PostDetailLayout from '@/libs/components/src/lib/content-posts/detail-post/PostDetailLayout';
import { otherArticles } from '@/libs/components/src/lib/article-post/mockData';
import { PollCaption } from '@/libs/components/src/lib/poll-post/PollCaption';
import MainPollContent from '@/libs/components/src/lib/poll-post/MainPollContent';
import { usePostById } from '@/libs/apis/src/lib/posts/use-feed';
import Head from '@/libs/components/src/lib/head/Head';
// import Head from '@/libs/components/src/lib/head/Head';

export const POLL_CAPTION = `With the growing body of evidence supporting the use of SGLT2 inhibitors in HFpEF patients, are you now routinely prescribing them as part of your initial management plan?`;

export default function PollPostPageWrapper() {
  const { authState } = useAuth();
  const params = useParams();

  const postId = params?.id as string;

  const { data: post, isLoading } = usePostById(postId);

  if (authState.isLoading || isLoading) {
    return <FullPageLoader open={true} />;
  }

  if (!post) {
    return (
      <div style={{ padding: '2rem', textAlign: 'center', fontSize: '1.2rem' }}>
        Invalid post ID. No post found.
      </div>
    );
  }

  return (
    <>
      <Head title={post.title ?? post.content} />

      <PostDetailLayout
        user={{
          name: post.publisherName || '',
          profilePic: post.profileImageUrlThumbnail || '',
          postedAgo: post.postedAt || '',
        }}
        sidebarItems={otherArticles}
        sidebarTitle="Other Posts"
        postId={post.id ?? ''}
        post={post}
      >
        <PollCaption POLL_CAPTION={post.content} postId={postId} />
        <MainPollContent
          question={post?.question}
          options={post?.options}
          allowCustomAnswer={post?.allowCustomAnswer || false}
          postId={postId}
          totalVotes={post?.totalVotes || 0}
          expiresAt={post?.expiresAt || ''}
        />
      </PostDetailLayout>
    </>
  );
}
