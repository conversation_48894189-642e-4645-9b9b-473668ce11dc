'use client';

import { useParams } from 'next/navigation';

import { FullPageLoader } from '@minicardiac-client/components';
import { useAuth } from '@minicardiac-client/apis';

import PostDetailLayout from '@/libs/components/src/lib/content-posts/detail-post/PostDetailLayout';
import { otherArticles } from '@/libs/components/src/lib/article-post/mockData';
import ArticleContent from '@/libs/components/src/lib/article-post/ArticleContent';
import ArticleCover from '@/libs/components/src/lib/article-post/ArticleCover';
import { usePostById } from '@/libs/apis/src/lib/posts/use-feed';
import Head from '@/libs/components/src/lib/head/Head';

export default function ArticlePageWrapper() {
  const { authState } = useAuth();
  const params = useParams();

  const articleId = params?.id as string;

  const { data: post, isLoading } = usePostById(articleId);

  if (authState.isLoading || isLoading) {
    return <FullPageLoader open={true} />;
  }

  if (!post) {
    return (
      <div style={{ padding: '2rem', textAlign: 'center', fontSize: '1.2rem' }}>
        Invalid post ID. No post found.
      </div>
    );
  }

  return (
    <>
      <Head title={post.title || post.content} />

      <PostDetailLayout
        user={{
          name: post.publisherName || '',
          profilePic: post.profileImageUrlThumbnail || '',
          postedAgo: post.postedAt || '',
        }}
        sidebarItems={otherArticles}
        sidebarTitle="Other Articles"
        postId={articleId}
        post={post}
      >
        <ArticleCover
          thumbnail={post.profileImageUrlThumbnail ?? ''}
          articleTitle={post.title ?? ''}
        />
        <ArticleContent content={post.content ?? ''} />
      </PostDetailLayout>
    </>
  );
}
